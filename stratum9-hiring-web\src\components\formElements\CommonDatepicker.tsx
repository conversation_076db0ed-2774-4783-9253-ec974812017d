/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { Control, Controller, FieldError, FieldErrors, FieldValues } from "react-hook-form";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface IDateRange {
  start: Date;
  end: Date;
}

interface IProps {
  control: Control<any>;
  name: string;
  error?: FieldError | FieldErrors<FieldValues>;
  className?: string;
  label?: string;
  dateFormat?: string;
  onClick?: () => void;
  onBlur?: () => void;
  dateRange: IDateRange[];
  defaultDate?: Date;
  placeholder?: string;
  isClearable?: boolean;
  icon?: string | undefined;
  required?: boolean;
}

const CommonDatePickerWrapper: React.FC<IProps> = ({
  control,
  name,
  label,
  dateRange,
  defaultDate,
  placeholder,
  error,
  required,
  onBlur,
  ...datePickerProps
}) => {
  return (
    <div className={"form-group common-datepicker-wrapper"}>
      <label>
        {label && label} {required ? <sup>*</sup> : null}
      </label>
      <div className="form-control">
        <Controller
          name={name}
          control={control}
          defaultValue={defaultDate && defaultDate}
          render={({ field }) => (
            <DatePicker
              showIcon
              disabledKeyboardNavigation
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" className="calendar-icon">
                  <path d="M152 24c0-13.3-10.7-24-24-24s-24 10.7-24 24V64H64C28.7 64 0 92.7 0 128v16 48V448c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V192 144 128c0-35.3-28.7-64-64-64H344V24c0-13.3-10.7-24-24-24s-24 10.7-24 24V64H152V24zM48 192H400V448c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V192z" />
                </svg>
              }
              onFocus={(e) => e.target.blur()}
              onBlur={onBlur}
              selected={field.value}
              className={"w-100 datepicker-input"}
              onChange={(date) => {
                // Format date as YYYY-MM-DD
                if (date) {
                  const year = date.getFullYear();
                  const month = String(date.getMonth() + 1).padStart(2, "0");
                  const day = String(date.getDate()).padStart(2, "0");
                  const formattedDate = `${year}-${month}-${day}`;

                  // Update the form with formatted string instead of Date object
                  field.onChange(formattedDate);
                } else {
                  field.onChange(date);
                }
              }}
              excludeDateIntervals={dateRange}
              minDate={new Date()}
              placeholderText={placeholder}
              {...datePickerProps}
            />
          )}
        />
      </div>
      {error && <p className="auth-msg error m-0">{error?.message as string}</p>}
    </div>
  );
};

export default CommonDatePickerWrapper;
