import dbConnection from "../dbConnection";
import InterviewModel from "../schema/interview";
import { AGORA_INTERVIEW_RECORDING_URL } from "../utils/constants";
import {
  mapDeletableS3Files,
  deleteS3FolderFilesWithObjects,
} from "../utils/helper";

export const AgoraAdditionalFilesDeleteLambda = async () => {
  try {
    const interviewRepo = await dbConnection.getRepository(InterviewModel);

    // Fetch interviews that ended and haven't had their additional files deleted
    const interviews = await interviewRepo
      .createQueryBuilder("interview")
      .select(["interview.id", "interview.jobId", "interview.jobApplicationId"])
      .where("interview.isEnded = :isEnded", { isEnded: true })
      .andWhere("interview.agoraAdditionalFilesDeleted = :deleted", {
        deleted: false,
      })
      .getMany();
    console.log("interviews===>>", interviews);

    if (!interviews.length) {
      console.log("No interviews found for cleanup.");
      return {
        success: true,
        code: 200,
        message: "No files to delete.",
        deletedInterviews: [],
      };
    }

    const deletedInterviewIds: number[] = [];

    const objectsToDelete = [];

    for (const interview of interviews) {
      const folderKey = AGORA_INTERVIEW_RECORDING_URL.replace(
        ":jobId",
        interview.jobId.toString()
      )
        .replace(":jobApplicationId", interview.jobApplicationId.toString())
        .replace(":interviewId", interview.id.toString());

      try {
        const deleteAbleFiles = await mapDeletableS3Files(folderKey);
        if (deleteAbleFiles.length > 0) {
          objectsToDelete.push(...deleteAbleFiles);
          deletedInterviewIds.push(interview.id);
        }
      } catch (err) {
        console.error(
          `Failed to delete files for interview ID: ${interview.id}`,
          err
        );
        // continue to next interview
      }
    }
    console.log("Objects to delete:", objectsToDelete);

    try {
      const deleteResult = await deleteS3FolderFilesWithObjects(
        objectsToDelete
      );
      // Update DB only if there were successful deletions
      if (deleteResult.length > 0) {
        const updateResult = await interviewRepo
          .createQueryBuilder()
          .update(InterviewModel)
          .set({ agoraAdditionalFilesDeleted: true })
          .whereInIds(deletedInterviewIds)
          .execute();

        console.log(
          `Marked ${updateResult.affected} interviews as cleaned up.`
        );
      }
    } catch (error) {
      console.error("Error updating interview records:", error);
      return {
        success: false,
        code: 500,
        message: "Failed to update interview records.",
      };
    }

    return {
      success: true,
      code: 200,
      message: "Cleanup complete.",
      deletedInterviews: deletedInterviewIds,
    };
  } catch (error) {
    console.error("AgoraAdditionalFilesDelete error:", error);
    return {
      success: false,
      code: 500,
      message: "Something went wrong while cleaning up files.",
    };
  }
};
