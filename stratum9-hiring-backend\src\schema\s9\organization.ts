import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column } from "typeorm";

export interface IOrganizationSchema {
  id: number;
  name: string;
  contact_person: string;
  organization_code: string;
  email: string;
  domain: string;
  users: number;
  location: string;
  assessment_categories: string;
  created_ts: Date;
  updated_ts: Date;
}

@Entity("organization")
class OrganizationModel implements IOrganizationSchema {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    length: 150,
  })
  name: string;

  @Column({
    length: 2000,
  })
  contact_person: string;

  @Column({
    length: 255,
  })
  email: string;

  @Column({ collation: "utf8mb4_bin" }) // Set the collation to case-sensitive
  organization_code: string;

  @Column()
  users: number;

  @Column({
    length: 255,
  })
  domain: string;

  @Column({
    length: 255,
  })
  location: string;

  @Column("text")
  assessment_categories: string;

  @Column({
    name: "stripe_customer_id",
    type: "varchar",
    length: 50,
    nullable: false,
  })
  stripeCustomerId: string;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  created_ts: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  updated_ts: Date;
}

export default OrganizationModel;
