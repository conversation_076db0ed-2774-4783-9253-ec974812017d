import React from "react";

type NextArrowIcon = {
  onClick?: React.MouseEventHandler<SVGSVGElement>;
  className?: string;
};

function NextArrowIcon({ onClick, className }: NextArrowIcon) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} onClick={onClick} width="17" height="15" viewBox="0 0 17 15" fill="none">
      <path d="M15.75 7.72461L0.75 7.72461" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M9.69922 1.701L15.7492 7.725L9.69922 13.75" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  );
}

export default NextArrowIcon;
