import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";
import { S3Client } from "@aws-sdk/client-s3";
import envConfig from "./envConfig";
import { ISecretKeys } from "../utils/constants";

const CONFIG = envConfig();

// Create client config with region
interface ClientConfig {
  region: string;
  credentials?: any;
}

const clientConfig: ClientConfig = {
  region: CONFIG.region,
};

let secretKeys: ISecretKeys;
export const getSecretKeys = async (hardLoad = false): Promise<ISecretKeys> => {
  if (!secretKeys || hardLoad) {
    try {
      const client = new SecretsManagerClient({
        ...clientConfig,
        maxAttempts: 3,
      });
      const command = new GetSecretValueCommand({
        SecretId: CONFIG.secretManagerKey,
      });

      const response = await client.send(command);

      if (response.SecretString) {
        secretKeys = JSON.parse(response.SecretString);
        return secretKeys;
      }
    } catch (err) {
      console.log("Error fetching secret keys:", err);

      getSecretKeys(hardLoad);
    }
  }
  return secretKeys;
};

const s3Client = new S3Client(clientConfig);
export default s3Client;
