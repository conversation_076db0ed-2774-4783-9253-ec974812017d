import { DataSource, EntityTarget } from "typeorm";
import { s9InnerviewDbConnectionDetails, s9MainDbConnectionDetails } from "./config/databaseConfig";

class DbConnection {
  connectionInfo: Promise<DataSource> | undefined;

  s9MainConnection: Promise<DataSource> | undefined

  /**
   * Get database connection.
   *
   */
  async getDataSource(): Promise<DataSource> {
    return new Promise((resolve) => {
      if (!this.connectionInfo) {
        s9InnerviewDbConnectionDetails().then((connection: Promise<DataSource>) => {
          this.s9MainConnection = connection;
          console.log("Database connection established successfully.");
          resolve(this.s9MainConnection);
        });
      } else {
        resolve(this.s9MainConnection);
      }
    });
  }

  async getS9MainDataSource(): Promise<DataSource> {
    return new Promise((resolve) => {
      if (!this.connectionInfo) {
        s9MainDbConnectionDetails().then((connection: Promise<DataSource>) => {
          this.connectionInfo = connection;
          console.log("Database connection established successfully.");
          resolve(this.connectionInfo);
        });
      } else {
        resolve(this.connectionInfo);
      }
    });
  }

  public async getS9MainDatabaseRepository<T>(model: EntityTarget<T>) {
    const dbConnection = await this.getS9MainDataSource();
    const repository = dbConnection.getRepository(model);
    return repository;
  }

  public async getRepository<T>(model: EntityTarget<T>) {
    const dbConnection = await this.getDataSource();
    const repository = dbConnection.getRepository(model);
    return repository;
  }
}

export default new DbConnection();
