const MicIcon = (props: { className?: string; isMicMute?: boolean }) => {
  const { className, isMicMute } = props;
  return (
    <div className={className}>
      {isMicMute ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="29" height="28" viewBox="0 0 29 28" fill="none">
          <path
            d="M22.834 11.0801C23.4781 11.0803 24 11.6029 24 12.2471V16.3301C24 19.5517 21.3886 22.164 18.167 22.1641H15.834V25.6641C15.8338 26.3082 15.3111 26.83 14.667 26.8301C14.0228 26.8301 13.5002 26.3082 13.5 25.6641V22.1641H11.167C9.62464 22.1641 8.22271 21.5646 7.17969 20.5869L8.83105 18.9355C9.45063 19.4914 10.2691 19.8301 11.167 19.8301H18.167C20.0999 19.83 21.667 18.263 21.667 16.3301V12.2471C21.667 11.6027 22.1897 11.0801 22.834 11.0801ZM14.667 1.16406C16.7255 1.16413 18.4719 2.49696 19.0918 4.34668L21.4697 1.96973C21.7626 1.67691 22.2374 1.67686 22.5303 1.96973C22.8231 2.2626 22.8231 2.7374 22.5303 3.03027L2.53027 23.0303C2.2374 23.3231 1.76263 23.3231 1.46973 23.0303C1.19528 22.7558 1.1781 22.3219 1.41797 22.0273L1.46973 21.9697L5.54688 17.8916C5.40878 17.3945 5.33398 16.8711 5.33398 16.3301V12.2471C5.33398 11.6028 5.85582 11.0803 6.5 11.0801C7.14426 11.0802 7.66699 11.6028 7.66699 12.2471V15.7725L10 13.4395V5.83008C10.0002 3.2529 12.0898 1.16406 14.667 1.16406ZM19.334 15.1641C19.3338 16.4525 18.2885 17.497 17 17.4971H12.334C11.7583 17.4971 11.2323 17.287 10.8252 16.9414L19.334 8.43359V15.1641Z"
            fill="white"
          />
        </svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29 28" fill="none">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.99967 15.168C9.99967 16.4566 11.0443 17.5013 12.333 17.5013H16.9997C18.2883 17.5013 19.333 16.4566 19.333 15.168V5.83464C19.333 3.25731 17.2437 1.16797 14.6663 1.16797C12.089 1.16797 9.99967 3.25731 9.99967 5.83464V15.168ZM6.49967 11.0846C7.14401 11.0846 7.66634 11.607 7.66634 12.2513V16.3346C7.66634 18.2676 9.23334 19.8346 11.1663 19.8346H14.6663H18.1663C20.0993 19.8346 21.6663 18.2676 21.6663 16.3346V12.2513C21.6663 11.607 22.1887 11.0846 22.833 11.0846C23.4773 11.0846 23.9997 11.607 23.9997 12.2513V16.3346C23.9997 19.5563 21.388 22.168 18.1663 22.168H15.833V25.668C15.833 26.3123 15.3107 26.8346 14.6663 26.8346C14.022 26.8346 13.4997 26.3123 13.4997 25.668V22.168H11.1663C7.94468 22.168 5.33301 19.5563 5.33301 16.3346V12.2513C5.33301 11.607 5.85534 11.0846 6.49967 11.0846Z"
            fill="white"
          />
        </svg>
      )}
    </div>
  );
};

export default MicIcon;
