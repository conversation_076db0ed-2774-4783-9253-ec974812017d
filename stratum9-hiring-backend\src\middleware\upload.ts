import multer from "multer";
import { PDF_CONTENT_TYPE, MAX_FILE_SIZE } from "../utils/constants";

const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: MAX_FILE_SIZE, // 5MB max file size
  },
  fileFilter: (req, file, cb) => {
    // Accept only PDF files (case-insensitive check)
    if (file.mimetype.toLowerCase() === PDF_CONTENT_TYPE.toLowerCase()) {
      cb(null, true);
    } else {
      cb(new Error(`Only PDF files are allowed. Received: ${file.mimetype}`));
    }
  },
});

export default upload;
