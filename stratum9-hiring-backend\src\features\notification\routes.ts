import express from "express";
import auth from "../../middleware/auth";
import {
  getNotificationsController,
  markAsWatchedController,
  deleteUsersAllNotificationsController,
  getUnreadNotificationsCountController,
} from "./controllers";
import HandleErrors from "../../middleware/handleError";
import { ROUTES } from "../../utils/constants";

const notificationRoutes = express.Router();

/**
 * @swagger
 * /api/v1/notifications:
 *   get:
 *     tags: [Notifications]
 *     summary: Get notifications with optional filters
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: ["Interview Scheduled", "Interview Feedback Pending", "Job Post Archived", "Candidate Promoted", "Candidate Hired/Unhired", "Final Assessment Submitted", "Subscription Updated"]
 *       - in: query
 *         name: isWatched
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: offset
 *         schema:
 *           type: number
 *           default: 0
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           default: 10
 *     responses:
 *       200:
 *         description: List of notifications
 */
notificationRoutes.get(
  ROUTES.NOTIFICATIONS.GET_NOTIFICATIONS,
  auth,
  HandleErrors(getNotificationsController)
);

/**
 * @swagger
 * /api/v1/notifications/{id}/mark-watched:
 *   patch:
 *     tags: [Notifications]
 *     summary: Mark a notification as watched
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: number
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Notification marked as watched
 *       404:
 *         description: Notification not found or already marked as watched
 */
notificationRoutes.post(
  ROUTES.NOTIFICATIONS.MARK_AS_WATCHED,
  auth,
  HandleErrors(markAsWatchedController)
);

notificationRoutes.delete(
  ROUTES.NOTIFICATIONS.DELETE_USERS_ALL_NOTIFICATIONS,
  auth,
  HandleErrors(deleteUsersAllNotificationsController)
);

notificationRoutes.get(
  ROUTES.NOTIFICATIONS.GET_UNREAD_NOTIFICATIONS_COUNT,
  auth,
  HandleErrors(getUnreadNotificationsCountController)
);

export default notificationRoutes;
