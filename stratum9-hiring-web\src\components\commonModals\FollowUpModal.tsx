"use client";
import React from "react";
import AiMarkIcon from "../svgComponents/AiMarkIcon";
import Button from "../formElements/Button";
import { FOLLOW_UP_TYPE } from "@/constants/commonConstants";

const FollowUpModal = ({
  question,
  type,
  onProceed,
  onCancel,
  loading,
}: {
  question: string;
  type: string;
  onProceed: () => void;
  onCancel: () => void;
  loading: boolean;
}) => {
  return (
    <div className="follow-up-modal">
      <div className="follow-up-container">
        <p className="sub-title">
          <AiMarkIcon className="p-1 ps-0" />
          Follow-Up Question Generated Via AI
        </p>
        <h4>{question}</h4>
        {/* this is follow up question start */}
        {type === FOLLOW_UP_TYPE.SKILL ? (
          <div className="question-group">
            <p className="question-label">Pick A Question</p>
            <label className="radio-item">
              <input type="radio" name="boldness" />
              <span className="radio-text">
                Can you share a specific instance where boldness helped you make a tough decision or take a calculated risk
              </span>
            </label>
            <label className="radio-item">
              <input type="radio" name="boldness" />
              <span className="radio-text">
                How do you balance boldness with strategic thinking to ensure positive outcomes in a professional setting?
              </span>
            </label>
          </div>
        ) : null}
        {/* this is follow up question end */}
        <div className="action-btn">
          <Button className="primary-btn w-100" onClick={onProceed} disabled={loading} loading={loading}>
            Proceed
          </Button>
          <Button className="dark-outline-btn w-100" onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FollowUpModal;
