"use client";
import React, { useEffect, useRef } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";

const SkillDetails = ({
  skillDetails,
  onClose,
}: {
  skillDetails: { title: string; description: string; short_description: string };
  onClose: () => void;
}) => {
  const modalContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalContentRef.current && !modalContentRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    // Add event listener when the component mounts
    document.addEventListener("mousedown", handleClickOutside);

    // Clean up the event listener when the component unmounts
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content" ref={modalContentRef}>
          <div className="modal-header p-0">
            <Button className="modal-close-btn" onClick={onClose}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body">
            <h3 className="text-center mb-3">
              <strong>{skillDetails.title}</strong>
            </h3>
            <p className="text-center">{skillDetails.description || skillDetails.short_description}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkillDetails;
