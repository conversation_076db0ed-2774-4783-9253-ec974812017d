import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  generatePresignedUrlValidation,
  removeAttachmentsFromS3Validation,
} from "./vaildation";
import { schemaValidation } from "../../middleware/validateSchema";
import { generatePresignedUrl, removeAttachmentsFromS3 } from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";

const commonRoutes = express.Router();

commonRoutes.post(
  ROUTES.COMMON.GENERATE_PRE_SIGNED_URL,
  auth,
  schemaValidation(generatePresignedUrlValidation),
  HandleErrors(generatePresignedUrl)
);

commonRoutes.post(
  ROUTES.COMMON.REMOVE_ATTACHMENTS_FROM_S3,
  auth,
  schemaValidation(removeAttachmentsFromS3Validation),
  HandleErrors(removeAttachmentsFromS3)
);

export default commonRoutes;
