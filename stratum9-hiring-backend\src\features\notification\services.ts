import * as Sentry from "@sentry/node";
import dbConnection from "../../db/dbConnection";
import NotificationsModal from "../../schema/s9-innerview/notifications";
import { API_RESPONSE_MSG } from "../../utils/constants";
import { GetNotificationsParams, ICreateNotification } from "./interface";

class NotificationServices {
  static getNotifications = async (
    orgId: number,
    userId: number,
    data: GetNotificationsParams
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const notificationsRepo = dataSource.getRepository(NotificationsModal);

      const notifications = await notificationsRepo
        .createQueryBuilder("notification")
        .where("notification.organizationId = :orgId", { orgId })
        .andWhere("notification.userId = :userId", { userId })
        .select([
          "notification.id as id",
          "notification.type as type",
          "notification.title as title",
          "notification.description as description",
          "notification.relatedId as relatedId",
          "notification.additionalInfo as additionalInfo",
          "notification.isWatched as isWatched",
          "notification.createdTs as createdTs",
          "notification.updatedTs as updatedTs",
        ])
        .orderBy("notification.createdTs", "DESC")
        .offset(data.offset)
        .limit(data.limit)
        .getRawMany();

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: notifications,
      };
    } catch (error) {
      console.log("error ===>>> ", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static createNotification = async (
    orgId: number,
    userId: number,
    data: ICreateNotification
  ) => {
    const { type, title, description, relatedId, additionalInfo } = data;
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const notificationsRepo = dataSource.getRepository(NotificationsModal);
      const notification = new NotificationsModal();

      notification.organizationId = orgId;
      notification.userId = userId;
      notification.type = type;
      notification.title = title;
      notification.description = description;
      notification.relatedId = relatedId;
      notification.additionalInfo = additionalInfo;
      notification.isWatched = false;

      await notificationsRepo.save(notification);

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.error("createNotification error ===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static markAsWatched = async (orgId: number, userId: number) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const notificationsRepo = dataSource.getRepository(NotificationsModal);

      const result = await notificationsRepo
        .createQueryBuilder()
        .update(NotificationsModal)
        .set({ isWatched: true })
        .where("organizationId = :orgId", { orgId })
        .andWhere("userId = :userId", { userId })
        .execute();

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: result,
      };
    } catch (error) {
      Sentry.captureException(error);
      throw error;
    }
  };

  static deleteUsersAllNotifications = async (
    orgId: number,
    userId: number
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const notificationsRepo = dataSource.getRepository(NotificationsModal);

      const result = await notificationsRepo
        .createQueryBuilder()
        .delete()
        .where("organizationId = :orgId", { orgId })
        .andWhere("userId = :userId", { userId })
        .execute();

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: result,
      };
    } catch (error) {
      Sentry.captureException(error);
      throw error;
    }
  };

  static getUnreadNotificationsCount = async (
    orgId: number,
    userId: number
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const notificationsRepo = dataSource.getRepository(NotificationsModal);

      const count = await notificationsRepo
        .createQueryBuilder("notification")
        .where("notification.organizationId = :orgId", { orgId })
        .andWhere("notification.userId = :userId", { userId })
        .andWhere("notification.isWatched = false")
        .getCount();

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: { count },
      };
    } catch (error) {
      Sentry.captureException(error);
      throw error;
    }
  };
}

export default NotificationServices;
