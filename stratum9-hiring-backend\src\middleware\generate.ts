import jwt from "jsonwebtoken";
import { IForgotPassword } from "../features/auth/interface";
import { PLATFORM } from "../utils/constants";

interface ITokenGenerate extends IForgotPassword {
  id: number;
  orgId: number;
  roleId: number;
  departmentId: number;
  type: string;
}

const createToken = async (
  user: ITokenGenerate,
  jwtSecret: string
): Promise<string> => {
  const { id, email, orgId, roleId, departmentId, type } = user;
  const token = jwt.sign(
    {
      id,
      email,
      orgId,
      roleId,
      departmentId,
      type,
      platform: PLATFORM.STRATUM9_INNERVIEW,
    },
    jwtSecret,
    {
      expiresIn: "3d",
    }
  );

  return token;
};

/**
 * Create a JWT token for final assessment
 * @param payload - Token payload containing finalAssessmentId
 * @returns JWT token string
 */
export const createAssessmentToken = async (payload: {
  finalAssessmentId: number;
}): Promise<string> => {
  const { finalAssessmentId } = payload;
  const token = jwt.sign(
    { finalAssessmentId },
    process.env.FINAL_ASSESSMENT_KEY || "final_assessment_key",
    {
      expiresIn: "24h", // 24 hours expiry time
    }
  );

  return token;
};

export default createToken;
