import <PERSON><PERSON><PERSON> from "openai";
import * as Sentry from "@sentry/node";
import {
  IQuestion,
  QuestionFormat,
} from "../features/finalAssessment/interface";
import { getSecretKeys } from "../config/awsConfig";

/**
 * Generate interview questions based on skill information
 * @param skillTitle - The title of the skill
 * @param skillDescription - The description of the skill
 * @param skillType - The type of the skill (career_based, role_specific, culture_specific)
 * @param numQuestions - Number of questions to generate (default: 2)
 * @returns Array of generated questions (MCQ and true/false)
 */
export const generateQuestionsForSkill = async (
  skillTitle: string,
  skillDescription: string,
  skillType: string,
  numQuestions: number = 2
): Promise<IQuestion[]> => {
  const keys = await getSecretKeys();
  // Initialize OpenAI client
  const openai = new OpenAI({
    apiKey: keys.openai_api_key,
  });

  try {
    const prompt = `Generate ${numQuestions} interview questions to assess a candidate's "${skillTitle}" skill, which is described as: "${skillDescription}". 
    This is a ${skillType} skill.
    
    IMPORTANT: Generate a truly random mix of multiple-choice questions and true/false questions. DO NOT follow a pattern of alternating between MCQ and true/false. Some skills might have all MCQs, some might have all true/false, and others might have any combination. The randomness is critical.
    
    For multiple-choice questions:
    - Each question should have 4 options (A, B, C, D)
    - Indicate the correct answer
    
    For true/false questions:
    - Each question should be answerable with true or false
    - Indicate whether the correct answer is true or false
    
    Format the response as a JSON object with a 'questions' array, where each question has:
    
    For MCQ questions:
    - 'type': "mcq"
    - 'question': the question text
    - 'options': array of option objects with 'id' (A, B, C, or D) and 'text' (the option text)
    - 'correctAnswerId': the ID of the correct option (A, B, C, or D)
    
    For true/false questions:
    - 'type': "true_false"
    - 'question': the question text
    - 'options': array of option objects with 'id' (true or false) and 'text' (the option text)
    - 'correctAnswer': boolean (true or false)
    
    Example format: 
    {
      "questions": [
        {
          "type": "mcq",
          "question": "What is the best approach to solve X?",
          "options": [
            { "id": "A", "text": "Option A text" },
            { "id": "B", "text": "Option B text" },
            { "id": "C", "text": "Option C text" },
            { "id": "D", "text": "Option D text" }
          ],
          "correctAnswerId": "B"
        },
        {
          "type": "true_false",
          "question": "Statement about the skill that is true or false",
          "options": [
            { "id": "true", "text": "True" },
            { "id": "false", "text": "False" }
          ],
          "correctAnswer": true
        }
      ]
    }`;

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content:
            "You are an expert interviewer who creates targeted questions to assess specific skills.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 500,
      response_format: { type: "json_object" },
    });

    const content = response.choices[0]?.message?.content || "[]";

    try {
      const parsedContent = JSON.parse(content);
      const questions = parsedContent.questions || [];

      // Validate the structure of each question
      return questions.filter((q: any) => {
        // Check common fields
        if (!q.question || !q.type) return false;

        // Validate based on question type
        if (q.type === QuestionFormat.MCQ) {
          return (
            Array.isArray(q.options) &&
            q.options.length === 4 &&
            q.correctAnswerId
          );
        }

        if (q.type === QuestionFormat.TRUE_FALSE) {
          return typeof q.correctAnswer === "boolean";
        }

        return false;
      });
    } catch (parseError) {
      Sentry.captureException(parseError);
      // Log error to Sentry instead of console
      return [];
    }
  } catch (error) {
    Sentry.captureException(error);
    // Log error to Sentry instead of console
    return [];
  }
};

export default {
  generateQuestionsForSkill,
};
