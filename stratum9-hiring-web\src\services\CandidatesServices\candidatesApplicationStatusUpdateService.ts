// candidatesApplicationServices.ts

import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse } from "@/interfaces/commonInterfaces";
import { UpdateCandidateApplicationStatusResponse } from "@/interfaces/jobApplicationInterface";

/**
 * Archive or unarchive a candidate application
 */
export const archiveActiveApplication = (
  applicationId: number,
  isActive: boolean,
  reason?: string
): Promise<ApiResponse<UpdateCandidateApplicationStatusResponse>> => {
  return http.put(endpoint.candidatesApplication.ARCHIVE_ACTIVE_APPLICATION.replace(":applicationId", applicationId.toString()), {
    isActive,
    reason,
  });
};
