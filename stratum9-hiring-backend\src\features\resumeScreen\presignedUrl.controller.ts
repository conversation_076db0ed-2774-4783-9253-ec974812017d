import { Request, Response } from "express";
import { v4 as uuidv4 } from "uuid";

import { API_RESPONSE_MSG } from "../../utils/constants";
import { gettingPreSignedUrl } from "../../utils/fileUpload";

import envConfig from "../../config/envConfig";
import { parsePdfWithRetries } from "../../utils/helper";

const CONFIG = envConfig();

// Extend the Express Request interface to include the file property from multe

/**
 * Generate a presigned URL for file upload to S3
 * @param req Request object with file details (name, type) in the body
 * @param res Response object
 */
const getPresignedUrl = async (req: Request, res: Response) => {
  try {
    // Extract file information from the request using multer's file object
    let fileType;
    let fileName;
    let pdfparse;

    console.log(">>>>>>>>>>>>>>>>>>>>>");
    console.log(req.file);

    if (req.file) {
      // If file was uploaded with multer
      fileType = req.file.mimetype;
      fileName = req.file.originalname;
      const bufferCopy = Buffer.from(req.file.buffer);
      // Parse the PDF content
      const pdfParsedTemp = await parsePdfWithRetries(bufferCopy);
      pdfparse = pdfParsedTemp;
    } else {
      // Fallback to form field values
      fileType = req.body.fileType;
      fileName = req.body.fileName;
      const bufferCopy = Buffer.from(req.body.buffer);
      // Parse the PDF content
      const pdfParsedTemp = await parsePdfWithRetries(bufferCopy);
      pdfparse = pdfParsedTemp;
    }

    console.log(">>>>>>>>>>>>>>>>>>>>>");
    console.log(fileType, fileName);

    if (!fileType || !fileName) {
      return res.status(400).json({
        success: false,
        message: "File type and file name are required",
        receivedBody: req.body,
        receivedFile: req.file,
      });
    }

    // Generate a unique file path for S3
    const uniqueId = uuidv4();
    const fileExtension = fileName.split(".").pop();
    const filePath = `resume-uploads/${uniqueId}.${fileExtension}`;
    try {
      // Generate presigned URL using the existing utility
      const presignedUrl = await gettingPreSignedUrl(filePath, fileType);
      // Create the public URL for the file
      const fileUrl = `https://${CONFIG.s3_bucket}.s3.amazonaws.com/${filePath}`;

      return res.status(200).json({
        success: true,
        message: API_RESPONSE_MSG.success,
        data: {
          presignedUrl: presignedUrl.split("?")[0],
          fileUrl,
          fileText: pdfparse.text,
        },
      });
    } catch (error) {
      console.error("Error generating presigned URL:", error);
      return res.status(500).json({
        success: false,
        message: API_RESPONSE_MSG.failed,
        error: error.message,
      });
    }
  } catch (error) {
    console.error("Error in getPresignedUrl:", error);
    return res.status(500).json({
      success: false,
      message: API_RESPONSE_MSG.failed,
      error: error.message,
    });
  }
};

export default getPresignedUrl;
