import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  updateOrScheduleInterview,
  getInterviewers,
  getMyInterviews,
  getUpcomingOrPastInterviews,
  addInterviewSkillQuestion,
  getInterviewSkillQuestions,
  updateInterviewSkillQuestion,
  getJobList,
  getCandidateList,
  updateInterviewAnswers,
  endInterview,
  conductInterviewStaticInformation,
  getInterviewFeedback,
  updateInterviewFeedback,
  getPendingInterviews,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import {
  getInterviewersValidation,
  updateOrScheduleInterviewValidation,
  getMyInterviewsValidation,
  getInterviewSkillQuestionsValidation,
  updateInterviewSkillQuestionValidation,
  addInterviewSkillQuestionValidation,
  getJobListValidation,
  getCandidateListValidation,
  updateInterviewAnswersValidation,
  endInterviewValidation,
  updateInterviewFeedbackValidation,
  getInterviewFeedbackValidation,
} from "./vaildation";
import {
  schemaValidation,
  queryValidation,
  paramsValidation,
} from "../../middleware/validateSchema";
import {
  authorizedForManagePreInterviewQuestions,
  authorizedForScheduleConductInterviews,
} from "../../middleware/isAuthorized";

const interviewRoutes = express.Router();

interviewRoutes.post(
  ROUTES.INTERVIEW.UPDATE_OR_SCHEDULE_INTERVIEW,
  auth,
  authorizedForScheduleConductInterviews,
  schemaValidation(updateOrScheduleInterviewValidation),
  HandleErrors(updateOrScheduleInterview)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.GET_MY_INTERVIEWS,
  auth,
  queryValidation(getMyInterviewsValidation),
  HandleErrors(getMyInterviews)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.GET_INTERVIEWERS,
  auth,
  queryValidation(getInterviewersValidation),
  HandleErrors(getInterviewers)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.GET_INTERVIEW_SKILL_QUESTIONS,
  auth,
  queryValidation(getInterviewSkillQuestionsValidation),
  HandleErrors(getInterviewSkillQuestions)
);

interviewRoutes.post(
  ROUTES.INTERVIEW.UPDATE_INTERVIEW_SKILL_QUESTION,
  auth,
  authorizedForManagePreInterviewQuestions,
  schemaValidation(updateInterviewSkillQuestionValidation),
  HandleErrors(updateInterviewSkillQuestion)
);

interviewRoutes.post(
  ROUTES.INTERVIEW.ADD_INTERVIEW_SKILL_QUESTION,
  auth,
  authorizedForManagePreInterviewQuestions,
  schemaValidation(addInterviewSkillQuestionValidation),
  HandleErrors(addInterviewSkillQuestion)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.GET_UPCOMING_OR_PAST_INTERVIEWS,
  auth,
  HandleErrors(getUpcomingOrPastInterviews)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.GET_JOB_LIST,
  auth,
  queryValidation(getJobListValidation),
  HandleErrors(getJobList)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.GET_CANDIDATE_LIST,
  auth,
  queryValidation(getCandidateListValidation),
  HandleErrors(getCandidateList)
);

interviewRoutes.post(
  ROUTES.INTERVIEW.UPDATE_INTERVIEW_ANSWERS,
  auth,
  schemaValidation(updateInterviewAnswersValidation),
  HandleErrors(updateInterviewAnswers)
);

interviewRoutes.post(
  ROUTES.INTERVIEW.END_INTERVIEW,
  auth,
  schemaValidation(endInterviewValidation),
  HandleErrors(endInterview)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.CONDUCT_INTERVIEW_STATIC_INFORMATION,
  auth,
  HandleErrors(conductInterviewStaticInformation)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.GET_INTERVIEW_FEEDBACK,
  auth,
  paramsValidation(getInterviewFeedbackValidation),
  HandleErrors(getInterviewFeedback)
);

interviewRoutes.post(
  ROUTES.INTERVIEW.UPDATE_INTERVIEW_FEEDBACK,
  auth,
  schemaValidation(updateInterviewFeedbackValidation),
  HandleErrors(updateInterviewFeedback)
);

interviewRoutes.get(
  ROUTES.INTERVIEW.GET_PENDING_INTERVIEWS,
  auth,
  HandleErrors(getPendingInterviews)
);

export default interviewRoutes;
