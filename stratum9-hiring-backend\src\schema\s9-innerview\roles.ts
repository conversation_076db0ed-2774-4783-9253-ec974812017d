import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Employee } from "./employees";
import { RolePermission } from "./role_permissions_mapping";

@Entity("roles")
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  name: string;

  @Column({ name: "organization_id", nullable: false })
  organizationId: number;

  @Column({ name: "is_active", default: true })
  isActive: boolean;

  @Column({ name: "is_default_role", default: false })
  isDefaultRole: boolean;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;

  // Relationships
  @OneToMany(() => Employee, (employee) => employee.role)
  employees: Employee[];

  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.role)
  rolePermissions: RolePermission[];
}

export default Role;
