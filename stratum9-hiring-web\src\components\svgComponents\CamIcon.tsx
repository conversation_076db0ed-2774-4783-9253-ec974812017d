const CamIcon = (props: { className?: string }) => {
  const { className } = props;
  return (
    <div className={className}>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" fill="none">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M13.3492 4.66602H7.65083H7.65081C6.54913 4.666 5.65169 4.66599 4.92307 4.72552C4.17002 4.78705 3.49517 4.91794 2.86655 5.23823C1.8787 5.74157 1.07555 6.54472 0.572217 7.53257C0.251921 8.16118 0.121035 8.83604 0.0595077 9.58909C-2.26523e-05 10.3177 -1.23186e-05 11.2151 3.68922e-07 12.3168V12.3168V15.6819V15.6819C-1.23186e-05 16.7836 -2.26523e-05 17.681 0.0595077 18.4096C0.121035 19.1627 0.251921 19.8375 0.572217 20.4661C1.07555 21.454 1.8787 22.2571 2.86655 22.7605C3.49517 23.0808 4.17002 23.2117 4.92307 23.2732C5.65167 23.3327 6.54908 23.3327 7.65072 23.3327H7.65086H13.3491H13.3493C14.4509 23.3327 15.3483 23.3327 16.0769 23.2732C16.83 23.2117 17.5048 23.0808 18.1335 22.7605C19.1213 22.2571 19.9244 21.454 20.4278 20.4661C20.7481 19.8375 20.879 19.1627 20.9405 18.4096C20.9435 18.3723 20.9464 18.3346 20.9492 18.2964L26.1552 22.015C26.9274 22.5665 28 22.0145 28 21.0656V6.93305C28 5.98411 26.9274 5.43213 26.1552 5.98369L20.9492 9.7023C20.9464 9.66412 20.9435 9.62638 20.9405 9.58909C20.879 8.83604 20.7481 8.16118 20.4278 7.53257C19.9244 6.54472 19.1213 5.74157 18.1335 5.23823C17.5048 4.91794 16.83 4.78705 16.0769 4.72552C15.3483 4.66599 14.4509 4.666 13.3492 4.66602H13.3492ZM3.92586 7.31725C4.17072 7.19249 4.50432 7.10084 5.11308 7.05111C5.73546 7.00026 6.53731 6.99935 7.7 6.99935H13.3C14.4627 6.99935 15.2645 7.00026 15.8869 7.05111C16.4957 7.10084 16.8293 7.19249 17.0741 7.31725C17.6229 7.59688 18.0691 8.04307 18.3488 8.59188C18.4735 8.83673 18.5652 9.17034 18.6149 9.7791C18.6658 10.4015 18.6667 11.2033 18.6667 12.366V15.6327C18.6667 16.7954 18.6658 17.5972 18.6149 18.2196C18.5652 18.8284 18.4735 19.162 18.3488 19.4068C18.0691 19.9556 17.6229 20.4018 17.0741 20.6815C16.8293 20.8062 16.4957 20.8979 15.8869 20.9476C15.2645 20.9984 14.4627 20.9994 13.3 20.9994H7.7C6.53731 20.9994 5.73546 20.9984 5.11308 20.9476C4.50432 20.8979 4.17072 20.8062 3.92586 20.6815C3.37706 20.4018 2.93086 19.9556 2.65123 19.4068C2.52647 19.162 2.43483 18.8284 2.38509 18.2196C2.33424 17.5972 2.33333 16.7954 2.33333 15.6327V12.366C2.33333 11.2033 2.33424 10.4015 2.38509 9.7791C2.43483 9.17034 2.52647 8.83673 2.65123 8.59188C2.93086 8.04307 3.37706 7.59688 3.92586 7.31725Z"
          fill="white"
        />
      </svg>
    </div>
  );
};

export default CamIcon;
