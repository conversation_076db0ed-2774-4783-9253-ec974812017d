import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { NotificationItem } from "@/interfaces/notificationInterface";

export const getNotifications = (data: { offset: number; limit: number }): Promise<IApiResponseCommonInterface<NotificationItem[]>> => {
  return http.get(endpoint.notification.GET_NOTIFICATIONS, data);
};

export const deleteAllNotifications = (): Promise<IApiResponseCommonInterface<unknown>> => {
  return http.remove(endpoint.notification.DELETE_ALL_NOTIFICATIONS);
};

export const updateNotificationStatus = (): Promise<IApiResponseCommonInterface<unknown>> => {
  return http.post(endpoint.notification.UPDATE_NOTIFICATION, {});
};

export const getUnreadNotificationsCount = (): Promise<IApiResponseCommonInterface<{ count: number }>> => {
  return http.get(endpoint.notification.GET_UNREAD_NOTIFICATIONS_COUNT);
};
