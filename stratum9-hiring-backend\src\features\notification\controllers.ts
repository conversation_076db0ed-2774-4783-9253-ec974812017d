import { Request, Response } from "express";
import NotificationServices from "./services";
import { GetNotificationsParams } from "./interface";

export const getNotificationsController = async (
  req: Request,
  res: Response
) => {
  try {
    const result = await NotificationServices.getNotifications(
      req.orgId,
      req.userId,
      req.query as GetNotificationsParams
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const markAsWatchedController = async (req: Request, res: Response) => {
  try {
    const result = await NotificationServices.markAsWatched(
      req.orgId,
      req.userId
    );

    return res.status(200).json({
      ...result,
    });
  } catch (error) {
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const deleteUsersAllNotificationsController = async (
  req: Request,
  res: Response
) => {
  try {
    const result = await NotificationServices.deleteUsersAllNotifications(
      req.orgId,
      req.userId
    );

    return res.status(200).json({
      ...result,
    });
  } catch (error) {
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const getUnreadNotificationsCountController = async (
  req: Request,
  res: Response
) => {
  try {
    const result = await NotificationServices.getUnreadNotificationsCount(
      req.orgId,
      req.userId
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};
