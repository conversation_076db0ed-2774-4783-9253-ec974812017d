import express from "express";
import HandleErrors from "../../middleware/handleError";
import { sanitizeBody } from "../../middleware/sanitize";
import {
  queryValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import { ROUTES } from "../../utils/constants";
import {
  uploadManualCandidate,
  getAllPendingJobApplications,
  changeApplicationStatus,
} from "./controller";
import getPresignedUrl from "./presignedUrl.controller";
import {
  manualCandidateUploadSchema,
  getAllPendingJobApplicationsSchema,
  changeApplicationStatusParamsSchema,
} from "./validation";
import upload from "../../middleware/upload";
import auth from "../../middleware/auth";
import { authorizedForManualResumeScreening } from "../../middleware/isAuthorized";

const resumeScreenRoutes = express.Router();

/**
 * @swagger
 * tags:
 *   name: ResumeScreen
 *   description: Resume screening and candidate management
 */

/**
 * @swagger
 * /api/v1/resume-screen/manual-candidate-upload:
 *   post:
 *     summary: Upload a candidate manually
 *     description: Creates a new candidate and job application
 *     tags: [ResumeScreen]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - organization_id
 *               - hiring_manager_id
 *               - job_id
 *               - candidates
 *             properties:
 *               organization_id:
 *                 type: number
 *                 description: Organization ID associated with the candidates
 *               hiring_manager_id:
 *                 type: number
 *                 description: ID of the hiring manager
 *               job_id:
 *                 type: number
 *                 description: Job ID associated with the applications
 *               candidates:
 *                 type: array
 *                 description: Array of candidate data
 *                 items:
 *                   type: object
 *                   required:
 *                     - name
 *                     - email
 *                     - gender
 *                     - additional_details
 *                     - resume_file
 *                     - resume_text
 *                     - assessment_file
 *                     - assessment_text
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: Name of the candidate
 *                     email:
 *                       type: string
 *                       description: Email of the candidate
 *                     gender:
 *                       type: string
 *                       enum: [Male, Female]
 *                       description: Gender of the candidate
 *                     additional_details:
 *                       type: string
 *                       description: Additional details about the candidate
 *                     resume_file:
 *                       type: string
 *                       description: S3 URL link to the resume file (required)
 *                     assessment_file:
 *                       type: string
 *                       description: S3 URL link to the assessment file (optional)
 *                   note: |
 *                     Fields like resume_text, assessment_text, and imageUrl are automatically
 *                     populated by the backend after processing the uploaded files.
 *     responses:
 *       200:
 *         description: Candidate uploaded successfully
 *       400:
 *         description: Invalid input data
 *       500:
 *         description: Server error occurred
 */
resumeScreenRoutes.post(
  ROUTES.RESUME_SCREEN.MANUAL_CANDIDATE_UPLOAD,
  auth,
  authorizedForManualResumeScreening,
  sanitizeBody(),
  schemaValidation(manualCandidateUploadSchema),
  HandleErrors(uploadManualCandidate)
);

/**
 * @swagger
 * /api/v1/resume-screen/get-all-pending-job-applications:
 *   get:
 *     summary: Get all pending job applications
 *     description: Retrieves all pending job applications with optional filtering by job_id, hiring_manager_id, status, and pagination support
 *     tags: [ResumeScreen]
 *     parameters:
 *       - in: query
 *         name: job_id
 *         schema:
 *           type: integer
 *         description: Optional job ID to filter applications
 *       - in: query
 *         name: hiring_manager_id
 *         schema:
 *           type: integer
 *         description: Optional hiring manager ID to filter applications
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Pending, Approved, Rejected, On-Hold]
 *         description: Optional status to filter applications (default is Pending)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Optional number of records to return per page (default is 10)
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Optional number of records to skip for pagination (default is 0)
 *     responses:
 *       200:
 *         description: List of pending job applications retrieved successfully
 *       400:
 *         description: Invalid input data
 *       500:
 *         description: Server error occurred
 */
resumeScreenRoutes.get(
  ROUTES.RESUME_SCREEN.GET_ALL_PENDING_JOB_APPLICATIONS,
  auth,
  authorizedForManualResumeScreening,
  queryValidation(getAllPendingJobApplicationsSchema),
  HandleErrors(getAllPendingJobApplications)
);

/**
 * @swagger
 * /api/v1/resume-screen/change-application-status:
 *   post:
 *     summary: Change the status of a job application
 *     description: Updates the status of a job application and creates a status history record
 *     tags: [ResumeScreen]
 *     parameters:
 *       - in: query
 *         name: job_id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Job ID associated with the application
 *       - in: query
 *         name: candidate_id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Candidate ID associated with the application
 *       - in: query
 *         name: hiring_manager_id
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID of the hiring manager changing the status
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Approved, Rejected, On-Hold]
 *         required: true
 *         description: New status for the application
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - hiring_manager_reason
 *             properties:
 *               hiring_manager_reason:
 *                 type: string
 *                 description: Reason for the status change
 *     responses:
 *       200:
 *         description: Application status updated successfully
 *       400:
 *         description: Invalid input data or application not found
 *       500:
 *         description: Server error occurred
 */
resumeScreenRoutes.post(
  ROUTES.RESUME_SCREEN.CHANGE_APPLICATION_STATUS,
  auth,
  authorizedForManualResumeScreening,
  sanitizeBody(),
  schemaValidation(changeApplicationStatusParamsSchema),
  HandleErrors(changeApplicationStatus)
);

/**
 * @swagger
 * /api/v1/resume-screen/get-presigned-url:
 *   post:
 *     summary: Generate a presigned URL for file upload
 *     description: Generates a presigned URL for uploading files to S3
 *     tags: [ResumeScreen]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - fileName
 *               - fileType
 *             properties:
 *               fileName:
 *                 type: string
 *                 description: Original name of the file
 *               fileType:
 *                 type: string
 *                 description: MIME type of the file
 *     responses:
 *       200:
 *         description: Presigned URL generated successfully
 *       400:
 *         description: Invalid input data
 *       500:
 *         description: Server error occurred
 */
resumeScreenRoutes.post(
  ROUTES.RESUME_SCREEN.GET_PRESIGNED_URL,
  auth,
  authorizedForManualResumeScreening,
  upload.single("file"), // Process multipart/form-data with 'file' field
  HandleErrors(getPresignedUrl)
);

export default resumeScreenRoutes;
