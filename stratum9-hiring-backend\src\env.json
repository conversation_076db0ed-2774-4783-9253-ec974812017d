{"local": {"env": "local", "port": 3001, "profile": "stratum9", "secretManagerKey": "stratum9_mobile_app-secret-keys-stratum9", "region": "us-east-1", "s9_public_url": "https://develop.drrjxkqeoo26r.amplifyapp.com", "generate_interview_questions_lambda": "stratum-hiring-cron-jobs-development-generateInterviewQuestions", "generate_int_final_summary_lambda": "stratum-hiring-cron-jobs-development-generateIntFinalSummary", "s3_bucket": "s9-interview-assets", "s3BucketName": "s9-interview-assets", "s3AccessKey": "********************", "s3SecretKey": "4O+kA6wzYIMYE8zAoOcFqhleRx6PV/rHX8iJU1tQ", "s3Region": 0, "s3FilenamePrefix": "agoraInterviewRecordings", "stripe": {"success_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/success", "cancel_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/cancel", "api_version": "2025-06-30.basil"}}, "development": {"env": "development", "port": 3001, "region": "us-east-1", "profile": "stratum9", "secretManagerKey": "stratum9_mobile_app-secret-keys-stratum9", "s9_public_url": "https://develop.drrjxkqeoo26r.amplifyapp.com", "s3_bucket": "s9-interview-assets", "s3BucketName": "s9-interview-assets", "s3AccessKey": "********************", "s3SecretKey": "4O+kA6wzYIMYE8zAoOcFqhleRx6PV/rHX8iJU1tQ", "s3Region": 0, "s3FilenamePrefix": "agoraInterviewRecordings", "generate_interview_questions_lambda": "stratum-hiring-cron-jobs-development-generateInterviewQuestions", "generate_int_final_summary_lambda": "stratum-hiring-cron-jobs-development-generateIntFinalSummary", "stripe": {"success_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/success", "cancel_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/cancel", "api_version": "2025-06-30.basil"}}, "production": {"env": "production", "port": 3001, "region": "us-east-1", "profile": "stratum9", "secretManagerKey": "prod-secret-keys-stratum9", "s3_bucket": "s9-interview-assets-prod", "s3BucketName": "s9-interview-assets", "s3AccessKey": "********************", "s3SecretKey": "4O+kA6wzYIMYE8zAoOcFqhleRx6PV/rHX8iJU1tQ", "s3Region": 0, "s3FilenamePrefix": "agoraInterviewRecordings", "generate_interview_questions_lambda": "stratum-hiring-cron-jobs-production-generateInterviewQuestions", "generate_int_final_summary_lambda": "stratum-hiring-cron-jobs-production-generateIntFinalSummary", "stripe": {"success_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/success", "cancel_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/cancel", "api_version": "2025-06-30.basil"}}}