/* eslint-disable prefer-destructuring */
/* eslint-disable no-unused-vars */
import { NextFunction, Response, Request } from "express";
import * as jwt from "jsonwebtoken";
import { getSecretKeys } from "../config/awsConfig";
import { PLATFORM, USER_TYPE } from "../utils/constants";

export const adminAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const keys = await getSecretKeys();
  const jwtToken =
    <string>req.headers.auth ||
    <string>req.headers.authorization ||
    <string>req.headers.Authorization;
  const tokenParts = jwtToken?.split(" ");

  try {
    if (!jwtToken) {
      return res.status(401).send({
        message: "token_req_msg",
        success: false,
        error: "token_req",
      });
    }

    if (tokenParts.length !== 2 || tokenParts[0].toLowerCase() !== "bearer") {
      return res.status(401).send({
        message: "invalid_token_msg",
        success: false,
        error: "invalid_token",
      });
    }

    const verify = jwt.verify(tokenParts[1], keys.token_key) as jwt.JwtPayload;
    if (!verify) {
      return res.status(401).send({
        message: "invalid_token_msg",
        success: false,
        error: "invalid_token",
      });
    }

    if (
      verify.type !== USER_TYPE.admin ||
      verify.platform !== PLATFORM.STRATUM9_INNERVIEW
    ) {
      return res.status(401).send({
        message: "unauthorized_msg",
        success: false,
        error: "unauthorized",
      });
    }

    req.userId = verify.id;
    next();
  } catch (error) {
    if (error.name === "TokenExpiredError") {
      return res.status(401).send({
        message: "token_expired_msg",
        success: false,
        error: "token_expired",
      });
    }
    return res.status(401).send({
      message: "token_invalid_msg",
      success: false,
      error: "invalid_token",
    });
  }
  return null;
};

export default adminAuth;
