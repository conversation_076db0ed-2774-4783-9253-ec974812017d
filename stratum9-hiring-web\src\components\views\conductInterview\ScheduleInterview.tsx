/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
"use client";
// import Link from "next/link";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { DateSelectArg, DatesSetArg } from "@fullcalendar/core";
import { EventImpl } from "@fullcalendar/core/internal";
import { useTranslations } from "next-intl";
import { debounce } from "lodash";
import { yupResolver } from "@hookform/resolvers/yup";

import { formatTimeForInput, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import CalenderEventModal from "@/components/commonModals/CalendarEventModal";
import style from "../../../styles/conductInterview.module.scss";
import { getInterviewers, getMyInterviews, updateOrScheduleInterview } from "@/services/interviewServices";
import { scheduleInterviewValidation } from "@/validations/interviewValidations";
import { IGetInterviewersResponse, IGetInterviewsResponse, IGetJobListResponse, ScheduleInterviewFormValues } from "@/interfaces/interviewInterfaces";
import InterviewDetailModal from "@/components/commonModals/InterviewDetailModal";
import CommonCalendar from "@/components/commonComponent/CommonCalendar";
import { PERMISSION, ScheduleInterviewFormSubmissionType } from "@/constants/commonConstants";
import { defaultValues } from "./ScheduleInterviewFromCalendar";
import toast from "react-hot-toast";
import { useSearchParams } from "next/navigation";
import { useHasPermission } from "@/utils/permission";

const ScheduleInterview = () => {
  const t = useTranslations();
  const initialFetchCompleted = useRef(false);
  const params = useSearchParams();

  const applicationId = params?.get("applicationId");
  const jobId = params?.get("jobId");
  const candidateName = params?.get("candidateName");
  const title = params?.get("title");
  const jobUniqueId = params?.get("jobUniqueId");

  const [interviews, setInterviews] = useState<IGetInterviewsResponse[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [interviewers, setInterviewers] = useState<IGetInterviewersResponse[]>([]);
  const [monthYear, setMonthYear] = useState<string>(`${String(new Date().getMonth() + 1).padStart(2, "0")}-${new Date().getFullYear()}`);
  const [loader, setLoader] = useState(false);
  const [jobs, setJobs] = useState<IGetJobListResponse[]>([]);
  const [fileUrls, setFileUrls] = useState<string[]>([]);
  const [interviewInfo, setInterviewInfo] = useState<IGetInterviewsResponse | null>(null);
  const [formType, setFormType] = useState<(typeof ScheduleInterviewFormSubmissionType)[keyof typeof ScheduleInterviewFormSubmissionType]>(
    ScheduleInterviewFormSubmissionType.SCHEDULE
  );

  const hasScheduleInterviewPermission = useHasPermission(PERMISSION.SCHEDULE_CONDUCT_INTERVIEWS);

  console.log("formType==", formType);

  const currentFileArrayLengthRef = useRef(fileUrls ? fileUrls.length : 0);

  useEffect(() => {
    if (applicationId) setValue("candidate", +applicationId);
    if (jobId) setValue("jobTitle", +jobId);
    if (jobUniqueId) setValue("jobId", jobUniqueId);
  }, [applicationId, jobId, jobUniqueId, isModalOpen]);

  console.log("====jobs====", jobs);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
    setError,
    getValues,
  } = useForm({
    resolver: yupResolver(scheduleInterviewValidation(t)),
  });

  console.log("control==", control._formValues);
  console.log("currentFileArrayLengthRef.current==", currentFileArrayLengthRef.current);

  const getAllInterviews = async (monthYear: string) => {
    try {
      const result = await getMyInterviews(monthYear);

      console.log("result", result);
      if (result?.data?.success) {
        const intterviews = result?.data?.data;
        setInterviews(intterviews);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onSubmit = async (data: ScheduleInterviewFormValues) => {
    try {
      if (!hasScheduleInterviewPermission) {
        toastMessageError(t("you_dont_have_permission_to_schedule_interview"));
        return;
      }
      setLoading(true);
      const { eventTitle, date, startTime, endTime, description, jobTitle, candidate, interviewer, interviewType } = data;
      console.log("inside onsubmit", data);

      const startDateTime = new Date(`${date}T${startTime}`);
      const endDateTime = new Date(`${date}T${endTime}`);

      // Convert to timestamp (milliseconds since epoch)
      const startTimestamp = startDateTime.getTime();
      const endTimestamp = endDateTime.getTime();
      const scheduleAtTimestamp = new Date().getTime();

      const updatePayload = {
        title: eventTitle,
        interviewerId: +interviewer,
        scheduleAt: scheduleAtTimestamp,
        startTime: startTimestamp,
        endTime: endTimestamp,
        description: description ?? "",
        roundType: interviewType,
        jobApplicationId: +candidate,
        interviewId: +interviewInfo?.id!,
        fileUrlArray: JSON.stringify(fileUrls),
      };

      console.log("updatePayload======>>>>", updatePayload);

      const payload = {
        ...updatePayload,
        interviewId: undefined,
        jobId: +jobTitle,
      };

      console.log("payload======>>>>", payload);

      const result = await updateOrScheduleInterview(formType === ScheduleInterviewFormSubmissionType.SCHEDULE ? payload : updatePayload);

      if (result?.data?.success) {
        toastMessageSuccess(t(result?.data?.message));
        setIsModalOpen(false);
        reset();
        setFileUrls([]);
        currentFileArrayLengthRef.current = 0;
        getAllInterviews(monthYear);
        setLoading(false);
      } else {
        setLoading(false);
        toastMessageError(t(result?.data?.message));
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // this function automatically called when calender renders
  const handleDatesSet = (info: DatesSetArg) => {
    console.log("Dates set:", info);

    // Get start date and add 7 days to ensure we're in the current month view
    const startDate = new Date(info.start);
    const adjustedDate = new Date(startDate);
    adjustedDate.setDate(startDate.getDate() + 7);

    // Format as MM-YYYY
    const month = String(adjustedDate.getMonth() + 1).padStart(2, "0"); // +1 because months are 0-indexed
    const year = adjustedDate.getFullYear();
    const formattedDate = `${month}-${year}`;

    if (formattedDate !== monthYear || !initialFetchCompleted.current) {
      setMonthYear(formattedDate);
      setInterviews([]);
      getAllInterviews(formattedDate);
      initialFetchCompleted.current = true;
    }
  };

  // when click on scheduled interview event it will show the data on the interview detail modal
  const handleEventClick = ({ event }: { event: EventImpl }) => {
    console.log("Event clicked:", event);

    const interviewInfoObj = interviews.find((interview) => Number(interview.id) === Number(event.id)) ?? null;

    console.log("interviewInfoObj", interviewInfoObj);

    setInterviewInfo(interviewInfoObj);
    const filesUrls = interviewInfoObj ? JSON.parse(interviewInfoObj?.attachments)?.fileUrls : [];
    setFileUrls(filesUrls);
    setIsViewModalOpen(true);
  };

  console.log("hasScheduleInterviewPermission");

  // function to select the date from calender
  const handleOnSelect = (info: DateSelectArg) => {
    toast.dismiss();
    console.log("Event selected:", info);

    if (!hasScheduleInterviewPermission) {
      console.log("inside if", !hasScheduleInterviewPermission);
      toastMessageError(t("you_dont_have_permission_to_schedule_interview"));
      return;
    }
    // Parse the selected date information
    const startDate = new Date(info.start);
    const endDate = new Date(info.end);

    const now = new Date().getTime();

    if (startDate.getTime() < now) {
      toastMessageError(t("cannot_schedule_interview_past"));
      return;
    }

    // Format date as YYYY-MM-DD for date input
    const formattedDate = startDate.toLocaleDateString("en-CA");
    console.log("formattedDate", formattedDate);

    const startTime = formatTimeForInput(startDate);
    const endTime = formatTimeForInput(endDate);

    if (jobUniqueId && title && jobId) {
      setJobs([
        {
          jobId: jobUniqueId,
          value: +jobId,
          label: title,
        },
      ]);
    }

    setValue("date", formattedDate);
    setValue("startTime", startTime);
    setValue("endTime", endTime);
    setIsModalOpen(true);
    getInterviewersList("");
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setFormType(ScheduleInterviewFormSubmissionType.SCHEDULE);
    reset(defaultValues);
  };

  // function to fetch interviewers list
  const getInterviewersList = useCallback(async (searchString: string) => {
    setLoader(true);
    try {
      const response = await getInterviewers(searchString, getValues ? getValues("jobTitle")!.toString() : "");

      if (response?.data?.success) {
        setInterviewers(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching interviewers:", error);
    } finally {
      setLoader(false);
    }
  }, []);

  const handleSearchInputChange = (event: string) => {
    const searchString = event.trim();
    console.log("searchString", searchString);
    getInterviewersList(searchString);
  };

  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 1000);

  // function to edit or update the interview
  const onHandleEdit = () => {
    console.log("inside edit");
    setIsViewModalOpen(false);
    setFormType(ScheduleInterviewFormSubmissionType.UPDATE);
    setIsModalOpen(true);
    getInterviewersList("");

    const startDate = interviewInfo?.start ? new Date(interviewInfo.start) : new Date();
    const endDate = interviewInfo?.end ? new Date(interviewInfo.end) : new Date();

    const startTime = formatTimeForInput(startDate);
    const endTime = formatTimeForInput(endDate);

    reset({
      eventTitle: interviewInfo?.title,
      date: startDate.toLocaleDateString("en-CA"),
      startTime,
      endTime,
      description: interviewInfo?.description,
      interviewer: interviewInfo?.interviewerId,
      jobId: interviewInfo?.jobUniqueId,
      jobTitle: interviewInfo?.jobId,
      interviewType: interviewInfo?.roundType,
      candidate: interviewInfo?.jobApplicationId,
    });
  };

  const onHandleViewModelClose = () => {
    setInterviewInfo(null);
    setFileUrls([]);
    setIsViewModalOpen(false);
    reset(defaultValues);
  };

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <span>{t("calendar")}</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-section">
          <CommonCalendar
            handleDatesSet={handleDatesSet}
            handleOnSelect={handleOnSelect}
            interviews={interviews}
            handleEventClick={handleEventClick}
          />
          {isModalOpen ? (
            <CalenderEventModal
              control={control}
              formType={formType}
              onClose={handleModalClose}
              handleSubmit={handleSubmit((data) => {
                console.log("data", data);
                return onSubmit(data as ScheduleInterviewFormValues);
              })}
              setFileUrls={setFileUrls}
              fileUrls={fileUrls}
              loading={loading}
              errors={errors}
              currentFileArrayLengthRef={currentFileArrayLengthRef}
              interviewers={interviewers}
              loader={loader}
              debouncedHandleSearchInputChange={debouncedHandleSearchInputChange}
              getValues={getValues}
              setValue={setValue}
              setError={setError}
              jobs={jobs}
              setJobs={setJobs}
              interviewInfo={interviewInfo}
              candidateName={candidateName || ""}
            />
          ) : null}
          {isViewModalOpen ? (
            <InterviewDetailModal onEdit={onHandleEdit} onClose={onHandleViewModelClose} interviewInfo={interviewInfo} attachments={fileUrls} />
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default ScheduleInterview;
