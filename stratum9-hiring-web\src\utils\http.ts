// src/utils/http.ts
import axios, { AxiosResponse } from "axios";
import { getSession } from "next-auth/react";

import env from "@/config/config";
import { withData, withError } from "@/utils/api";
import { ISession } from "@/interfaces/commonInterfaces";
import { logout, toastMessageError } from "./helper";
import { TOKEN_EXPIRED } from "@/constants/commonConstants";

export const http = axios.create({
  baseURL: env.apiBaseUrl,
  headers: { "Content-Type": "application/json" },
});

http.interceptors.request.use(async (req) => {
  const session = (await getSession()) as unknown as ISession;
  const accessToken = session?.user?.data?.token;
  if (accessToken) {
    req.headers.authorization = `Bearer ${accessToken}`;
  }
  req.headers["ngrok-skip-browser-warning"] = "fjdlkghjsk";
  return req;
});

// Flag to prevent multiple logout calls
let isLoggingOut = false;

http.interceptors.response.use(
  (res) => withData(res.data) as AxiosResponse,
  async (err) => {
    const session = (await getSession()) as unknown as ISession;
    const userId = session?.user?.data?.authData?.userData?.id;
    const accessToken = session?.user?.data?.token;

    if (err?.response?.status === 401 && !isLoggingOut && accessToken) {
      isLoggingOut = true;
      try {
        await logout(userId);
        toastMessageError(TOKEN_EXPIRED);

        if (typeof window !== "undefined") {
          window.location.reload();
        }
      } catch (error) {
        console.error("Session cleanup error:", error);
      } finally {
        isLoggingOut = false;
      }
    } else if (err?.response?.status === 403) {
      // Show toast message for forbidden access (403)
      toastMessageError(err?.response?.data?.message);
    }
    return withError(err?.response?.data?.error);
  }
);

export function get<P, R>(url: string, params?: P): Promise<R> {
  return http({
    method: "get",
    url,
    params,
  });
}

export function post<D, P, R>(url: string, data: D, params?: P): Promise<R> {
  return http({
    method: "post",
    url,
    data,
    params,
  });
}

export function postFile<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {
  return http({
    method: "post",
    url,
    data,
    params,
    headers: { "Content-Type": "multipart/form-data" },
  });
}
export function put<D, P, R>(url: string, data: D, params?: P): Promise<R> {
  return http({
    method: "put",
    url,
    data,
    params,
  });
}

export function patch<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {
  return http({
    method: "patch",
    url,
    data,
    params,
  });
}
export function remove<P, R>(url: string, params?: P): Promise<R> {
  return http({
    method: "delete",
    url,
    params,
  });
}
