{"env": {"node": true, "browser": true, "es2021": true, "jest": true}, "extends": ["plugin:react/recommended", "airbnb", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "jest"], "rules": {"no-shadow": "off", "@typescript-eslint/no-shadow": ["error"], "react/react-in-jsx-scope": "off", "react/jsx-filename-extension": [2, {"extensions": [".js", ".jsx", ".ts", ".tsx"]}], "no-use-before-define": "off", "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "no-console": "off"}, "settings": {"import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}}}