import React, { FC } from "react";

const FileIcon: FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="42" height="42" viewBox="0 0 60 60" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.29 42.4341H21.24C20.205 42.4341 19.365 41.5941 19.365 40.5591C19.365 39.5241 20.205 38.6841 21.24 38.6841H39.29C40.325 38.6841 41.165 39.5241 41.165 40.5591C41.165 41.5941 40.325 42.4341 39.29 42.4341Z"
        fill="#999999"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.29 31.9678H21.24C20.205 31.9678 19.365 31.1278 19.365 30.0928C19.365 29.0578 20.205 28.2178 21.24 28.2178H39.29C40.325 28.2178 41.165 29.0578 41.165 30.0928C41.165 31.1278 40.325 31.9678 39.29 31.9678Z"
        fill="#999999"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.1267 21.5259H21.2393C20.2043 21.5259 19.3643 20.6859 19.3643 19.6509C19.3643 18.6159 20.2043 17.7759 21.2393 17.7759H28.1267C29.1617 17.7759 30.0017 18.6159 30.0017 19.6509C30.0017 20.6859 29.1617 21.5259 28.1267 21.5259Z"
        fill="#999999"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.7721 8.75L20.5495 8.76C14.7295 8.795 11.2495 12.395 11.2495 18.3925V41.3825C11.2495 47.42 14.762 51.025 20.6395 51.025L39.8621 51.0175C45.6821 50.9825 49.1621 47.3775 49.1621 41.3825V18.3925C49.1621 12.355 45.6521 8.75 39.7721 8.75ZM20.642 54.775C12.782 54.775 7.49951 49.3925 7.49951 41.3825V18.3925C7.49951 10.31 12.617 5.0575 20.537 5.01L39.7721 5C47.6321 5 52.9121 10.3825 52.9121 18.3925V41.3825C52.9121 49.4625 47.7946 54.7175 39.8746 54.7675L20.642 54.775Z"
        fill="#333333"
      />
    </svg>
  );
};

export default FileIcon;
