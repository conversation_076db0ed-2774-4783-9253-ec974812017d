import sgMail from "@sendgrid/mail";
import { getSecretKeys } from "../config/awsConfig";
import { ISendMail, IEmailData } from "./commonInterfaces";

/**
 *  Send mail
 */
export const sendMail = async ({
  email,
  subject,
  textContent,
  htmlContent,
}: ISendMail) => {
  try {
    const keys = await getSecretKeys();

    sgMail.setApiKey(keys.sendgrid_key);
    const mailData: IEmailData = {
      from: keys.sendgrid_mail,
      to: email,
      subject,
      text: textContent,
      html: htmlContent,
    };

    const [res] = await sgMail.send(mailData);

    return {
      isError: false,
      messageId: res.headers["x-message-id"],
      message: "Sent",
    };
  } catch (error) {
    console.log("Sendgrid error===>>>", error);
    return {
      isError: true,
      errorMessage: error.message,
    };
  }
};

export default sendMail;
