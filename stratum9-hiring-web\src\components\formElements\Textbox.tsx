import React, { InputHTMLAttributes } from "react";

import { Control, Controller, FieldValues, Path } from "react-hook-form";

interface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {
  iconClass?: string;
  align?: "left" | "right";
  children?: React.ReactNode;
}

interface TextboxProps<T extends FieldValues> extends CommonInputProps {
  name: Path<T>;
  control: Control<T>;
}

export default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {
  return (
    <div className={`${iconClass} ${align}`}>
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <input
            {...props}
            value={field.value}
            onChange={(e) => {
              field.onChange(e);
              props.onChange?.(e);
            }}
            aria-label=""
          />
        )}
        defaultValue={"" as T[typeof name]}
      />
      {children}
    </div>
  );
}

export function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {
  return (
    <div className={`${iconClass} ${align}`}>
      <input {...props} onChange={onChange} />

      {children}
    </div>
  );
}
