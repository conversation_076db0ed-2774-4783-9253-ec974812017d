import type { <PERSON>ada<PERSON> } from "next";
import "../../node_modules/bootstrap/dist/css/bootstrap.css";
import "../styles/style.scss";

import { NextIntlClientProvider } from "next-intl";
import { getLocale } from "next-intl/server";
import { Toaster } from "react-hot-toast";

// Import Redux Provider
import ReduxProvider from "@/redux/ReduxProvider";
import HeaderWrapper from "@/components/header/HeaderWrapper";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();

  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta charSet="UTF-8" />
        <meta
          name="description"
          content="Stratum9 Hiring Web lets teams post jobs, screen candidates, run structured interviews and hire faster with data-driven insights—all in one intuitive platform."
        />
        <meta name="keywords" content="Stratum9 Hiring Web, Hiring Platform, Job Board, Recruitment Software" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" />
        <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap" rel="stylesheet" />
      </head>
      <body>
        <ReduxProvider>
          <NextIntlClientProvider locale={locale}>
            <div className="main-body">
              <HeaderWrapper />
              <div className="scroll-content">{children}</div>
            </div>
            <Toaster position="top-right" />
          </NextIntlClientProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
