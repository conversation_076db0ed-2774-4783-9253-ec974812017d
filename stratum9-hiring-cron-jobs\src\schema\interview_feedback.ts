import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";

interface IHighlights {
  aiFeedback: string[];
  overAllFeedback: string[];
}

@Entity("interview_feedback")
class InterviewFeedbackModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "interview_id", nullable: true })
  interviewId: number;

  @Column({ name: "job_skill_id", nullable: true })
  jobSkillId: number;

  @Column({ name: "highlights", nullable: true, type: "json" })
  highlights: IHighlights;

  @CreateDateColumn({
    type: "timestamp",
    name: "created_ts",
    nullable: false,
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
    nullable: false,
  })
  updatedTs: Date;
}

export default InterviewFeedbackModel;
