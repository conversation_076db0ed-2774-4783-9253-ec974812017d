import endpoint from "@/constants/endpoint";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import * as http from "@/utils/http";

/**
 * Interface for User Profile data
 */
export interface IUserProfile {
  id: number;
  image: string | null;
  firstName: string;
  lastName: string;
}

/**
 * Get current user profile data
 * @returns Promise with API response containing user profile data
 */
export const getMyProfile = (): Promise<IApiResponseCommonInterface<IUserProfile>> => {
  return http.get<null, IApiResponseCommonInterface<IUserProfile>>(endpoint.userprofile.GET_MY_PROFILE);
};

/**
 * Interface for Update Profile Payload
 */
export interface UpdateProfilePayload {
  firstName: string;
  lastName: string;
  image: string | null;
}

/**
 * Update user profile data
 * @param payload Object containing updated profile data
 * @returns Promise with API response containing updated user profile data
 */
export const updateMyProfile = (payload: UpdateProfilePayload): Promise<IApiResponseCommonInterface<IUserProfile>> => {
  return http.put<UpdateProfilePayload, IUserProfile, IApiResponseCommonInterface<IUserProfile>>(endpoint.userprofile.UPDATE_MY_PROFILE, payload);
};
