import { NotificationType } from "../schema/notifications";

export interface ISendMail {
    email: string;
    subject: string;
    textContent: string;
    htmlContent: string;
}

export interface IEmailData {
    from: string;
    to: string;
    subject: string;
    text: string;
    html: string;
    attachments?: {
        content: string;
        filename: string;
        disposition: string;
    }[];
}

export interface ICreateNotification {
    type: NotificationType;
    title?: string;
    description?: string;
    relatedId: number;
    additionalInfo?: Record<string, any>;
  }
