import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse, IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { AddEmployeeResponseData, EmployeeInterface } from "@/interfaces/employeeInterface";

/**
 * Add new employees to the system
 * @param employees Array of employee data to add
 * @returns Promise with API response
 */
// Define interface for the transformed employee data that matches backend validation
interface EmployeePayload {
  firstName: string;
  lastName: string;
  email: string;
  departmentId: number;
  roleId: number;
  // interviewOrder: number;
}

export const addEmployees = (employees: EmployeePayload[]): Promise<IApiResponseCommonInterface<AddEmployeeResponseData>> => {
  return http.post(endpoint.employee.ADD_EMPLOYEES, { employees });
};

/**
 * Get employees by department ID
 * @param departmentId ID of the department to fetch employees for
 * @param limit Optional maximum number of records to return
 * @param offset Optional offset for pagination
 * @param search Optional search term
 * @returns Promise with API response containing employee data
 */
export const getEmployeesByDepartment = (
  data: {
    limit?: number;
    offset?: number;
    search?: string;
  },
  departmentId?: number
): Promise<IApiResponseCommonInterface<EmployeeInterface[]>> => {
  return http.get(endpoint.employee.GET_EMPLOYEES_BY_DEPARTMENT, {
    ...data,
    departmentId,
  });
};

/**
 * Update employee role
 * @param employeeId ID of the employee to update
 * @param roleId ID of the new role
 * @returns Promise with API response
 */
export const updateEmployeeRole = (employeeId: number, roleId: number): Promise<ApiResponse> => {
  // Replace the :employeeId placeholder in the URL
  const baseUrl = endpoint.employee.UPDATE_EMPLOYEE_ROLE.replace(":employeeId", employeeId.toString());
  return http.put(baseUrl, {
    roleId,
  });
};

/**
 * Interface for employee order update response
 */
export interface EmployeeOrderUpdateResponse {
  id: number;
  interviewOrder: number;
}

/**
 * Update employee interview order
 * @param employeeId ID of the employee to update
 * @param interviewOrder New interview order value
 * @param departmentId Department ID
 * @returns Promise with API response
 */
export const updateEmployeeInterviewOrder = (
  employeeId: number,
  newInterviewOrder: number,
  departmentId: number
): Promise<IApiResponseCommonInterface<EmployeeOrderUpdateResponse[]>> => {
  // Replace the :employeeId placeholder in the URL
  const baseUrl = endpoint.employee.UPDATE_EMPLOYEE_INTERVIEW_ORDER.replace(":employeeId", employeeId.toString());
  return http.put(baseUrl, {
    newInterviewOrder,
    departmentId,
  });
};

/**
 * Update employee role
 * @param employeeId ID of the employee to update
 * @param roleId ID of the new role
 * @returns Promise with API response
 */
export const updateEmployeeStatus = (employeeId: number, status: boolean): Promise<ApiResponse> => {
  // Replace the :employeeId placeholder in the URL
  const baseUrl = endpoint.employee.UPDATE_EMPLOYEE_STATUS.replace(":employeeId", employeeId.toString());
  return http.put(baseUrl, {
    status,
  });
};
/**
 * Delete an employee from the system
 * @param employeeId ID of the employee to delete
 * @returns Promise with API response
 */
export const deleteEmployee = (employeeId: number): Promise<ApiResponse> => {
  const baseUrl = endpoint.employee.DELETE_EMPLOYEE.replace(":employeeId", employeeId.toString());
  return http.remove(baseUrl);
};
