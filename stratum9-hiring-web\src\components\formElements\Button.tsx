import React, { ButtonHTMLAttributes, DetailedHTMLP<PERSON>, MouseEvent, ReactNode } from "react";
import Loader from "@/components/loader/Loader";

interface Props extends DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {
  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
  disabled?: boolean;
  children?: ReactNode;
  loading?: boolean;
}

const Button = ({ className, type, disabled = false, onClick, children, loading }: Props) => (
  <button
    type={type}
    className={`theme-btn ${className}`}
    onClick={(e) => {
      if (onClick) {
        onClick(e as unknown as MouseEvent<HTMLButtonElement>);
      }
    }}
    disabled={disabled}
    aria-label=""
  >
    {children}
    {loading ? <Loader /> : null}
  </button>
);

export default Button;
