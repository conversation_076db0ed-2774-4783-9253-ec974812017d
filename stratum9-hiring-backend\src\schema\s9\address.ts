import {
  <PERSON><PERSON><PERSON>,
  PrimaryG<PERSON>ated<PERSON><PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
} from "typeorm";
import UserModel from "./user";

export interface IAddress {
  id: number;
  user_id: number;
  occupation: string;
  desired_occupation: string;
  company: string;
  contact_number: string;
  state: string;
  country: string;
  city: string;
  location: string;
  organization_code: string;
  organization_id: number;
}

@Entity("address")
class AddressModel implements IAddress {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column({
    length: 255,
  })
  occupation: string;

  @Column({
    length: 255,
  })
  desired_occupation: string;

  @Column({
    length: 255,
  })
  company: string;

  @Column({ collation: "utf8mb4_bin" }) // Set the collation to case-sensitive
  organization_code: string;

  @Column()
  organization_id: number;

  @Column({
    length: 14,
  })
  contact_number: string;

  @Column({ length: 50 })
  state: string;

  @Column({})
  location: string;

  @Column({ length: 50 })
  country: string;

  @Column({ length: 50 })
  city: string;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  created_ts: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  updated_ts: Date;

  @ManyToOne(() => UserModel, (user) => user.address)
  @JoinColumn({
    name: "user_id",
  })
  user: UserModel[];
}

export default AddressModel;
