import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  UpdateDateColumn,
  CreateDateColumn,
} from "typeorm";

enum Gender {
  MALE = "Male",
  FEMALE = "Female",
}

@Entity("candidates")
export default class CandidatesModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "org_id", nullable: false })
  orgId: number;

  @Column({ length: 50, nullable: false })
  name: string;

  @Column({ length: 50, nullable: false })
  email: string;

  @Column({
    type: "enum",
    enum: Gender,
    nullable: false,
  })
  gender: Gender;

  @Column({ name: "image_url", length: 255, nullable: false })
  imageUrl: string;

  @CreateDateColumn({
    type: "timestamp",
    nullable: false,
    name: "created_ts",
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    nullable: false,
    name: "updated_ts",
  })
  updatedTs: Date;
}
