import { Request, Response } from "express";
import { DEFAULT_LIMIT } from "../../utils/constants";
import CandidateApplicationService from "./services";

// Get activity logs by ord_id with optional logType, limit, offset
const getLogsController = async (req: Request, res: Response) => {
  try {
    const logType = req.query.logType ? String(req.query.logType) : undefined;
    const limit = req.query.limit ? Number(req.query.limit) : DEFAULT_LIMIT;
    const offset = req.query.offset ? Number(req.query.offset) : 0;

    const logs = await CandidateApplicationService.getActivityLogsByOrgId(
      req.orgId,
      logType,
      limit,
      offset
    );

    return res.status(200).json(logs);
  } catch (error) {
    return res
      .status(500)
      .json({ success: false, message: "Internal server error" });
  }
};

export default getLogsController;
