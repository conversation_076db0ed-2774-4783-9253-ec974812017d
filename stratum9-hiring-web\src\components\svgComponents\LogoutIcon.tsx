import React from "react";

function LogoutIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={{ minWidth: "20px", fill: "#fff" }}
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
    >
      <g clipPath="url(#clip0_10037_3401)">
        <path
          d="M18.6667 11.2415V8.57487C18.6667 7.86763 18.3857 7.18935 17.8856 6.68925C17.3855 6.18915 16.7072 5.9082 16 5.9082H6.66667C5.95942 5.9082 5.28115 6.18915 4.78105 6.68925C4.28095 7.18935 4 7.86763 4 8.57487V24.5749C4 25.2821 4.28095 25.9604 4.78105 26.4605C5.28115 26.9606 5.95942 27.2415 6.66667 27.2415H16C16.7072 27.2415 17.3855 26.9606 17.8856 26.4605C18.3857 25.9604 18.6667 25.2821 18.6667 24.5749V21.9082"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M12 16.5762H28L24 12.5762" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M24 20.5762L28 16.5762" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_10037_3401">
          <rect width="32" height="32" fill="white" transform="translate(0 0.271484)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default LogoutIcon;
