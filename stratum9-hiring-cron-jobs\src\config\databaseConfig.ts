import * as Sentry from "@sentry/node";
import { DataSource } from "typeorm";
import { SnakeNamingStrategy } from "typeorm-naming-strategies";

import { getSecretKeys } from "./awsConfig";
import { DB_CONST } from "../utils/constants";
import InterviewModel from "../schema/interview";
import InterviewSkillEvaluationModel from "../schema/interview_skill_evaluations";
import InterviewSkillQuestionsAnswersModel from "../schema/interview_skill_questions_answers";
import CandidatesModel from "../schema/candidates";
import JobApplicationsModel from "../schema/job_applications";
import JobSkillsModel from "../schema/job_skills";
import { JobsModel } from "../schema/jobs";
import SkillsModel from "../schema/skills";
import OrganizationSubscriptionModel from "../schema/organization_subscriptions";
import OrganizationSubscriptionBenefitModel from "../schema/organization_subscription_benefits";
import SubscriptionPlanModel from "../schema/subscription_plans";
import InterviewFeedbackModel from "../schema/interview_feedback";
import FinalAssessmentsModel from "../schema/final_assessments";
import ApplicantAdditionalInfoModel from "../schema/applicant_additional_info";
import UserModel from "../schema/s9/user";
import NotificationsModal from "../schema/notifications";

/**
 * Original database connection details for stratum9_innerview_dev
 * @returns DataSource promise
 */
export const s9InnerviewDbConnectionDetails = async () => {
  const keys = await getSecretKeys();
  console.log("s9InnerviewDbConnectionDetails keys", keys);
  return new Promise((resolve) => {
    try {
      const dataSource = new DataSource({
        type: "mysql",
        host: keys.db_hostname,
        port: 3306,
        username: keys.db_username,
        password: keys.db_password,
        database: keys.s9_hiring_database_name,
        connectTimeout: 30000,
        logging: false,
        extra: {
          charset: DB_CONST.CHARSET,
        },
        timezone: "Z",
        name: DB_CONST.S9_INNERVIEW_NAME,
        entities: [
          InterviewModel,
          InterviewSkillEvaluationModel,
          InterviewSkillQuestionsAnswersModel,
          CandidatesModel,
          JobApplicationsModel,
          JobSkillsModel,
          JobsModel,
          SkillsModel,
          OrganizationSubscriptionModel,
          OrganizationSubscriptionBenefitModel,
          SubscriptionPlanModel,
          InterviewFeedbackModel,
          FinalAssessmentsModel,
          ApplicantAdditionalInfoModel,
          NotificationsModal
        ],
        namingStrategy: new SnakeNamingStrategy(),
        migrations: ["src/migrations/**/*{.ts,.js}"],
        subscribers: ["../src/subscriber/**/*{.ts,.js}"],
      });
      dataSource
        .initialize()
        .then(() => {
          resolve(dataSource);
        })
        .catch((err) => {
          Sentry.captureException(err);
          resolve(undefined);
        });
    } catch (e) {
      console.error("Error initializing database connection:", e);
      Sentry.captureException(e.message);
      resolve(undefined);
    }
  });
};

export const s9MainDbConnectionDetails = async () => {
  const keys = await getSecretKeys();
  return new Promise((resolve) => {
    try {
      const dataSource = new DataSource({
        type: "mysql",
        host: keys.db_hostname,
        port: 3306,
        username: keys.db_username,
        password: keys.db_password,
        database: keys.db_database_name,
        connectTimeout: 30000,
        extra: {
          charset: DB_CONST.CHARSET,
        },
        timezone: "Z",
        name: DB_CONST.S9_NAME,
        entities: [UserModel],
        namingStrategy: new SnakeNamingStrategy(),
        // migrations: ["src/migrations/**/*{.ts,.js}"],
        // subscribers: ["../src/subscriber/**/*{.ts,.js}"],
      });
      dataSource
        .initialize()
        .then(() => {
          resolve(dataSource);
        })
        .catch((err) => {
          Sentry.captureException(err);
          resolve(undefined);
        });
    } catch (e) {
      Sentry.captureException(e.message);
      resolve(undefined);
    }
  });
};