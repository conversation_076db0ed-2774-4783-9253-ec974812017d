/* eslint-disable @typescript-eslint/no-explicit-any */
export type TApiState<T = Record<string, any>> = T | null;

// Base form values
export type FormValues = {
  title: string;
  employment_type: string;
  salary_range: string;
  salary_cycle: string;
  location_type: string;
  state: string;
  city: string;
  role_overview: string;
  responsibilities: string;
  educations_requirement: string;
  skills_and_software_expertise: string;
  experience_required: string;
  ideal_candidate_traits: string;
  about_company: string;
  tone_style: string;
  compliance_statement: string[];
  show_compliance: boolean;
  hiring_type: string;
  experience_level: string;
  // Optional fields
  certifications?: string;
  perks_benefits?: string;
  additional_info?: string;
  department_id: string;
};

// Extended type for extra fields
export type ExtendedFormValues = FormValues & {
  jd_link?: string;
};

declare module "html2pdf.js";
