import { Request, Response } from "express";
import * as Sentry from "@sentry/node";
import UserProfileServices from "./services";

/**
 * Get user profile information including image, name, role, email, organization, department
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getMyProfile = async (req: Request, res: Response) => {
  try {
    const data = await UserProfileServices.getMyProfile(req.userId);

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Update user profile information including first name, last name, and profile image
 *
 * @param {Request} req - The HTTP request object containing update data
 * @param {Response} res - The HTTP response object
 */
export const updateMyProfile = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };
    const result = await UserProfileServices.updateMyProfile({
      userId: req.userId,
      ...body,
    });

    return res.status(result.success ? 200 : 400).json({
      ...result,
      code: result.success ? 200 : 400,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json({
      success: false,
      message: "An error occurred while updating user profile",
      code: error.output?.statusCode ?? 500,
    });
  }
};
