import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ExtendedFormValues } from "@/types/types";
import { initialState } from "@/constants/commonConstants";

export const jobDetailsSlice = createSlice({
  name: "jobDetails",
  initialState,
  reducers: {
    // Set all job details at once
    setJobDetails: (state, action: PayloadAction<ExtendedFormValues>) => {
      return { ...state, ...action.payload };
    },
    // Clear job details
    clearJobDetails: () => {
      return initialState;
    },
    // Update a specific field in job details
    updateJobDetail: <T extends keyof ExtendedFormValues>(
      state: ExtendedFormValues,
      action: PayloadAction<{ field: T; value: ExtendedFormValues[T] }>
    ) => {
      const { field, value } = action.payload;
      state[field] = value;
    },
  },
});

export const { setJobDetails, clearJobDetails, updateJobDetail } = jobDetailsSlice.actions;

// Simple selectors to use directly with useSelector
export const selectJobDetails = (state: { jobDetails: ExtendedFormValues }) => state.jobDetails;

// Export the reducer directly for easier import in the store
export default jobDetailsSlice.reducer;
