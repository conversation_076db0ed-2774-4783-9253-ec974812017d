import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { Role } from "./roles";
import { Permission } from "./permissions";

@Entity("role_permissions_mapping")
export class RolePermission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "role_id" })
  roleId: number;

  @Column({ name: "permission_id" })
  permissionId: number;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;

  // Relationships
  @ManyToOne(() => Role, (role) => role.rolePermissions)
  @JoinColumn({ name: "role_id" })
  @Index()
  role: Role;

  @ManyToOne(() => Permission, (permission) => permission.rolePermissions)
  @JoinColumn({ name: "permission_id" })
  @Index()
  permission: Permission;
}

export default RolePermission;
