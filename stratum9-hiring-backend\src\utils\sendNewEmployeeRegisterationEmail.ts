import * as Sentry from "@sentry/node";
import sendMail from "./sendgrid";

export const EMPLOYEE_TYPE = {
  NEW: "new",
  EXISTING: "existing",
};
/**
 * Sends a registration email to a new employee with their credentials.
 * @param {Object} params
 * @param {string} params.email - Employee's email address
 * @param {string} params.employeeName - Employee's name
 * @param {string} params.organizationName - Organization name
 * @param {string} params.rawPassword - Generated password for the employee
 * @returns {Promise<{response: any}>}
 */
const sendNewEmployeeRegistrationMail = async ({
  email,
  type,
  employeeName,
  organizationName,
  rawPassword,
}: {
  email: string;
  type?: string;
  employeeName: string;
  organizationName: string;
  rawPassword?: string;
}) => {
  try {
    const subject = "🚀 Welcome to S9 InnerView – Your Account Details";

    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Account Notification - S9 InnerView</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f4f4f4;
        padding: 0;
        margin: 0;
      }
      .container {
        max-width: 600px;
        background-color: #ffffff;
        margin: 30px auto;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
      }
      .logo {
        text-align: center;
        margin-bottom: 20px;
      }
      .logo img {
        width: 150px;
      }
      .highlight {
        font-weight: bold;
        color: #436EB6;
      }
      .credentials {
        background-color: #f9f9f9;
        border-left: 4px solid #436EB6;
        padding: 15px;
        margin: 20px 0;
        font-family: monospace;
      }
      .tip {
        margin-top: 20px;
        background-color: #fff3cd;
        padding: 10px 15px;
        border-left: 4px solid #ffc107;
        font-size: 14px;
      }
      .footer {
        margin-top: 30px;
        font-size: 14px;
        color: #555;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">
        <img src="https://stratum9-images-dev.s3-accelerate.amazonaws.com/resources/stratum-logo.png" alt="S9 Logo" />
      </div>
      <h2>Welcome to <span class="highlight">S9 InnerView</span>!</h2>
      <p>Dear ${employeeName || "Employee"} from ${organizationName || "your organization"},</p>

      ${(() => {
        // Using an immediately-invoked function expression (IIFE) to avoid nested ternaries
        if (type === EMPLOYEE_TYPE.NEW) {
          return `
          <p>
            We're thrilled to welcome you to <strong>S9 InnerView</strong>, your dedicated platform for hiring and interview management powered by Stratum 9.
          </p>
          <p>Your user account has been successfully created. Please find your login credentials below:</p>
          <div class="credentials">
            <p><strong>Platform:</strong> S9 InnerView</p>
            <p><strong>Organization:</strong> ${organizationName || "your organization"}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Temporary Password:</strong>  ${rawPassword || "[Contact your administrator]"}</p>
            <p><strong>Login URL:</strong> <a href="https://app.s9innerview.com" target="_blank">https://app.s9innerview.com</a></p>
          </div>
          <div class="tip">
            🔐 <strong>Security Tip:</strong> Please change your password after logging in. Use a strong and secure password to protect your account.
          </div>
          `;
        }

        if (type === EMPLOYEE_TYPE.EXISTING) {
          return `
          <p>
            You are already registered with <strong>S9 InnerView</strong> under the organization <strong>${organizationName || "your organization"}</strong>.
          </p>
          <p>
            You can continue using your existing login credentials to access the platform.
          </p>
          <div class="credentials">
            <p><strong>Platform:</strong> S9 InnerView</p>
            <p><strong>Organization:</strong> ${organizationName || "your organization"}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Login URL:</strong> <a href="https://app.s9innerview.com" target="_blank">https://app.s9innerview.com</a></p>
          </div>
          `;
        }

        // Default case
        return `
        <p>
          Welcome to <strong>S9 InnerView</strong>, your dedicated platform for hiring and interview management powered by Stratum 9.
        </p>
        <div class="credentials">
          <p><strong>Platform:</strong> S9 InnerView</p>
          <p><strong>Organization:</strong> ${organizationName || "your organization"}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Login URL:</strong> <a href="https://app.s9innerview.com" target="_blank">https://app.s9innerview.com</a></p>
        </div>
        `;
      })()}

      <p class="footer">
        Best regards,<br />
        <strong>Stratum 9 Team</strong>
      </p>
    </div>
  </body>
</html>
`;

    const textContent = `
Welcome to S9 InnerView!`;
    const response = await sendMail({
      email: email.toLowerCase(),
      subject,
      textContent,
      htmlContent,
    });
    return { response };
  } catch (error) {
    Sentry.captureException(error);
    return { error: "Employee registration email sending failed" };
  }
};

export default sendNewEmployeeRegistrationMail;
