import envConfig from "../config/envConfig";
import sendMail from "./sendgrid";


export const sendInterviewFeedbackEmail = async ({ interviewerEmail, candidateName, jobTitle, interviewerName, interviewId }) => {

    const config = envConfig();

    const feedbackLink = `${config.s9_public_url}/interview-feedback/${interviewId}`;

    console.log("feedbackLink===>>>", feedbackLink)

    const html = `
    <!DOCTYPE html>
    <html lang="en">

    <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Interview Feedback Ready - S9 InnerView</title>
    </head>

    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5a8; color: #333;">
    <table width="100%" border="0" cellspacing="0" cellpadding="0"
        style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        
        <!-- Logo Section -->
        <tr>
        <td align="center" style="padding: 20px 0 0; background-color: #fff;">
            <img src="./logo-s9.svg" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;" />
        </td>
        </tr>

        <!-- Email Body -->
        <tr>
        <td style="padding: 30px;">
            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Hi <span style="color: #3182ce; font-weight: 600;">${interviewerName}</span>,
            </p>

            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Thank you for interviewing <strong>${candidateName}</strong> for the <strong>${jobTitle}</strong> role.
            </p>

            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            ✨ Access a complete view of the interview – featuring your insights and AI-generated feedback – for a transparent, skill-by-skill evaluation.
            </p>

            <!-- Button: Review Candidate Feedback -->

            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            See how the candidate performed across the skills you explored during the interview.
            </p>

            <!-- Button: View Your Feedback -->
            <p style="text-align: center; margin-bottom: 30px;">
            <a href="${feedbackLink}"
                style="display: inline-block; background-color: #3182ce; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-size: 16px;">
                View/Edit Interview Feedback
            </a>
            </p>

            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Reflect on your interview approach and track your own growth as an interviewer. We’re here to help you become even better.
            </p>

            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            To ensure a smooth and timely hiring process, we request you to review the candidate’s feedback within <strong>24 hours</strong>.
            </p>

            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Your input plays a vital role in shaping our next steps. If you have any questions or face issues accessing the links, please reach out to the team.
            </p>

            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
            Thank you for helping us build better teams – one conversation at a time.
            </p>

            <p style="font-size: 16px; line-height: 1.6; margin: 20px 0 0 0;">
            <strong>Warm regards,</strong><br />
            S9 Innerview Team<br />
            Stratum 9 | Empowering Human Potential
            </p>
        </td>
        </tr>

        <!-- Footer -->
        <tr>
        <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
            © 2025 STRATUM 9. All rights reserved.
        </td>
        </tr>
    </table>
    </body>

    </html>
`
    const subject = "Interview Feedback is Ready – Let’s Keep the Momentum Going"

    await sendMail({
        email: interviewerEmail,
        subject,
        textContent: subject,
        htmlContent: html,
    });

}
