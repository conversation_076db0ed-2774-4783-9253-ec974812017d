/* eslint-disable no-param-reassign */
/* eslint-disable prefer-template */
/* eslint-disable no-nested-ternary */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */

import * as Sen<PERSON> from "@sentry/node";
import { InvokeCommand, LambdaClient } from "@aws-sdk/client-lambda";
import { Brackets, In, Repository } from "typeorm";
import OpenAI from "openai";
import CryptoJS from "crypto-js";

import envConfig from "../../config/envConfig";
import {
  ACTIVITY_LOGS_VALUES,
  ACTIVITY_LOG_TYPE,
  API_RESPONSE_MSG,
  DEFAULT_LIMIT,
  DEFAULT_OFFSET,
  ENTITY_TYPE,
  GPT_MODEL,
  INTERVIEW_EMAIL_TYPE,
  INTERVIEW_QUESTION_TYPE,
  MAX_HOURS_BETWEEN_START_AND_END_TIME,
  ONE_TO_ONE_INTERVIEW_INSTRUCTIONS,
  PERMISSION,
  REDIS_KEYS,
  SCHEDULE_INTERVIEW_MINUTES_MS,
  SCHEDULE_INTERVIEW_ONE_MONTH_MS,
  STRATUM_POINT_DESCRIPTION,
  VIDEO_CALL_INTERVIEW_INSTRUCTIONS,
} from "../../utils/constants";

import InterviewModel, { RoundType } from "../../schema/s9-innerview/interview";
import dbConnection from "../../db/dbConnection";
import {
  IAddInterviewSkillQuestion,
  IGenerateInterviewSkillQuestions,
  IGetCandidateList,
  IGetInterviewSkillQuestions,
  IGetJobList,
  IUpdateInterviewAnswers,
  IUpdateInterviewSkillQuestion,
  ScheduleInterviewData,
  IEndInterview,
  IGenerateFollowUpQuestions,
} from "./interface";
import Employee from "../../schema/s9-innerview/employees";
import UserModel from "../../schema/s9/user";
import JobApplicationsModel, {
  Status,
} from "../../schema/s9-innerview/job_applications";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import { JobsModel } from "../../schema/s9-innerview/jobs";
import InterviewSkillQuestionsAnswersModel, {
  SourceType,
} from "../../schema/s9-innerview/interview_skill_questions_answers";
import JobSkillsModel, {
  SkillType,
} from "../../schema/s9-innerview/job_skills";
import InterviewSkillEvaluationModel from "../../schema/s9-innerview/interview_skill_evaluations";
import { sendInterviewEmail } from "../../utils/interviewEmail";
import AuthServices from "../auth/services";
import NotificationServices from "../notification/services";
import { NotificationType } from "../../schema/s9-innerview/notifications";
import OrganizationModel from "../../schema/s9/organization";
import Cache from "../../db/cache";
import { clientConfig, getSecretKeys } from "../../config/awsConfig";
import AccessManagementServices from "../accessManagement/services";
import InterviewFeedbackModel from "../../schema/s9-innerview/interview_feedback";
import SkillsModel from "../../schema/s9-innerview/skills";
import FinalAssessmentsModel from "../../schema/s9-innerview/final_assessments";
import { addActivityLog } from "../../utils/helper";

const CONFIG = envConfig();

// console.log("CONFIG===>>>", CONFIG);

export class InterviewServices {
  static conductInterviewStaticInformation = async () => {
    try {
      const cache = new Cache();
      const cachedData = await cache.get(
        REDIS_KEYS.CONDUCT_INTERVIEW_INFORMATION
      );
      // console.log("cachedData===>>>", cachedData);
      if (cachedData) {
        return {
          success: true,
          data: JSON.parse(cachedData),
          message: API_RESPONSE_MSG.success,
        };
      }

      const data = {
        oneToOneInterviewInstructions: ONE_TO_ONE_INTERVIEW_INSTRUCTIONS,
        videoCallInterviewInstructions: VIDEO_CALL_INTERVIEW_INSTRUCTIONS,
        stratumDescription: STRATUM_POINT_DESCRIPTION,
      };
      await cache.set(
        REDIS_KEYS.CONDUCT_INTERVIEW_INFORMATION,
        JSON.stringify(data)
      );
      return {
        success: true,
        data,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      return { success: false, message: error.message };
    }
  };

  static getJobList = async ({ orgId, searchString }: IGetJobList) => {
    try {
      const jobRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);

      let query = jobRepo
        .createQueryBuilder("job")
        .where("job.orgId = :orgId", { orgId })
        .andWhere("job.isActive = true")
        .groupBy(
          "job.id, job.job_id, job.title, job.createdTs, job.isActive, job.finalJobDescriptionHtml"
        )
        .select([
          "job.id as value",
          "job.title as label",
          "job.jobId as jobId",
        ]);

      if (searchString && searchString.trim() !== "") {
        const searchTerm = `%${searchString.toLowerCase().trim()}%`;
        query = query
          .andWhere("job.title like :searchTitle", {
            searchTitle: searchTerm,
          })
          .limit(DEFAULT_LIMIT);
      } else {
        query = query.limit(5);
      }

      const jobList = await query.getRawMany();

      const formatterJobList = jobList.map((job) => ({
        value: job.value,
        label: job.label + `(${job.jobId})`,
        jobId: job.jobId,
      }));
      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: formatterJobList,
      };
    } catch (error) {
      console.log("getJobList error ===>>>", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getCandidateList = async ({
    orgId,
    jobId,
    searchString,
  }: IGetCandidateList) => {
    try {
      const jobApplicationRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          JobApplicationsModel
        );

      let query = jobApplicationRepo
        .createQueryBuilder("jobApplication")
        .leftJoinAndSelect("jobApplication.candidate", "candidate")
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.jobId = :jobId", { jobId })
        .andWhere("jobApplication.isActive = true")
        .andWhere("jobApplication.status = 'Approved'")
        .select(["jobApplication.id as value", "candidate.name as label"]);

      if (searchString && searchString.trim() !== "") {
        const searchTerm = `%${searchString.toLowerCase().trim()}%`;
        query = query
          .andWhere("candidate.name like :searchCandidateName", {
            searchCandidateName: searchTerm,
          })
          .limit(DEFAULT_LIMIT);
      } else {
        query = query.limit(5);
      }

      const candidateList = await query.getRawMany();
      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: candidateList,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.log("getCandidateList=======>>>>", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getMyInterviews = async (data: {
    userId: number;
    monthYear: string;
    roleId: number;
    orgId: number;
  }) => {
    try {
      const { monthYear, userId, roleId, orgId } = data;

      const rolePermissions = await AccessManagementServices.getUserPermissions(
        roleId,
        true
      );
      const permission = rolePermissions.data.rolePermissions.includes(
        PERMISSION.VIEW_ALL_SCHEDULED_INTERVIEWS
      );
      console.log("permission=======>>>", permission);

      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);
      const userData = await userRepo.findOneBy({ id: userId });

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const [month, year] = monthYear
        ? monthYear.split("-")
        : [new Date().getMonth() + 1, new Date().getFullYear()];

      const interviewQuery = interviewRepo
        .createQueryBuilder("interview")
        .leftJoinAndSelect("interview.job", "job")
        .leftJoinAndSelect("interview.jobApplication", "jobApplication")
        .leftJoinAndSelect("jobApplication.candidate", "candidate")
        .where(
          "MONTH(CONVERT_TZ(interview.startTime, '+00:00', :timezone)) = :month",
          { month, timezone: userData.time_zone }
        )
        .andWhere(
          "YEAR(CONVERT_TZ(interview.startTime, '+00:00', :timezone)) = :year",
          { year, timezone: userData.time_zone }
        )
        .andWhere("job.isActive = true")
        .andWhere("job.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true");

      if (!permission) {
        interviewQuery.andWhere(
          "(interview.interviewerId = :userId OR interview.scheduledBy = :userId)",
          {
            userId,
          }
        );
      }

      const interviews = await interviewQuery
        .select([
          "interview.id as id",
          "interview.title as title",
          "interview.startTime as start",
          "interview.endTime as end",
          "interview.roundNumber as roundNumber",
          "interview.roundType as roundType",
          "interview.isEnded as isEnded",
          "interview.scheduleAt as scheduleAt",
          "interview.jobApplicationId as jobApplicationId",
          "interview.attachments as attachments",
          "interview.interviewerId as interviewerId",
          "interview.description as description",
          "jobApplication.resumeFile as resumeLink",
          "candidate.name as candidateName",
          "candidate.id as candidateId",
          "interview.channelName as channelName",
          "job.title as jobTitle",
          "job.job_id as jobUniqueId",
          "job.id as jobId",
        ])
        .getRawMany();

      // Collect all unique interviewerIds
      const interviewerIds = [
        ...new Set(interviews.map((i) => i.interviewerId)),
      ];

      // Fetch all users in one query
      const users = await userRepo.find({
        where: { id: In(interviewerIds) },
        select: ["id", "first_name", "last_name"],
      });

      console.log("getInterviews users===>>>", users);

      // Map users by id for quick lookup
      const userMap = new Map(
        users.map((user) => [user.id, `${user.first_name} ${user.last_name}`])
      );

      // Merge user info into interviews
      const interviewsInfo = interviews.map((interview) => {
        const userName = userMap.get(+interview.interviewerId);

        return {
          ...interview,
          interviewerName: userName || "",
        };
      });

      return {
        success: true,
        message: API_RESPONSE_MSG.interviews_fetched,
        data: interviewsInfo,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getInterviewers = async (data: {
    orgId: number;
    jobId: number;
    searchString: string;
  }) => {
    const { orgId, jobId, searchString } = data;

    console.log("data===>>>>", data);
    try {
      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);
      const employeeRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(Employee);

      const jobRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);

      const job = await jobRepo.findOne({ where: { id: +jobId } });

      if (!job) {
        return {
          success: false,
          message: API_RESPONSE_MSG.job_not_found,
        };
      }

      console.log(job?.departmentId, "searchString===>>>", searchString);

      const employeesWithRoles = await employeeRepo
        .createQueryBuilder("employee")
        .leftJoinAndSelect("employee.role", "role")
        .where("employee.organizationId = :orgId", { orgId })
        .orderBy({
          "CASE WHEN employee.departmentId = :departmentId THEN 0 ELSE 1 END":
            "ASC",
          "employee.interviewOrder": "ASC",
        })
        .setParameter("departmentId", job?.departmentId)
        .select(["employee.userId as userId", "role.name as roleName"])
        .getRawMany();

      console.log("employeesWithRoles===>>>", employeesWithRoles);

      if (employeesWithRoles.length === 0) {
        return {
          success: true,
          message: API_RESPONSE_MSG.interviewers_fetched,
          data: [],
        };
      }

      const employeeUserIdsList = employeesWithRoles.map(
        (employee) => employee.userId
      );

      console.log("employeeUserIdsList===>>>", employeeUserIdsList);

      const userIdToRoleMap = new Map(
        employeesWithRoles.map((row) => [row.userId, row.roleName])
      );

      console.log("userIdToRoleMap===>>>", userIdToRoleMap);

      let query = userRepo
        .createQueryBuilder("")
        .select(["id", "first_name", "last_name", "email"])
        .where("id IN (:...userIds)", { userIds: employeeUserIdsList });

      if (searchString && searchString.trim() !== "") {
        const searchTerm = `%${searchString.toLowerCase().trim()}%`;
        query = query.andWhere(
          "CONCAT(first_name, ' ', last_name) like :searchFullName",
          {
            searchFullName: searchTerm,
          }
        );
      }

      // Get all matching users first
      const allUsers = await query.getRawMany();

      // Sort users based on the employeeUserIdsList order
      const sortedUsers = employeeUserIdsList
        .map((userId) => allUsers.find((user) => +user.id === +userId))
        .filter(Boolean); // Remove any undefined entries

      // Apply limit after sorting
      const users = sortedUsers.slice(
        0,
        searchString && searchString.trim() !== "" ? 10 : 5
      );

      console.log("users===>>>", users);

      const filteredUsers = users.map((user) => ({
        label: `${user.first_name} ${user.last_name} (${userIdToRoleMap.get(+user.id) || ""})`,
        value: user.id,
      }));

      return {
        success: true,
        message: API_RESPONSE_MSG.interviewers_fetched,
        data: filteredUsers,
      };
    } catch (error) {
      console.log("getInterviewers error===>>>", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static async prepareAndSendInterviewEmail({
    jobTitle,
    orgId,
    candidateInfo,
    interviewerId,
    startTimeDate,
    endTimeDate,
    roundType,
    roundNumber,
    fileUrlArray,
    type,
    previousDate = null,
    previousInterviewerName = null,
    previousInterviewerEmail = null,
    meetingJoinLink = "",
  }: {
    jobTitle: string;
    candidateInfo: { name: string; email: string };
    interviewerId: number;
    orgId: number;
    startTimeDate: Date;
    endTimeDate: Date;
    roundType: string;
    roundNumber: number;
    fileUrlArray: string;
    type: string;
    previousDate?: Date;
    previousInterviewerName?: string;
    previousInterviewerEmail?: string;
    meetingJoinLink?: string;
  }) {
    try {
      const orgRepo =
        await dbConnection.getS9DatabaseRepository(OrganizationModel);
      const orgInfo = await orgRepo.findOne({
        where: { id: orgId },
        select: ["name"],
      });

      const interviewerInfo = await AuthServices.getUserByUserId(interviewerId);

      console.log("interviewerEmail=====>>>>>", interviewerInfo.email);
      console.log("candidateEmail====>>>>>>", candidateInfo.email);

      console.log("startTimeDate @@@@@@@@@@@@@@@@@@@@", startTimeDate);
      console.log("endTimeDate @@@@@@@@@@@@@@@@@@@@", endTimeDate);
      console.log("fileUrlArray @@@@@@@@@@@@@@@@@@@@", fileUrlArray);
      const emailParams = {
        interviewerEmail: interviewerInfo.email,
        candidateEmail: candidateInfo.email,
        candidateName: candidateInfo.name,
        position: jobTitle,
        interviewType: roundType,
        date: startTimeDate,
        duration:
          (new Date(endTimeDate).getTime() -
            new Date(startTimeDate).getTime()) /
          (1000 * 60),
        interviewRound: roundNumber,
        meetingLink: meetingJoinLink,
        resume: fileUrlArray?.length ? JSON.parse(fileUrlArray)[0] : "",
        interviewerName: `${interviewerInfo.first_name} ${interviewerInfo.last_name}`,
        orgName: orgInfo.name,
        type,
        previousDate,
        previousInterviewerName,
        previousInterviewerEmail,
      };

      await sendInterviewEmail(emailParams);

      return { success: true, message: API_RESPONSE_MSG.success };
    } catch (error) {
      console.error("Error sending interview email:", error);
      Sentry.captureException(error);
      return { success: false, message: API_RESPONSE_MSG.failed };
    }
  }

  static overlappingInterviews = async (
    jobApplicationId: number,
    jobId: number,
    roundType: string,
    startTime: Date,
    endTime: Date,
    interviewId?: number
  ) => {
    const interviewRepo =
      await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

    const query = interviewRepo
      .createQueryBuilder("interview")
      .andWhere("interview.jobApplicationId = :jobApplicationId", {
        jobApplicationId,
      })
      .andWhere("interview.jobId = :jobId", { jobId })
      .andWhere("interview.roundType = :roundType", { roundType })
      .andWhere(
        "((:startTime BETWEEN interview.startTime AND interview.endTime) OR " +
          "(:endTime BETWEEN interview.startTime AND interview.endTime) OR " +
          "(interview.startTime <= :startTime AND interview.endTime >= :endTime))",
        { startTime, endTime }
      );

    // Exclude the current interview when updating
    if (interviewId) {
      query.andWhere("interview.id != :interviewId", { interviewId });
    }

    const overlappingInterviewsCount = await query.getCount();
    return overlappingInterviewsCount;
  };

  static generateMeetingChannelNameAndLink = async ({
    title,
    interviewId,
    jobApplicationId,
    interviewerId,
    candidateName,
    candidateId,
  }) => {
    const channelName = `interview_${title?.split(" ").join("_")}_${Math.floor(100000 + Math.random() * 900000)}`;

    const keys = await getSecretKeys();
    const interviewer = await AuthServices.getUserByUserId(interviewerId);

    const interviewerName = interviewer.first_name
      ? interviewer.first_name
      : "" + interviewer.last_name
        ? interviewer.last_name
        : "";

    const meetingJoinData = {
      interviewId,
      jobApplicationId,
      channelName,
      candidateId,
      candidateName,
      interviewerName,
    };

    console.log("meetingJoinData=====>>>>>", meetingJoinData);

    const encoded = CryptoJS.AES.encrypt(
      JSON.stringify(meetingJoinData),
      keys.agora_meeting_link_encryption_key
    ).toString();

    const encodedParams = encodeURIComponent(encoded);

    const meetingJoinLink = `${CONFIG.s9_public_url}/candidate-join?params=${encodedParams}`;

    return { channelName, meetingJoinLink };
  };

  static updateOrScheduleInterview = async (
    data: ScheduleInterviewData,
    userId: number
  ) => {
    const {
      title,
      jobId,
      interviewerId,
      jobApplicationId,
      scheduleAt,
      startTime,
      endTime,
      roundType,
      description,
      fileUrlArray,
      interviewId,
    } = data;
    console.log("data===>>>>", data);
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const jobApplicationRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          JobApplicationsModel
        );
      const finalAssessmentRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          FinalAssessmentsModel
        );

      const jobApplication = await jobApplicationRepo.findOne({
        where: { id: +jobApplicationId },
        relations: ["candidate"],
      });

      if (!jobApplication || jobApplication.status !== Status.APPROVED) {
        return {
          success: false,
          message: API_RESPONSE_MSG.job_application_is_not_approved,
        };
      }

      const finalAssessment = await finalAssessmentRepo.findOne({
        where: { jobApplicationId: +jobApplicationId },
      });

      if (finalAssessment && Object.keys(finalAssessment).length > 0) {
        return {
          success: false,
          message:
            API_RESPONSE_MSG.cannot_schedule_interview_once_final_assessment_is_generated,
        };
      }

      const scheduledAtDate = new Date(scheduleAt);
      const startTimeDate = new Date(startTime);
      console.log("startTimeDate", startTimeDate);
      const endTimeDate = new Date(endTime);
      console.log("endTimeDate", endTimeDate);

      const now = new Date().getTime();
      if (startTimeDate.getTime() < now || endTimeDate.getTime() < now) {
        return {
          success: false,
          message: API_RESPONSE_MSG.cannot_schedule_interview_past,
        };
      }

      // Check if end time is after start time
      if (endTimeDate.getTime() <= startTimeDate.getTime()) {
        return {
          success: false,
          message: API_RESPONSE_MSG.end_time_must_be_after_start_time,
        };
      }

      // Check if interview is scheduled more than one month in advance
      if (startTimeDate.getTime() > now + SCHEDULE_INTERVIEW_ONE_MONTH_MS) {
        return {
          success: false,
          message:
            API_RESPONSE_MSG.cannot_schedule_more_than_one_month_in_advance,
        };
      }

      // Check if there is at least 10 minutes between start and end time
      if (
        endTimeDate.getTime() - startTimeDate.getTime() <
        SCHEDULE_INTERVIEW_MINUTES_MS
      ) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_must_be_at_least_10_min,
        };
      }

      // add max of 2 hours between start and end time
      if (
        endTimeDate.getTime() - startTimeDate.getTime() >
        MAX_HOURS_BETWEEN_START_AND_END_TIME
      ) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_must_not_exceed_2_hours,
        };
      }

      const jobRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);
      const job = await jobRepo.findOne({
        where: { id: jobApplication.jobId },
        select: ["title", "orgId"],
      });

      const candidateRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(CandidatesModel);
      const candidateInfo = await candidateRepo.findOne({
        where: { id: +jobApplication.candidateId },
        select: ["name", "email"],
      });

      if (interviewId) {
        // Update existing interview
        const interview = await interviewRepo.findOne({
          where: { id: +interviewId },
        });

        console.log(interview.isEnded, "interview===>>>", interview);

        if (!interview) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_not_found,
          };
        }

        if (interview.isEnded) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_is_ended,
          };
        }

        const overlappingInterviewsCount = await this.overlappingInterviews(
          interview.jobApplicationId,
          interview.jobId,
          roundType,
          startTimeDate,
          endTimeDate,
          interview.id
        );

        if (overlappingInterviewsCount > 0) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_already_scheduled,
          };
        }

        const previousInterviewerInfo = await AuthServices.getUserByUserId(
          interview.interviewerId
        );

        // helper agora video call implementation
        let meetingJoinLink = "";
        let channelName = "";

        if (
          roundType === RoundType.VIDEO_CALL &&
          interview.roundType === RoundType.ONE_ON_ONE
        ) {
          const response = await this.generateMeetingChannelNameAndLink({
            interviewId: interview.id,
            jobApplicationId,
            candidateId: jobApplication.candidateId,
            candidateName: jobApplication.candidate.name,
            interviewerId: interview.interviewerId,
            title,
          });

          meetingJoinLink = response.meetingJoinLink;
          channelName = response.channelName;

          console.log("meetingJoinLink===>>>", meetingJoinLink);
          console.log("channelName===>>>", channelName);
        }

        // Update interview properties
        interview.title = title;
        interview.interviewerId = +interviewerId;
        interview.roundType = roundType;
        interview.scheduleAt = scheduledAtDate;
        interview.startTime = startTimeDate;
        interview.endTime = endTimeDate;
        interview.description = description;
        interview.attachments = fileUrlArray
          ? { fileUrls: JSON.parse(fileUrlArray) }
          : null;
        interview.channelName = channelName;
        interview.updatedTs = new Date();

        const updatedInterview = await interviewRepo.save(interview);
        console.log("updatedInterview====", updatedInterview);

        await this.prepareAndSendInterviewEmail({
          jobTitle: job.title,
          orgId: job.orgId,
          candidateInfo,
          interviewerId,
          startTimeDate,
          endTimeDate,
          roundType,
          roundNumber: interview.roundNumber,
          fileUrlArray,
          type: INTERVIEW_EMAIL_TYPE.UPDATE,
          previousDate: interview.startTime,
          previousInterviewerName: `${previousInterviewerInfo.first_name} ${previousInterviewerInfo.last_name}`,
          previousInterviewerEmail: previousInterviewerInfo.email,
          meetingJoinLink,
        });

        return {
          success: true,
          data: updatedInterview,
          message: API_RESPONSE_MSG.interview_updated_successfully,
        };
      }

      // proceed with schedule interview
      const existingInterviewInfo = await interviewRepo.findOne({
        where: {
          jobId: +jobId,
          jobApplicationId: +jobApplicationId,
        },
        order: {
          id: "DESC",
        },
      });

      console.log("existingInterviewInfo===>>>", existingInterviewInfo);

      if (existingInterviewInfo && !existingInterviewInfo.isEnded) {
        return {
          success: false,
          message: API_RESPONSE_MSG.previously_scheduled_interview_is_not_ended,
        };
      }

      if (existingInterviewInfo && !existingInterviewInfo.isFeedbackFilled) {
        return {
          success: false,
          message:
            API_RESPONSE_MSG.interview_feedback_is_not_filled_by_interviewer_yet,
        };
      }

      if (
        existingInterviewInfo &&
        !existingInterviewInfo.isAllowedForNextRound
      ) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_is_not_allowed_for_next_round,
        };
      }

      console.log("scheduleDateUTC===>>>", scheduledAtDate);
      console.log("startTimeUTC===>>>", startTimeDate);
      console.log("endTimeUTC===>>>", endTimeDate);

      const overlappingInterviewsCount = await this.overlappingInterviews(
        jobApplicationId,
        jobId,
        roundType,
        startTimeDate,
        endTimeDate
      );

      if (overlappingInterviewsCount > 0) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_already_scheduled,
        };
      }

      const roundNumber = existingInterviewInfo?.roundNumber
        ? Number(existingInterviewInfo.roundNumber) + 1
        : 1;

      console.log("roundNumber===>>>", roundNumber);

      const interview = new InterviewModel();
      interview.title = title;
      interview.jobId = +jobId;
      interview.interviewerId = +interviewerId;
      interview.jobApplicationId = +jobApplicationId;
      interview.scheduleAt = scheduledAtDate;
      interview.startTime = startTimeDate;
      interview.endTime = endTimeDate;
      interview.roundType = roundType;
      interview.roundNumber = roundNumber;
      interview.description = description;
      interview.scheduledBy = +userId;
      interview.attachments = fileUrlArray
        ? { fileUrls: JSON.parse(fileUrlArray) }
        : null;

      const result = await interviewRepo.save(interview);
      console.log("result===>>>", result);
      // helper agora video call implementation
      let meetingJoinLink = "";

      if (roundType === RoundType.VIDEO_CALL) {
        const response = await this.generateMeetingChannelNameAndLink({
          interviewId: result.id,
          jobApplicationId,
          candidateId: jobApplication.candidateId,
          candidateName: jobApplication.candidate.name,
          interviewerId: interview.interviewerId,
          title,
        });

        meetingJoinLink = response.meetingJoinLink;

        await interviewRepo.update(result.id, {
          channelName: response.channelName,
        });
        console.log("meetingJoinLink===>>>", meetingJoinLink);
      }

      // Add activity log after scheduling interview
      await addActivityLog({
        orgId: job.orgId,
        logType: ACTIVITY_LOG_TYPE.SCHEDULE_INTERVIEW,
        userId,
        entityId: interview.id,
        entityType: ENTITY_TYPE.INTERVIEW,
        oldValue: null,
        newValue: ACTIVITY_LOGS_VALUES,
        comments: `Scheduled an interview for ${candidateInfo.name} for the ${job.title} position.`,
      });

      await this.prepareAndSendInterviewEmail({
        jobTitle: job.title,
        orgId: job.orgId,
        candidateInfo,
        interviewerId,
        startTimeDate,
        endTimeDate,
        roundType,
        roundNumber,
        fileUrlArray,
        type: INTERVIEW_EMAIL_TYPE.SCHEDULE,
        meetingJoinLink,
      });

      await NotificationServices.createNotification(
        candidateInfo.orgId,
        interviewerId,
        {
          type: NotificationType.INTERVIEW_SCHEDULED,
          title: NotificationType.INTERVIEW_SCHEDULED,
          description: `Interview scheduled for ${candidateInfo.name} for ${job?.title}.`,
          relatedId: interview.id,
        }
      );

      await this.generateInterviewSkillQuestions({
        jobId,
        jobApplicationId,
        roundNumber,
        interviewId: interview.id,
      });

      return {
        success: true,
        message: API_RESPONSE_MSG.interview_scheduled_successfully,
      };
    } catch (error) {
      console.log("scheduleInterview error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getInterviewSkillQuestions = async (
    data: IGetInterviewSkillQuestions
  ) => {
    console.log("data===>>>", data);
    try {
      const { jobApplicationId, interviewId } = data;

      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interviewData = await interviewRepo.find({
        where: { jobApplicationId },
        select: ["id", "hardSkillMarks", "interviewerId"],
      });

      // find the current interview
      const interview = interviewData?.find((i) => i.id === interviewId);

      const interviewIds = [];
      const interviewerIds = new Set<number>();

      interviewData?.forEach((interview_) => {
        interviewIds.push(interview_.id);
        interviewerIds.add(interview_.interviewerId);
      });

      const interviewSkillEvaluationRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillEvaluationModel
        );

      const interviewSkillEvaluation = await interviewSkillEvaluationRepo.find({
        where: { interviewId: In(interviewIds) },
        select: ["jobSkillId", "skillMarks", "locked"],
      });

      console.log("interviewSkillEvaluation===>>>", interviewSkillEvaluation);

      const interviewSkillEvaluationMap = new Map();
      interviewSkillEvaluation.forEach((evaluation) => {
        interviewSkillEvaluationMap.set(evaluation.jobSkillId, {
          skillMarks: evaluation.skillMarks,
          locked: evaluation.locked,
        });
      });

      console.log(
        "interviewSkillEvaluationMap===>>>",
        interviewSkillEvaluationMap
      );

      const response = await interviewSkillQuestionsAnswersRepo
        .createQueryBuilder("isqa")
        .leftJoinAndSelect("isqa.jobSkill", "jobSkill")
        .leftJoinAndSelect("jobSkill.skill", "skill")
        .leftJoinAndSelect(
          "interview_skill_evaluations",
          "ise",
          "ise.jobSkillId = jobSkill.id AND ise.interviewId IN (:...interviewIds)",
          { interviewIds }
        )
        .where(
          "isqa.jobApplicationId = :jobApplicationId AND isqa.interviewId = :interviewId AND (jobSkill.type = :type OR jobSkill.type IS NULL)",
          { jobApplicationId, interviewId, type: SkillType.CAREER_BASED }
        )
        .orWhere(
          "isqa.jobApplicationId = :jobApplicationId AND jobSkill.type IN (:...types)",
          {
            jobApplicationId,
            interviewId,
            types: [SkillType.ROLE_SPECIFIC, SkillType.CULTURE_SPECIFIC],
          }
        )
        .select([
          "isqa.id as id",
          "isqa.jobSkillId as jobSkillId",
          "isqa.interviewId as interviewId",
          "isqa.jobApplicationId as jobApplicationId",
          "isqa.question as question",
          "isqa.answer as answer",
          "jobSkill.type as questionType",
          "jobSkill.skillId as skillId",
          "skill.title as skillTitle",
          "ise.interviewerId as interviewerId",
          "isqa.createdTs as createdTs",
          "isqa.updatedTs as updatedTs",
        ])
        .getRawMany();

      // console.log("response===>>>", response);

      const careerBasedSkillsQuestions = response.filter(
        (skill) => skill.interviewId === +interviewId
      );
      const roleSpecificSkillsQuestions = response.filter(
        (skill) =>
          skill.interviewId !== +interviewId &&
          skill.questionType === SkillType.ROLE_SPECIFIC
      );
      const cultureSpecificSkillsQuestions = response.filter(
        (skill) =>
          skill.interviewId !== +interviewId &&
          skill.questionType === SkillType.CULTURE_SPECIFIC
      );

      console.log("interviewerIds===>>>", interviewerIds);

      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);
      const users = await userRepo.find({
        where: { id: In([...interviewerIds]) },
        select: ["id", "first_name", "last_name"],
      });
      console.log("users===>>>", users);

      const userMap = new Map();
      users.forEach((user) => {
        userMap.set(
          user.id,
          `${user.first_name.charAt(0).toUpperCase()}${user.last_name.charAt(0).toUpperCase()}`
        );
      });
      console.log("userMap===>>>", userMap);

      // Group role specific questions by skill title
      const formattedRoleSpecificSkillsQuestionsGroupedBySkill =
        roleSpecificSkillsQuestions.reduce((acc, item) => {
          if (!acc[item.skillTitle]) {
            acc[item.skillTitle] = {
              questions: [],
              score: interviewSkillEvaluationMap.get(item.jobSkillId)
                ?.skillMarks,
              isLocked: interviewSkillEvaluationMap.get(item.jobSkillId)
                ?.locked,
              interviewerName: userMap.get(item.interviewerId) || "",
            };
          }
          acc[item.skillTitle].questions.push(item);
          return acc;
        }, {});

      // Group culture specific questions by skill title
      const formattedCultureSpecificSkillsQuestionsGroupedBySkill =
        cultureSpecificSkillsQuestions.reduce((acc, item) => {
          if (!acc[item.skillTitle]) {
            acc[item.skillTitle] = {
              questions: [],
              score: interviewSkillEvaluationMap.get(item.jobSkillId)
                ?.skillMarks,
              isLocked: interviewSkillEvaluationMap.get(item.jobSkillId)
                ?.locked,
              interviewerName: userMap.get(item.interviewerId) || "",
            };
          }
          acc[item.skillTitle].questions.push(item);
          return acc;
        }, {});

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: {
          roleSpecificQuestions:
            formattedRoleSpecificSkillsQuestionsGroupedBySkill,
          cultureSpecificQuestions:
            formattedCultureSpecificSkillsQuestionsGroupedBySkill,
          careerBasedQuestions: {
            questions: careerBasedSkillsQuestions,
            score: interview?.hardSkillMarks || 0,
          },
        },
      };
    } catch (error) {
      console.log("getInterviewSkillQuestionsAnswers error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static updateInterviewSkillQuestion = async (
    data: IUpdateInterviewSkillQuestion
  ) => {
    console.log("data===>>>", data);
    try {
      const { interviewQuestionId, question, interviewId } = data;

      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );

      const interviewSkillQuestion =
        await interviewSkillQuestionsAnswersRepo.findOne({
          where: { id: interviewQuestionId },
        });

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const interview = await interviewRepo.findOne({
        where: { id: interviewId },
      });

      console.log(interview.isEnded, "interview===>>>", interview);

      if (interview.isEnded) {
        return {
          success: false,
          message: API_RESPONSE_MSG.cannot_update_interview_after_ended,
        };
      }

      if (!interviewSkillQuestion) {
        return {
          success: false,
          message: API_RESPONSE_MSG.question_not_found,
        };
      }

      interviewSkillQuestion.question = question;
      interviewSkillQuestion.updatedTs = new Date();

      await interviewSkillQuestionsAnswersRepo.save(interviewSkillQuestion);

      return {
        success: true,
        message: API_RESPONSE_MSG.interview_question_updated,
      };
    } catch (error) {
      console.log("updateInterviewSkillQuestion error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static addInterviewSkillQuestion = async (
    data: IAddInterviewSkillQuestion
  ) => {
    try {
      const {
        jobApplicationId,
        interviewId,
        question,
        skillType,
        jobSkillId,
        questionType,
      } = data;

      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const interview = await interviewRepo.findOne({
        where: { id: interviewId },
      });

      if (interview.isEnded) {
        return {
          success: false,
          message:
            API_RESPONSE_MSG.cannot_add_interview_skill_question_after_ended,
        };
      }

      if (questionType !== INTERVIEW_QUESTION_TYPE.FOLLOW_UP) {
        // Check if maximum questions limit is reached based on skill type
        if (skillType === SkillType.CAREER_BASED) {
          // Count existing career based questions for this interview
          const careerBasedCount =
            await interviewSkillQuestionsAnswersRepo.count({
              where: {
                interviewId,
                jobApplicationId,
              },
            });

          console.log("careerBasedCount===>>>", careerBasedCount);

          if (careerBasedCount >= 8) {
            return {
              success: false,
              message: API_RESPONSE_MSG.max_career_based_questions_reached,
            };
          }
        } else {
          // For role or culture specific questions, validate jobSkillId
          if (!jobSkillId) {
            return {
              success: false,
              message: API_RESPONSE_MSG.invalid_data,
            };
          }

          // Count existing questions for this specific job skill
          const skillSpecificCount =
            await interviewSkillQuestionsAnswersRepo.count({
              where: {
                jobSkillId,
                jobApplicationId,
              },
            });

          console.log("skillSpecificCount===>>>", skillSpecificCount);

          // Check limit based on skill type
          if (
            (skillType === SkillType.ROLE_SPECIFIC ||
              skillType === SkillType.CULTURE_SPECIFIC) &&
            skillSpecificCount >= 6
          ) {
            return {
              success: false,
              message:
                skillType === SkillType.ROLE_SPECIFIC
                  ? API_RESPONSE_MSG.max_role_specific_questions_reached
                  : API_RESPONSE_MSG.max_culture_specific_questions_reached,
            };
          }
        }
      }

      const interviewSkillQuestion = new InterviewSkillQuestionsAnswersModel();
      interviewSkillQuestion.jobApplicationId = jobApplicationId;
      interviewSkillQuestion.interviewId =
        skillType === SkillType.CAREER_BASED ? interviewId : null;
      interviewSkillQuestion.jobSkillId =
        skillType === SkillType.CAREER_BASED ? null : jobSkillId;
      interviewSkillQuestion.question = question;
      interviewSkillQuestion.source = SourceType.MANUAL;

      const savedQuestion = await interviewSkillQuestionsAnswersRepo.save(
        interviewSkillQuestion
      );

      let questionData = {};
      if (skillType === SkillType.CAREER_BASED) {
        questionData = {
          id: savedQuestion.id,
          jobSkillId: savedQuestion.jobSkillId,
          interviewId,
          jobApplicationId,
          question,
          answer: "",
          questionType: SkillType.CAREER_BASED,
          interviewerId: interview.interviewerId,
          createdTs: savedQuestion.createdTs,
          updatedTs: savedQuestion.updatedTs,
        };
      } else {
        const jobSkillRepo =
          await dbConnection.getS9InnerViewDatabaseRepository(JobSkillsModel);
        const jobSkill = await jobSkillRepo.findOne({
          where: { id: jobSkillId },
          relations: ["skill"],
        });

        questionData = {
          id: savedQuestion.id,
          jobSkillId: savedQuestion.jobSkillId,
          interviewId,
          jobApplicationId,
          question,
          answer: "",
          questionType: jobSkill.type,
          interviewerId: interview.interviewerId,
          skillId: jobSkill.skillId,
          skillTitle: jobSkill.skill?.title || "",
          createdTs: savedQuestion.createdTs,
          updatedTs: savedQuestion.updatedTs,
        };
      }

      return {
        success: true,
        message: API_RESPONSE_MSG.interview_question_added,
        data: questionData,
      };
    } catch (error) {
      console.log("addInterviewSkillQuestion error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static updateInterviewAnswers = async (
    data: IUpdateInterviewAnswers,
    userId: number
  ) => {
    try {
      const {
        interviewId,
        skillMarked,
        jobSkillId,
        skillId,
        skillType,
        answers,
      } = data;

      console.log("data===>>>", data);
      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interview = await interviewRepo.findOne({
        where: {
          id: interviewId,
        },
      });

      if (interview?.isEnded) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_is_ended,
        };
      }

      if (interview.interviewerId !== userId) {
        return {
          success: false,
          message: API_RESPONSE_MSG.unauthorized,
        };
      }

      if (answers.length > 0) {
        // Create a query builder for bulk updates
        console.log("inside length check");

        // Process all answers in a single transaction
        await interviewSkillQuestionsAnswersRepo.manager.transaction(
          async (transactionalEntityManager) => {
            const updatePromises = answers.map(
              ({ questionId, answer }) =>
                answer?.length &&
                transactionalEntityManager
                  .createQueryBuilder()
                  .update(InterviewSkillQuestionsAnswersModel)
                  .set({
                    answer,
                    updatedTs: new Date(),
                  })
                  .where("id = :questionId", { questionId })
                  .execute()
            );

            // Execute all updates in parallel within the transaction
            await Promise.all(updatePromises);
          }
        );
      }

      if (skillType === SkillType.CAREER_BASED) {
        const res = await interviewRepo.update(interviewId, {
          hardSkillMarks: skillMarked,
          updatedTs: new Date(),
        });
        console.log("update res===>>>", res);
      } else {
        const interviewSkillEvaluationRepo =
          await dbConnection.getS9InnerViewDatabaseRepository(
            InterviewSkillEvaluationModel
          );

        const isEvaluationAlreadyExist =
          await interviewSkillEvaluationRepo.findOne({
            where: {
              interviewId,
              jobSkillId,
            },
          });

        console.log("isEvaluationAlreadyExist===>>>", isEvaluationAlreadyExist);

        if (isEvaluationAlreadyExist?.locked) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_already_ended,
          };
        }

        if (isEvaluationAlreadyExist) {
          await interviewSkillEvaluationRepo.update(
            isEvaluationAlreadyExist.id,
            {
              skillMarks: skillMarked,
              updatedTs: new Date(),
            }
          );
        } else {
          const newInterviewSkillEvaluation =
            new InterviewSkillEvaluationModel();
          newInterviewSkillEvaluation.interviewerId = userId;
          newInterviewSkillEvaluation.interviewId = interviewId;
          newInterviewSkillEvaluation.skillMarks = skillMarked;
          newInterviewSkillEvaluation.jobSkillId = jobSkillId;
          newInterviewSkillEvaluation.skillId = skillId;
          await interviewSkillEvaluationRepo.save(newInterviewSkillEvaluation);
        }
      }

      return {
        success: true,
        code: 200,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.error("updateInterviewQuestionAnswers error ===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        code: 500,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static generateInterviewSkillQuestions = async (
    data: IGenerateInterviewSkillQuestions
  ) => {
    console.log("data===>>>", data);
    try {
      const { jobId, jobApplicationId, roundNumber, interviewId } = data;

      const lambdaClient = new LambdaClient({
        ...clientConfig,
      });

      const command = new InvokeCommand({
        FunctionName: CONFIG.generate_interview_questions_lambda,
        Payload: JSON.stringify({
          jobId,
          jobApplicationId,
          roundNumber,
          interviewId,
        }),
      });

      await lambdaClient
        .send(command)
        .then((res) => {
          console.log("lamda response===>>>", res);
        })
        .catch((err) => {
          console.log("lamda error===>>>", err);
        });

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.log("generateInterviewSkillQuestionsAnswers error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static generateFollowUpQuestions = async (
    data: IGenerateFollowUpQuestions
  ) => {
    try {
      const { jobSkillId, type, interviewId, jobId } = data;

      const cache = new Cache();

      const transcripts = await cache.get(
        `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`
      );

      console.log("transcripts===>>>", transcripts);

      // Extract only the most recent portion of the transcript
      // Average speaking rate: ~150 words per minute, ~5 chars per word = ~750 chars/min
      // For a 5-minute interval: ~3,750 characters
      // We are taking 2000 characters to be on the safe side
      const CHARS_PER_FIVE_MINUTES = 2000;

      let recentTranscript = "";
      if (transcripts && typeof transcripts === "string") {
        // If transcript is longer than our limit, take only the most recent portion
        if (transcripts.length > CHARS_PER_FIVE_MINUTES) {
          recentTranscript = transcripts.slice(-CHARS_PER_FIVE_MINUTES);

          // Try to start at a word boundary if possible
          const firstSpaceIndex = recentTranscript.indexOf(" ");
          if (firstSpaceIndex > 0) {
            recentTranscript = recentTranscript.substring(firstSpaceIndex + 1);
          }
        } else {
          // If transcript is shorter than our limit, use the entire transcript
          recentTranscript = transcripts;
        }
      }

      console.log(
        "Using recent transcript portion (chars):",
        recentTranscript.length
      );

      const jobRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);
      const job = await jobRepo.findOne({
        where: { id: jobId },
        select: ["title"],
      });

      console.log("job===>>>", job);

      const jobSkillsRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobSkillsModel);
      const jobSkill = await jobSkillsRepo
        .createQueryBuilder("jobSkill")
        .leftJoinAndSelect("jobSkill.skill", "skill")
        .where("jobSkill.id = :jobSkillId", { jobSkillId })
        .select(["skill.title", "skill.shortDescription"])
        .getRawOne();

      console.log("jobSkill===", jobSkill);

      const keys = await getSecretKeys();
      const openAiClient = new OpenAI({ apiKey: keys.openai_api_key });

      const response = await openAiClient.chat.completions.create({
        model: GPT_MODEL,
        messages: [
          {
            role: "system",
            content: `You are an expert HR interviewer and question generator. Generate professional, insightful interview questions that assess candidates effectively.
          `,
          },
          {
            role: "user",
            content: `
            Generate a follow up question for the given skill.
            character limit for question is 100.

            **Skill Data**:
            ${JSON.stringify(jobSkill)}

            **Recent Transcripts Data (Last 5 minutes)**:
            ${JSON.stringify(recentTranscript)}

            **Job Title**:
            ${job.title}

            jobSkill fomat is:
            {
              jobSkill_id: number,
              jobSkill_type: ${type},
              skill_title: string,
              skill_short_description: string
            }

            **Requirements**:
            - Generate exactly 1 question for the given skill
            - Professional and clear language
            - Focus on the most recent part of the conversation in the transcript
            - Do not generate questions from normal conversation only if transcript is related to job title and the given skill description
            - Analyze if the transcript is related to the job title and the given skill in terms of synonyms and idoms and phrases
            - if the jobskill is undefined then it is career_based skill in that case use transcript solely else use shortDescription of skill to get the correct follow based on transcript and short description
            - Ensure the question is different from previous questions by focusing on the most recent transcript content

            **Response Format**:
            json
            {
              questionText: string
            }
          `,
          },
        ],
      });

      const responseContent = response.choices[0].message.content || "";

      console.log("responseContent===>>>", responseContent);

      // Extract JSON from the response by removing markdown formatting
      let jsonContent = responseContent;

      // Remove markdown code block indicators if present
      if (responseContent.includes("```json")) {
        jsonContent = responseContent.replace(/```json\n|\n```/g, "");
      } else if (responseContent.includes("```")) {
        jsonContent = responseContent.replace(/```\n|\n```/g, "");
      }

      console.log("jsonContent===>>>", jsonContent);

      return jsonContent;
    } catch (error) {
      console.log("generateFollowUpQuestions error===>>>", error);
      Sentry.captureException(error);

      return "";
    }
  };

  static getUpcomingOrPastInterviews = async (
    orgId: number,
    userId: number,
    roleId: number,
    searchStr?: string, // ✅ Added: Enables filtering by candidate name or job title
    isPast: boolean = false,
    offset: number = DEFAULT_OFFSET, // ✅ Added: Enables pagination (skips 'offset' records)
    limit: number = DEFAULT_LIMIT // ✅ Added: Limits the number of results returned
    // ✅ Added: Enables filtering by candidate name or job title
  ) => {
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const now = new Date();

      const query = interviewRepo
        .createQueryBuilder("interview")
        .leftJoin(
          JobApplicationsModel,
          "jobApplication",
          "interview.jobApplicationId = jobApplication.id"
        )
        .leftJoin(
          CandidatesModel,
          "candidate",
          "jobApplication.candidateId = candidate.id"
        )
        .leftJoin(JobsModel, "job", "interview.jobId = job.id")
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere(
          isPast
            ? new Brackets((qb) => {
                qb.where("interview.startTime < :now", { now }).orWhere(
                  "interview.isEnded = :isEnded",
                  { isEnded: true }
                );
              })
            : new Brackets((qb) => {
                qb.where("interview.startTime >= :now", { now }).andWhere(
                  "interview.isEnded = :isEnded",
                  { isEnded: false }
                );
              })
        )
        .select([
          "interview.id AS interviewId",
          "interview.jobApplicationId AS jobApplicationId",
          "interview.startTime AS startTime",
          "interview.endTime AS endTime",
          "interview.isEnded AS isEnded",
          "interview.roundType AS roundType",
          "jobApplication.resumeFile AS resumeFile",
          "candidate.name AS candidateName",
          "job.title AS jobTitle",
        ]);

      // 🔍 Filtering logic using search string
      // ✅ Added: This allows filtering by candidate name or job title using a case-insensitive search
      if (searchStr?.trim()) {
        const likeStr = `%${searchStr.trim().toLowerCase()}%`;
        query.andWhere(
          "(LOWER(candidate.name) LIKE :searchStr OR LOWER(job.title) LIKE :searchStr)",
          { searchStr: likeStr }
        );
      }
      query.orderBy("interview.startTime", isPast ? "DESC" : "ASC");

      const rolePermissions = await AccessManagementServices.getUserPermissions(
        roleId,
        true
      );
      const permission = rolePermissions.data.rolePermissions.includes(
        PERMISSION.VIEW_ALL_SCHEDULED_INTERVIEWS
      );
      console.log("permission=======>>>", permission);

      if (!permission) {
        query.andWhere(
          "(interview.interviewerId = :userId OR interview.scheduledBy = :userId)",
          {
            userId,
          }
        );
      }

      query.skip(offset).take(limit);

      const interviews = await query.getRawMany();

      return {
        success: true,
        code: 200,
        message: `Fetched ${isPast ? "past" : "upcoming"} interviews successfully`,
        data: interviews,
      };
    } catch (error) {
      console.error("getUpcomingOrPastInterviews error ===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        code: 500,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static endInterview = async ({
    interviewId,
    behaviouralNotes,
  }: IEndInterview) => {
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const evaluationRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillEvaluationModel
        );

      const interview = await interviewRepo.findOne({
        where: { id: interviewId },
      });

      if (interview?.isEnded) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_already_ended,
        };
      }

      const cache = new Cache();
      const interviewTranscript = await cache.get(
        `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`
      );

      console.log(
        "REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY + interviewId=====>>>>>",
        REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY + interviewId
      );

      console.log(
        "interviewTranscript===>>>end interview",
        interviewTranscript
      );

      console.log("behavioralNotes====>>>>>", behaviouralNotes);

      // Update the interview
      await interviewRepo.update(
        { id: interviewId },
        {
          isEnded: true,
          applicantBehavioralNotes: behaviouralNotes,
          transcriptText: interviewTranscript,
          interviewEndTime: new Date(),
        }
      );

      // Lock all evaluation rows for this interview
      await evaluationRepo.update({ interviewId }, { locked: true });

      return {
        success: true,
        code: 200,
        message: API_RESPONSE_MSG.interview_ended,
      };
    } catch (error) {
      console.error("endInterview error ===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        code: 500,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getInterviewFeedback = async (interviewId: number) => {
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interview = await interviewRepo
        .createQueryBuilder("interview")
        .leftJoinAndSelect("interview.jobApplication", "jobApplication")
        .leftJoinAndSelect("jobApplication.candidate", "candidate")
        .leftJoinAndSelect("jobApplication.job", "job")
        .where("interview.id = :interviewId", { interviewId })
        .select([
          "interview.id as interviewId",
          "interview.jobApplicationId as jobApplicationId",
          "interview.roundNumber as roundNumber",
          "interview.hardSkillMarks as hardSkillMarks",
          "interview.applicantAiBehavioralAnalysis as applicantAiBehavioralAnalysis",
          "interview.applicantBehavioralNotes as applicantBehavioralNotes",
          "candidate.name as candidateName",
          "candidate.email as candidateEmail",
          "interview.isFeedbackFilled as isFeedbackFilled",
          "job.title as jobTitle",
          "interview.aiDecisionForNextRound as aiDecisionForNextRound",
        ])
        .getRawOne();

      console.log("interview ===>>>", interview);

      if (!interview) {
        console.log("Interview not found for id:", interviewId);

        return {
          success: false,
          message: API_RESPONSE_MSG.interview_not_found,
          data: null,
        };
      }

      const interviewFeedbackRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewFeedbackModel
        );

      const response = await interviewFeedbackRepo
        .createQueryBuilder("ifr")
        .leftJoinAndSelect(
          "interview_skill_evaluations",
          "ise",
          "ise.jobSkillId = ifr.jobSkillId"
        )
        .leftJoinAndSelect(
          JobSkillsModel,
          "jobSkill",
          "jobSkill.id = ise.jobSkillId"
        )
        .leftJoinAndSelect(SkillsModel, "skill", "skill.id = jobSkill.skillId")
        .select([
          "ifr.id as id",
          "ifr.jobSkillId as jobSkillId",
          "ifr.highlights as highlights",
          "ise.skill_marks as score",
          "skill.title as skillTitle",
          "jobSkill.type as type",
          "ifr.createdTs as createdTs",
          "ifr.updatedTs as updatedTs",
        ])
        .groupBy(
          "ifr.id, ifr.jobSkillId, skill.title, jobSkill.type, ifr.createdTs, ifr.updatedTs"
        ) // Group by all the non-aggregated fields
        .getRawMany();

      // console.log("response ===>>>", response);

      const careerBasedSkill = response.find(
        (skill) => skill.jobSkillId === null
      );

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: {
          cultureSpecificSkills: response
            .filter((skill) => skill.type === SkillType.CULTURE_SPECIFIC)
            .map((data) => ({
              ...data,
              highlights: JSON.parse(data.highlights),
            })),
          roleSpecificSkills: response
            .filter((skill) => skill.type === SkillType.ROLE_SPECIFIC)
            .map((data) => ({
              ...data,
              highlights: JSON.parse(data.highlights),
            })),
          careerBasedSkills: {
            id: careerBasedSkill?.id,
            score: interview.hardSkillMarks,
            highlights: careerBasedSkill
              ? JSON.parse(careerBasedSkill.highlights)
              : { overAllFeedback: [] },
          },
          applicantAiBehavioralAnalysis:
            interview.applicantAiBehavioralAnalysis,
          applicantBehavioralNotes: interview.applicantBehavioralNotes,
          roundNumber: interview.roundNumber,
          candidateName: interview.candidateName,
          jobTitle: interview.jobTitle,
          candidateEmail: interview.candidateEmail,
          isFeedbackFilled: interview.isFeedbackFilled,
          aiDecisionForNextRound: interview.aiDecisionForNextRound,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      console.log("getInterviewFeedback error===>>>", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        data: null,
      };
    }
  };

  static updateInterviewFeedback = async ({
    interviewId,
    feedback,
    isAdvanced,
    orgId,
    userId,
    behavioralNotes,
  }: {
    interviewId: number;
    feedback: string;
    isAdvanced: boolean;
    orgId: number;
    userId: number;
    behavioralNotes?: string;
  }) => {
    try {
      console.log("updateInterviewFeedback called with data ===>>>", {
        interviewId,
        feedback,
        isAdvanced,
        orgId,
        behavioralNotes,
      });

      // Get the feedback repository
      const interviewFeedbackRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewFeedbackModel
        );
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interview = await interviewRepo.findOne({
        where: { id: interviewId },
      });

      console.log("interview ===>>>", interview);

      if (!interview) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_not_found,
        };
      }

      if (userId !== interview.interviewerId) {
        return {
          success: false,
          message: API_RESPONSE_MSG.unauthorized,
        };
      }

      if (!interview.isEnded) {
        return {
          success: false,
          message:
            API_RESPONSE_MSG.cannot_update_feedback_before_ending_interview,
        };
      }

      if (interview.isFeedbackFilled) {
        return {
          success: false,
          message: API_RESPONSE_MSG.feedback_already_submitted,
        };
      }

      const interviewFeedbacks = await interviewFeedbackRepo.find({
        where: { interviewId },
      });

      console.log("interviewFeedbacks ===>>>", interviewFeedbacks);

      if (!interviewFeedbacks) {
        console.log("No feedback found for this interview");
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_feedback_not_found,
        };
      }

      const feedbackData = JSON.parse(feedback);
      console.log("feedbackData ===>>>", feedbackData);

      const skillData = [...feedbackData];

      console.log("skillData ===>>>", skillData);

      // First fetch all existing feedback records to preserve AI feedback
      const feedbackIds = skillData.map((skill) => skill.id);
      const existingFeedbacks = await interviewFeedbackRepo.find({
        where: { id: In(feedbackIds) }, // Use TypeORM's In operator
      });

      // Map existing feedbacks by id for easy lookup
      const feedbackMap = existingFeedbacks.reduce(
        (acc, existingItem) => {
          acc[existingItem.id] = existingItem;
          return acc;
        },
        {} as Record<number, any>
      );
      console.log("feedbackMap ===>>>", behavioralNotes);
      console.log("interviewId ===>>>", interviewId);

      // Create update promises for skills feedback
      const promises = skillData
        .map((skill) => {
          const existingFeedback = feedbackMap[skill.id];

          if (!existingFeedback) {
            return Promise.resolve(); // Skip if not found
          }

          // Only update overAllFeedback while preserving the existing aiFeedback
          return interviewFeedbackRepo.update(
            { id: skill.id },
            {
              highlights: {
                overAllFeedback: skill.highlights.overAllFeedback,
                // Keep the existing aiFeedback
                aiFeedback: existingFeedback.highlights?.aiFeedback || [],
              },
            }
          );
        })
        .filter(Boolean); // Filter out any undefined promises

      const ressss = await Promise.all(promises);
      console.log("ressss ===>>>", ressss);

      console.log("isAdvanced ===>>>", isAdvanced);

      // send notification interviewer
      if (isAdvanced) {
        const jobApplicationRepo =
          await dbConnection.getS9InnerViewDatabaseRepository(
            JobApplicationsModel
          );

        const candidate = await jobApplicationRepo
          .createQueryBuilder("jobApplication")
          .leftJoinAndSelect("jobApplication.candidate", "candidate")
          .where("jobApplication.id = :id", { id: interview.jobApplicationId })
          .select(["candidate.name as name"])
          .getRawOne();

        console.log("candidate ===>>>", candidate);

        NotificationServices.createNotification(orgId, interview.scheduledBy, {
          type: NotificationType.INTERVIEW_ADVANCED,
          title: NotificationType.INTERVIEW_ADVANCED,
          description: `${candidate.name} promoted to next round. Prepare for interview.`,
          relatedId: interview.id,
        });
      }

      const result = await this.generateAISummaryAndUpdateDecision({
        interviewId,
        isAdvanced,
        interview,
        interviewRepo,
        behavioralNotes,
      });

      if (result.success === false) {
        console.log("Failed to generate AI summary or update decision");
        return {
          success: false,
          message: result.message || API_RESPONSE_MSG.failed,
        };
      }

      console.log("AI summary generated and decision updated successfully");
      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.log("updateInterviewFeedback error===>>>", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static generateAISummaryAndUpdateDecision = async ({
    interviewId,
    isAdvanced,
    interview,
    interviewRepo,
    behavioralNotes,
  }: {
    interviewId: number;
    isAdvanced: boolean;
    interview: InterviewModel;
    interviewRepo: Repository<InterviewModel>;
    behavioralNotes: string;
  }) => {
    try {
      console.log("inside generateAISummaryAndUpdateDecision");
      // Get OpenAI API Key
      let OPENAI_API_KEY = "";
      try {
        const keys = await getSecretKeys();
        OPENAI_API_KEY = keys.openai_api_key;
      } catch (error) {
        Sentry.captureException(error);
        return {
          success: false,
          message: "Failed to retrieve OpenAI API key",
          error: error.message,
        };
      }

      // Initialize OpenAI client
      const openAiClient = new OpenAI({ apiKey: OPENAI_API_KEY });

      // Fetch interview data to get highlights and behavioral notes
      const interviewData =
        await InterviewServices.getInterviewFeedback(interviewId);
      console.log("interviewData ===>>>", interviewData);
      // Check if interview data retrieval was successful
      if (!interviewData.success || !interviewData.data) {
        console.log("Failed to retrieve interview data");
        return {
          success: false,
          message: "Failed to retrieve interview data",
        };
      }

      const feedbackData = interviewData.data;

      // Prepare input for AI summary generation
      const roleSpecificHighlights = feedbackData.roleSpecificSkills
        .flatMap((skill) => skill.highlights?.overAllFeedback || [])
        .filter(Boolean);

      const cultureSpecificHighlights = feedbackData.cultureSpecificSkills
        .flatMap((skill) => skill.highlights?.overAllFeedback || [])
        .filter(Boolean);

      const careerBasedHighlights =
        feedbackData.careerBasedSkills?.highlights?.overAllFeedback || [];

      // const behavioralNotes = feedbackData.applicantAiBehavioralAnalysis || "";

      // Prepare the prompt for OpenAI
      const prompt = `
You are an experienced hiring manager reviewing interview feedback for a candidate.
Based on the following interview highlights and behavioral notes, generate a comprehensive and professional summary of the candidate.
The candidate is being considered for the position of: ${feedbackData.jobTitle}.
The candidate's name is: ${feedbackData.candidateName}.

Role-Specific Skills Highlights:
${roleSpecificHighlights.map((highlight) => `- ${highlight}`).join("\n")}

Culture-Specific Skills Highlights:
${cultureSpecificHighlights.map((highlight) => `- ${highlight}`).join("\n")}

Career-Based Skills Highlights:
${careerBasedHighlights.map((highlight) => `- ${highlight}`).join("\n")}

Behavioral Notes:
${behavioralNotes}

Final Decision: ${isAdvanced ? "ADVANCE to next round" : "REJECT the candidate"}

Please generate a comprehensive and professional summary of the candidate's performance that includes:
1. Overall assessment of the candidate's fit for the role
2. Key strengths demonstrated during the interview
3.output format should be an array of strings
4. Areas for development or potential concerns
5. Justification for the ${isAdvanced ? "advancement" : "rejection"} decision
6. Recommendations for next steps (${isAdvanced ? "future interview focus areas" : "feedback for the candidate"})

The tone should be professional, balanced, and supportive regardless of the decision.
Limit the response to 20-30 words.
      `;

      // Call OpenAI to generate the summary
      const aiResponse = await openAiClient.chat.completions.create({
        model: GPT_MODEL,
        messages: [
          {
            role: "system",
            content:
              "You are an expert hiring assistant that provides balanced and insightful interview feedback.",
          },
          { role: "user", content: prompt },
        ],
      });

      if (!aiResponse?.choices?.[0]?.message?.content) {
        return {
          success: false,
          message: "Failed to generate AI summary",
        };
      }

      // Get raw AI response content
      const aiGeneratedSummary = aiResponse.choices[0].message.content;

      console.log(
        typeof aiGeneratedSummary,
        "raw aiGeneratedSummary ===>>>",
        aiGeneratedSummary
      );

      let processedSummary = "";

      // Try to parse the content as JSON if it appears to be in array format
      if (
        aiGeneratedSummary.trim().startsWith("[") &&
        aiGeneratedSummary.trim().endsWith("]")
      ) {
        try {
          // Parse as JSON array
          const parsedArray = JSON.parse(aiGeneratedSummary);
          if (Array.isArray(parsedArray)) {
            // Join array elements with proper formatting
            processedSummary = parsedArray
              .map((item) => item.trim())
              .join("\n\n");
            console.log("processed array summary ===>>>", processedSummary);
          } else {
            // If not an array but valid JSON, stringify it properly
            processedSummary = JSON.stringify(parsedArray);
          }
        } catch (error) {
          console.log("Error parsing AI summary as JSON ===>>>", error);
          // If parsing fails, fall back to string processing
          processedSummary = aiGeneratedSummary;
        }
      } else {
        // Not in array format, use the raw content
        processedSummary = aiGeneratedSummary;
      }

      // Clean up the AI response to remove unwanted artifacts
      processedSummary = processedSummary
        // First pass: handle basic string escapes
        .replace(/\\n'\s*\+/g, " ") // Remove \n' + artifacts
        .replace(/\\n/g, "\n") // Convert \n to actual newlines if needed
        .replace(/\\'/g, "'") // Replace escaped single quotes

        // Second pass: handle various '+' patterns
        .replace(/'\s*\+\s*/g, "'") // Remove + after quotes
        .replace(/"\s*\+\s*/g, '"') // Remove + after double quotes
        .replace(/\s*\+\s*'/g, "'") // Remove + before quotes
        .replace(/\s*\+\s*"/g, '"') // Remove + before double quotes
        .replace(/\s*\+\s*/g, " ") // Replace standalone + with space
        .replace(/\+/g, "") // Remove any remaining + characters
        .trim();

      console.log("final cleaned summary ===>>>", processedSummary);

      // Format the AI summary as JSON with a highlight key
      const aiSummaryJson = { highlight: JSON.parse(aiGeneratedSummary) };
      console.log("aiSummaryJson ===>>>", aiSummaryJson);

      // Update interview with decision and summary
      interview.isAllowedForNextRound = isAdvanced;
      interview.applicantAiBehavioralAnalysis = behavioralNotes;
      interview.interviewSummary = aiSummaryJson;
      interview.isFeedbackFilled = true;

      await interviewRepo.save(interview);

      console.log("Interview updated successfully");
      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.error("generateAISummaryAndUpdateDecision error ===>>>", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getPendingInterviews = async (userId: number, orgId: number) => {
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      // Fetch interviews where feedback is not filled
      const pendingInterviews = await interviewRepo
        .createQueryBuilder("interview")
        .leftJoinAndSelect("interview.jobApplication", "jobApplication")
        .leftJoinAndSelect("jobApplication.candidate", "candidate")
        .leftJoinAndSelect("jobApplication.job", "job")
        .where("interview.isFeedbackFilled = :isFeedbackFilled", {
          isFeedbackFilled: false,
        })
        .andWhere("interview.isEnded = true")
        .andWhere("interview.interviewerId = :interviewerId", {
          interviewerId: userId,
        })
        .andWhere("job.orgId = :orgId", { orgId })
        .select([
          "interview.id as interviewId",
          "candidate.name as candidateName",
          "job.title as jobTitle",
          "interview.roundNumber as roundNumber",
          "interview.scheduleAt as date",
          "interview.startTime as startTime",
          "interview.endTime as endTime",
          "interview.interviewerId as interviewerId",
        ])
        .getRawMany();

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: pendingInterviews,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.log("getPendingInterviews error===>>>", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        data: null,
      };
    }
  };
}

export default InterviewServices;
