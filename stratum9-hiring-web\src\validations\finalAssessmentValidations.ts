// Validation schema
import * as yup from "yup";

export const addQuestionValidationSchema = (translation: (key: string) => string) =>
  yup.object().shape({
    question: yup.string().required(translation("question_is_required")),
    questionType: yup
      .string()
      .oneOf(["mcq", "true_false"], translation("question_type_is_required"))
      .required(translation("question_type_is_required")),
    options: yup
      .array()
      .of(
        yup.object().shape({
          id: yup.string().required(translation("option_id_is_required")),
          text: yup.string().required(translation("option_text_is_required")),
        })
      )
      .required(translation("options_are_required"))
      .min(2, translation("at_least_2_options_are_required")),
    correctAnswer: yup.string().required(translation("correct_answer_is_required")),
  });
export const createValidationSchema = (t: (key: string) => string) =>
  yup.object().shape({
    email: yup.string().email(t("please_enter_valid_email")).required(t("email_is_required")),
  });
