export const DB_CONST = {
  TYPE: "mysql",
  CHARSET: "utf8mb4_unicode_ci",
  S9_NAME: "s9_db",
  S9_INNERVIEW_NAME: "s9iv_db",
};

export const ENV_VARIABLE = {
  LOCAL: "local",
};

export const AGORA_INTERVIEW_RECORDING_URL =
  "agoraInterviewRecordings/:jobId/:jobApplicationId/:interviewId";

export const GPT_MODEL = "gpt-4.1";

export const INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS = {
  BEGINNER: "beginner",
  INTERMEDIATE: "intermediate",
  ADVANCED: "advanced",
};

/**
 * Benefit slugs used across the application for subscription benefits
 */

export const BENEFIT_SLUGS = {
  JOB_POSTINGS: "job_postings",
  RESUME_SCREENING: "resume_screening",
  MANUAL_RESUME_UPLOAD: "manual_resume_upload",
};

export interface ISecretKeys {
  pubnub_publishKey: string;
  pubnub_subscribeKey: string;
  pubnub_userId: string;
  deepgram_secret_key: string;
  token_key: string;
  otp_enc_key: string;
  db_hostname: string;
  db_username: string;
  db_password: string;
  db_database_name: string;
  s9iv_db_database_name: string;
  s9iv_db_hostname: string;
  s9iv_db_username: string;
  s9iv_db_password: string;
  reset_password_url: string;
  jwt_secret: string;
  twillo_sid: string;
  twillo_token: string;
  twillo_number: string;
  database_host: string;
  database_port: string;
  database_username: string;
  database_password: string;
  database_name: string;
  paypal_WebhookID: string;
  paypal_clientId: string;
  paypal_clientSecret: string;
  admin_url: string;
  seller_url: string;
  paypal_mode: string;
  fedex_secret: string;
  fedex_apikey: string;
  shipper_postalcode: string;
  fedex_account: string;
  sendgrid_key: string;
  sendgrid_mail: string;
  fedex_url: string;
  paypal_url: string;
  accelerate_url: string;
  ec2_generate_pdf_url: string;
  googleAutocompleteApiKey: string;
  authKey: string;
  dailywins_attempt_limit: number;
  redis_db_endpoint: string;
  s3_bucket_cloudfront_distribution_url: string;
  s3_bucket_cloudfront_distribution_id: string;
  s3_bucket_cloudfront_distribution_url_for_s9_innerview: string;
  s3_bucket_cloudfront_distribution_id_for_s9_innerview: string;
  s3_bucket_url_s9_innerview: string;
  s3_bucket_url: string;
  s9_hiring_database_name: string;
  openai_api_key: string;
}

export const ACTIVITY_LOG_TYPE = {
  INTERVIEW_FEEDBACK: "Interview Feedback",
  CANDIDATE_FINAL_SUMMARY: "Candidate Final Summary",
};



export const ENTITY_TYPE = {
  FINAL_ASSESSMENTS_MODEL: "Final Assessments Model",
  INTERVIEW_FEEDBACK_MODEL: "Interview Feedback Model",
};

export const SUMMARY_CREATED_COMMENT = "Summary Created"
export const ACTIVITY_LOG_INTERVIEW_FEEDBACK = {
  POSITIVE : "Positive Feedback",
  NEGATIVE : "Negative Feedback"
}