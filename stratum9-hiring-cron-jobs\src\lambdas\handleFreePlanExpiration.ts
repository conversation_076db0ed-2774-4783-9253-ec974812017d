import dbConnection from "../dbConnection";
import OrganizationSubscriptionModel from "../schema/organization_subscriptions";
import OrganizationSubscriptionBenefitModel from "../schema/organization_subscription_benefits";
import SubscriptionPlanModel, { PaymentType } from "../schema/subscription_plans";
import * as Sentry from "@sentry/node";
import { BENEFIT_SLUGS } from "../utils/constants";

/**
 * Utility function to format dates in 'YYYY-MM-DD' format.
 * @param date - Date object or string to format.
 * @returns {string} - Formatted date.
 */
const formatDate = (date: Date | string): string => {
  try {
    // If it's a string date, convert to Date object
    if (typeof date === 'string') {
      date = new Date(date);
    }
    
    // Ensure it's a valid date
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.warn('Invalid date provided to formatDate:', date);
      return ''; // Return empty string for invalid dates
    }
    
    // Format the date in YYYY-MM-DD
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date:', error);
    return ''; // Return empty string on error
  }
};

/**
 * Fetch all expired subscriptions, not limited to free plan.
 * @param orgSubsRepo - Repository for the OrganizationSubscriptionModel.
 * @param today - Today's date used for comparison.
 * @returns {Promise<any[]>} - List of expired subscriptions.
 */
const fetchExpiredActiveFreePlanSubscriptions = async (
  orgSubsRepo: any,
  today: Date,
  freePlanId: number
): Promise<any[]> => {
  try {
    console.log(`Fetching active, expired free plan subscriptions for benefit reset on ${formatDate(today)}`);

    return await orgSubsRepo
      .createQueryBuilder('os')
      .select([
        'os.id',
        'os.organizationId',
        'os.subscriptionId',
        'os.expiryDate',
        'os.status',
        'os.nextBillingDate',
        'os.isActive'
      ])
      .where('os.subscriptionId = :subscriptionId', { subscriptionId: freePlanId })
      .andWhere('os.expiryDate <= :today', { today: formatDate(today) })
      .andWhere('os.isActive = :isActive', { isActive: true })
      .getMany();
  } catch (error) {
    console.error("Error fetching filtered expired subscriptions:", error);
    throw new Error("Error fetching filtered expired subscriptions");
  }
};


/**
 * Extend the subscription expiry date for one month (single subscription).
 * @param subscription - The subscription to extend.
 * @param orgSubsRepo - Repository for the OrganizationSubscriptionModel.
 * @returns {Promise<{id: number, newExpiryDate: Date, newNextBillingDate: Date}>} - The updated subscription data.
 */
const extendSubscriptionExpiry = async (subscription: any, orgSubsRepo: any): Promise<{id: number, newExpiryDate: Date, newNextBillingDate: Date}> => {
  const currentExpiry = new Date(subscription.expiryDate);
  const newExpiryDate = new Date(currentExpiry);
  newExpiryDate.setMonth(newExpiryDate.getMonth() + 1);

  const newNextBillingDate = new Date(newExpiryDate);

  try {
    await orgSubsRepo
      .createQueryBuilder()
      .update()
      .set({
        expiryDate: newExpiryDate,
        nextBillingDate: newNextBillingDate
      })
      .where("id = :id", { id: subscription.id })
      .execute();
    
    return { 
      id: subscription.id, 
      newExpiryDate, 
      newNextBillingDate 
    };
  } catch (error) {
    console.error(`Error extending subscription ${subscription.id}:`, error);
    throw new Error(`Error extending subscription ${subscription.id}`);
  }
};

/**
 * Extend multiple subscription expiry dates in bulk.
 * @param subscriptions - Array of subscriptions to extend.
 * @param orgSubsRepo - Repository for the OrganizationSubscriptionModel.
 * @returns {Promise<Array<{id: number, organizationId: number, newExpiryDate: Date, success: boolean}>>}
 */
const extendSubscriptionsInBulk = async (subscriptions: any[], orgSubsRepo: any): Promise<any[]> => {
  if (subscriptions.length === 0) return [];
  
  console.log(`Preparing to bulk extend ${subscriptions.length} subscriptions`);
  const today = new Date();
  
  // Filter out subscriptions that have already been extended
  const subscriptionsToExtend = subscriptions.filter(sub => {
    const currentExpiry = new Date(sub.expiryDate);
    return currentExpiry <= today;
  });
  
  if (subscriptionsToExtend.length === 0) {
    console.log('No subscriptions need extension after filtering');
    return [];
  }
  
  console.log(`Bulk extending ${subscriptionsToExtend.length} subscriptions`);
  
  try {
    // Create a map of subscription IDs for updating
    const subscriptionIds = subscriptionsToExtend.map(sub => sub.id);
    
    // For each subscription, calculate its new expiry date (1 month from current expiry)
    const updatePromises = subscriptionsToExtend.map(async (subscription) => {
      const currentExpiry = new Date(subscription.expiryDate);
      const newExpiryDate = new Date(currentExpiry);
      newExpiryDate.setMonth(newExpiryDate.getMonth() + 1);
      // const newNextBillingDate = new Date(newExpiryDate);
      
      return {
        id: subscription.id,
        organizationId: subscription.organizationId,
        subscriptionId: subscription.subscriptionId,
        oldExpiryDate: formatDate(currentExpiry),
        newExpiryDate: formatDate(newExpiryDate),
        success: true
      };
    });
    
    // Prepare batch update data
    const updates = subscriptionsToExtend.map(sub => {
      const currentExpiry = new Date(sub.expiryDate);
      const newExpiryDate = new Date(currentExpiry);
      newExpiryDate.setMonth(newExpiryDate.getMonth() + 1);
      const newNextBillingDate = new Date(newExpiryDate);
      
      return {
        id: sub.id,
        expiryDate: newExpiryDate,
        nextBillingDate: newNextBillingDate
      };
    });
    
    // Execute the bulk update with a single query
    if (subscriptionIds.length > 0) {
      // TypeORM doesn't support true bulk updates with different values per record
      // So we'll need to use individual updates in a transaction for efficiency
      await orgSubsRepo.manager.transaction(async transactionalEntityManager => {
        const updatePromises = updates.map(update => {
          return transactionalEntityManager.update(
            OrganizationSubscriptionModel,
            { id: update.id },
            { 
              expiryDate: update.expiryDate, 
              nextBillingDate: update.nextBillingDate 
            }
          );
        });
        
        await Promise.all(updatePromises);
      });
      
      console.log(`Successfully bulk updated ${subscriptionIds.length} subscriptions`);
    }
    
    return await Promise.all(updatePromises);
  } catch (error) {
    console.error(`Error in bulk subscription extension:`, error);
    Sentry.captureException(error);
    throw new Error(`Bulk subscription extension failed: ${error.message}`);
  }
};

// /**
//  * Handle sending email notifications for subscription extension.
//  * @param subscription - The subscription being extended.
//  */
// const sendExtensionNotification = async (subscription: any): Promise<void> => {
//   try {
//     // Placeholder for actual email sending logic.
//     console.log(`Subscription extension notification would be sent to org-${subscription.organizationId}@example.com`);
//     console.log(`- Extension period: ${subscription.expiryDate} to ${new Date(subscription.expiryDate).toISOString()}`);
//   } catch (emailError) {
//     console.error("Error preparing subscription extension email notification:", emailError);
//   }
// };

/**
 * Process benefits data from plan JSON and apply it to the benefits model
 * @param benefits - Organization benefits record to update
 * @param benefitsData - Raw benefits data from plan
 */
const processBenefitsData = async (
  benefits: OrganizationSubscriptionBenefitModel,
  benefitsData: any
): Promise<void> => {
  // Process benefits based on format (array or object)
  if (Array.isArray(benefitsData)) {
    // Handle benefits array format
    for (const benefit of benefitsData) {
      if (benefit.slug === BENEFIT_SLUGS.JOB_POSTINGS && typeof benefit.value === 'number') {
        benefits.jobPostings = benefit.value;
      }
      if (benefit.slug === BENEFIT_SLUGS.RESUME_SCREENING && typeof benefit.value === 'number') {
        benefits.resumeScreening = benefit.value;
      }
      if (benefit.slug === BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD && typeof benefit.value === 'number') {
        benefits.manualResumeUpload = benefit.value;
      }
    }
  } else if (typeof benefitsData === 'object') {
    // Handle benefits as object format
    if (benefitsData[BENEFIT_SLUGS.JOB_POSTINGS]?.limit) {
      benefits.jobPostings = benefitsData[BENEFIT_SLUGS.JOB_POSTINGS].limit;
    }
    if (benefitsData[BENEFIT_SLUGS.RESUME_SCREENING]?.limit) {
      benefits.resumeScreening = benefitsData[BENEFIT_SLUGS.RESUME_SCREENING].limit;
    }
    if (benefitsData[BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD]) {
      // If manual_resume_upload is a value, use it
      if (typeof benefitsData[BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD] === 'number') {
        benefits.manualResumeUpload = benefitsData[BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD];
      }
      // If manual_resume_upload has properties like limit/value
      else if (typeof benefitsData[BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD] === 'object') {
        if ('limit' in benefitsData[BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD]) {
          benefits.manualResumeUpload = benefitsData[BENEFIT_SLUGS.MANUAL_RESUME_UPLOAD].limit;
        }
      }
    }
    // Set unlimited if specified
    if (benefitsData[BENEFIT_SLUGS.JOB_POSTINGS]?.unlimited) {
      benefits.jobPostings = -1; // -1 represents unlimited
    }
    if (benefitsData[BENEFIT_SLUGS.RESUME_SCREENING]?.unlimited) {
      benefits.resumeScreening = -1; // -1 represents unlimited
    }
  }
};

/**
 * Resets the benefits for an organization to match their subscription plan.
 * @param organizationId - The organization ID
 * @param planId - The subscription plan ID
 * @returns {Promise<boolean>} - True if reset was successful
 */
const resetSubscriptionBenefits = async (
  organizationId: number,
  planId: number
): Promise<boolean> => {
  try {
    // Get the repositories
    const planRepo = await dbConnection.getRepository(SubscriptionPlanModel);
    const benefitsRepo = await dbConnection.getRepository(OrganizationSubscriptionBenefitModel);
    
    // Get or create benefits record
    let benefits = await benefitsRepo.findOne({
      where: { organizationId }
    });
    
    if (!benefits) {
      console.log(`No benefits record found for organization ${organizationId}, creating new one`);
      benefits = new OrganizationSubscriptionBenefitModel();
      benefits.organizationId = organizationId;
    }
    
    // Get the subscription plan with benefits
    // Explicitly select only the fields we need to avoid any schema mismatches
    const subscriptionPlan = await planRepo.findOne({
      where: { id: planId },
      select: ['id', 'name', 'benefits']
    });
    
    // Plan found with benefits data - use it to update organization benefits
    if (subscriptionPlan && subscriptionPlan.benefits) {
      console.log(`Resetting benefits for plan ID ${planId}:`, subscriptionPlan.benefits);

      // Process benefits from plan data
      await processBenefitsData(benefits, subscriptionPlan.benefits);
      await benefitsRepo.save(benefits);
      
      console.log(`Benefits reset for organization ${organizationId} based on plan ${planId}`);
      return true;
    } else {
      console.warn(`Subscription plan with ID ${planId} not found or has no benefits. Unable to reset benefits.`);
      console.log('subscriptionPlan:', subscriptionPlan);
      return false;
    }
  } catch (error) {
    console.error(`Error resetting benefits for organization ${organizationId}:`, error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Process expired subscriptions and reset their benefits.
 * @param expiredSubscriptions - List of expired subscriptions
 * @returns {Promise<any[]>} - Results of processing
 */
const processExpiredSubscriptionBenefits = async (expiredSubscriptions: any[]): Promise<any[]> => {
  const results = [];
  
  for (const subscription of expiredSubscriptions) {
    try {
      console.log(`Processing expired subscription ${subscription.id} for organization ${subscription.organizationId}`);
      
      // Reset benefits for this organization based on current plan
      const resetSuccessful = await resetSubscriptionBenefits(
        subscription.organizationId,
        subscription.subscriptionId
      );
      
      // Safely handle the expiryDate which might not be a valid Date object
      let formattedExpiryDate = '';
      try {
        formattedExpiryDate = subscription.expiryDate ? formatDate(subscription.expiryDate) : '';
      } catch (dateError) {
        console.warn(`Invalid expiryDate for subscription ${subscription.id}:`, dateError);
      }
      
      results.push({
        subscriptionId: subscription.id,
        organizationId: subscription.organizationId,
        expiryDate: formattedExpiryDate,
        benefitsReset: resetSuccessful,
        success: true
      });
    } catch (error) {
      console.error(`Error processing subscription ${subscription.id}:`, error);
      Sentry.captureException(error);
      
      results.push({
        subscriptionId: subscription.id,
        organizationId: subscription.organizationId,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
};


/**
 * Fetch the ID of the active free subscription plan from the database.
 * @returns {Promise<number | null>} - The ID of the free plan, or null if not found.
 */
const getFreePlanId = async (): Promise<number | null> => {
  try {
    const planRepo = await dbConnection.getRepository(SubscriptionPlanModel);

    const freePlan = await planRepo.findOne({
      where: {
        paymentType: PaymentType.FREE,
        isActive: true
      },
      select: ['id', 'name']
    });

    if (!freePlan) {
      console.warn('No active free plan found in subscription_plans table.');
      return null;
    }

    console.log(`Free plan found: ID=${freePlan.id}, Name=${freePlan.name}`);
    return freePlan.id;
  } catch (error) {
    console.error('Error fetching free plan ID:', error);
    Sentry.captureException(error);
    return null;
  }
};


/**
 * Lambda function to handle subscription lifecycle
 * 1. When a free plan subscription reaches its expiry date, this function extends it for another month
 * 2. For all expired subscriptions, it resets the benefit values in the organization_subscription_benefits table
 */
export const handleSubscriptionLifecycleLambda = async (event: any, _context: any, _callback: any) => {
  console.log("Handle free plan expiration Lambda function event", event);

  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize time to midnight

    const orgSubsRepo = await dbConnection.getRepository(OrganizationSubscriptionModel);
    console.log("Organization subscription repository loaded successfully");

    const FREE_PLAN_ID = await getFreePlanId();
    if (!FREE_PLAN_ID) {
      throw new Error('No active free plan found. Aborting lifecycle handling.');
    }

    // Step 1: Fetch expired, active, free plan subscriptions
    const expiredFreeSubscriptions = await fetchExpiredActiveFreePlanSubscriptions(orgSubsRepo, today, FREE_PLAN_ID);
    console.log(`Found ${expiredFreeSubscriptions.length} expired free subscriptions`);

    let benefitResults = [];
    if (expiredFreeSubscriptions.length > 0) {
      // Step 2: Reset benefits BEFORE extending
      benefitResults = await processExpiredSubscriptionBenefits(expiredFreeSubscriptions);
      const resetCount = benefitResults.filter(r => r.benefitsReset).length;
      console.log(`Reset benefits for ${resetCount} subscriptions`);
    }

    let results = [];

    if (expiredFreeSubscriptions.length > 0) {
      try {
        // Step 3: Extend subscriptions in bulk
        const bulkResults = await extendSubscriptionsInBulk(expiredFreeSubscriptions, orgSubsRepo);

        results = bulkResults.map(item => ({
          success: item.success,
          subscriptionId: item.id,
          organizationId: item.organizationId,
          oldExpiryDate: item.oldExpiryDate,
          newExpiryDate: item.newExpiryDate
        }));

        bulkResults.forEach(item => {
          console.log(`Extended subscription ${item.id} for organization ${item.organizationId}: ${item.oldExpiryDate} → ${item.newExpiryDate}`);
        });

      } catch (error) {
        console.error(`Error in bulk subscription processing:`, error);
        Sentry.captureException(error);
        console.log('Falling back to individual subscription processing');

        // Step 4: Fallback to individual extension if bulk fails
        const updatePromises = expiredFreeSubscriptions.map(async (subscription) => {
          try {
            const currentExpiry = new Date(subscription.expiryDate);
            if (currentExpiry > today) {
              console.log(`Subscription ${subscription.id} already extended, skipping.`);
              return { success: false, subscriptionId: subscription.id, message: 'Already extended' };
            }

            const updated = await extendSubscriptionExpiry(subscription, orgSubsRepo);
            return {
              success: true,
              subscriptionId: subscription.id,
              organizationId: subscription.organizationId,
              newExpiryDate: formatDate(updated.newExpiryDate)
            };
          } catch (error) {
            console.error(`Error processing subscription ${subscription.id}:`, error);
            Sentry.captureException(error);
            return { success: false, subscriptionId: subscription.id, error: error.message };
          }
        });

        results = await Promise.all(updatePromises);
      }
    }

    // Final logging
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    console.log(`Successfully extended ${successCount} subscriptions with ${failureCount} failures. Details: `, JSON.stringify(results, null, 2));

    return {
      success: true,
      message: `Extended ${successCount} free plan subscriptions for another month (${failureCount} failures) and reset benefits for expired subscriptions`,
      data: {
        processedCount: results.length,
        successCount,
        failureCount,
        results,
        benefitsReset: {
          processedCount: expiredFreeSubscriptions.length,
          resetCount: benefitResults.filter(r => r.benefitsReset).length,
          results: benefitResults
        }
      }
    };

  } catch (error) {
    console.error("Error in handleSubscriptionLifecycleLambda:", error);
    Sentry.captureException(error);

    return {
      success: false,
      message: `Error handling free plan expirations: ${error.message}`,
      error: error.stack
    };
  }
};



