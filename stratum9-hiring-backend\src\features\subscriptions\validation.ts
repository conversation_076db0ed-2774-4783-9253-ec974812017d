import * as <PERSON><PERSON> from "joi";

export const cancelSubscriptionValidation = Joi.object({
  user_id: Joi.string().required().messages({
    "string.empty": "user_id_empty",
    "any.required": "user_id_req",
  }),
});

export const buySubscriptionValidation = Joi.object({
  planId: Joi.number().required().messages({
    "number.empty": "planId_empty",
    "any.required": "planId_req",
  }),
  pricingId: Joi.number().required().messages({
    "number.empty": "pricingId_empty",
    "any.required": "pricingId_req",
  }),
});
