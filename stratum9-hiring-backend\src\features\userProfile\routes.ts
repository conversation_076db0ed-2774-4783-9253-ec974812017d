import { getMyProfile, updateMyProfile } from "./controller";
import auth from "../../middleware/auth";
import { ROUTES } from "../../utils/constants";
import employeeManagementRoutes from "../employeeManagement/routes";
import { schemaValidation } from "../../middleware/validateSchema";
import updateUserProfileValidation from "./validations";

const userProfileRoutes = employeeManagementRoutes;

/**
 * @swagger
 * /api/user-profile/get-my-profile:
 *   get:
 *     summary: Get my profile information
 *     description: Retrieves the current user's profile details including image, first name, and last name
 *     tags:
 *       - User Profile
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                   description: Indicates if the request was successful
 *                 message:
 *                   type: string
 *                   example: User profile fetched successfully
 *                   description: Status message
 *                 data:
 *                   type: object
 *                   properties:
 *                     image:
 *                       type: string
 *                       description: User profile image URL
 *                     firstName:
 *                       type: string
 *                       description: User's first name
 *                     lastName:
 *                       type: string
 *                       description: User's last name
 *       401:
 *         description: Unauthorized - User not authenticated
 *       404:
 *         description: User profile not found
 *       500:
 *         description: Internal server error
 */
userProfileRoutes.get(ROUTES.USER_PROFILE.GET_MY_PROFILE, auth, getMyProfile);

/**
 * @swagger
 * /api/user-profile/update-my-profile:
 *   post:
 *     summary: Update my profile information
 *     description: Updates user profile details including first name, last name, and profile image
 *     tags:
 *       - User Profile
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: User's first name
 *                 example: John
 *               lastName:
 *                 type: string
 *                 description: User's last name
 *                 example: Doe
 *               image:
 *                 type: string
 *                 nullable: true
 *                 description: User's profile image (base64 encoded or URL)
 *                 example: null
 *     responses:
 *       200:
 *         description: User profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User profile updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: User ID
 *                       example: 1
 *                     image:
 *                       type: string
 *                       description: User profile image URL
 *                     userName:
 *                       type: string
 *                       description: User's full name
 *                     email:
 *                       type: string
 *                       description: User's email address
 *                     role:
 *                       type: string
 *                       description: User's role in the organization
 *                     organizationName:
 *                       type: string
 *                       description: Name of the user's organization
 *                     organizationCode:
 *                       type: string
 *                       description: Organization code
 *                     departmentName:
 *                       type: string
 *                       description: User's department name
 *                     createdTs:
 *                       type: string
 *                       format: date-time
 *                       description: Account creation date
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Bad request - Invalid input data
 *       401:
 *         description: Unauthorized - User not authenticated
 *       500:
 *         description: Internal server error
 */
userProfileRoutes.put(
  ROUTES.USER_PROFILE.UPDATE_MY_PROFILE,
  auth,
  schemaValidation(updateUserProfileValidation),
  updateMyProfile
);

export default userProfileRoutes;
