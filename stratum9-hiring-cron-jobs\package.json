{"name": "stratum9-backend", "version": "1.0.0", "description": "", "main": "App.ts", "scripts": {"start": "set NODE_ENV=local&& nodemon src/App.ts&& node --max-old-space-size=5120", "mobile_start": "set NODE_ENV=stratum9_mobile_app&& nodemon src/App.ts&& node --max-old-space-size=5120", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "check-types": "tsc --pretty --noEmit", "check-prettier": "prettier --check .", "check-lint": "eslint . --ext .ts --ext .tsx --ext .js --ext .jsx", "prettier-fix": "prettier --write .", "lint-fix": "eslint --fix --ext .js,.jsx,.ts,.tsx", "test-all": "npm run check-prettier && npm run check-lint && npm run check-types && npm run build", "typeorm": "typeorm-ts-node-esm -d ./src/dataSource.ts"}, "keywords": [], "author": "Biz4group", "license": "ISC", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@aws-sdk/client-s3": "^3.859.0", "@aws-sdk/client-secrets-manager": "^3.438.0", "@sendgrid/mail": "^8.1.5", "@sentry/node": "^7.70.0", "mysql": "^2.18.1", "openai": "^5.8.2", "ts-node": "^10.9.2", "typeorm": "^0.3.17", "typeorm-naming-strategies": "^4.1.0"}}