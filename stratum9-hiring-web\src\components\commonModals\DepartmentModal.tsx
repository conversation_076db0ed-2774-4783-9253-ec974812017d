"use client";
import React, { FC, useState, useEffect } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { addDepartment, updateDepartment, deleteDepartment, IDepartmentAlter } from "@/services/departmentService";
import { departmentValidationSchema } from "@/utils/validationSchema";

import Loader from "../loader/Loader";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { DEPARTMENT_ALTER_MODE } from "../views/accessManagement/EmployeeManagement";
import { useTranslations } from "next-intl";
import { DepartmentForm, DepartmentFormData, DepartmentModalProps } from "@/interfaces/departmentInterface";
import { normalizeSpaces } from "@/utils/helper";

const DepartmentModal: FC<DepartmentModalProps> = ({ onClickCancel, onSubmitSuccess, disabled, department, mode }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isNameChanged, setIsNameChanged] = useState(false);
  const t = useTranslations();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<DepartmentFormData>({
    defaultValues: {
      name: department?.name || "",
    },
    resolver: yupResolver(departmentValidationSchema(t)),
    mode: "onChange",
  });

  // Watch for changes in the name field
  const currentName = watch("name");

  // Update isNameChanged when the name changes
  useEffect(() => {
    if (mode === DEPARTMENT_ALTER_MODE.EDIT && department) {
      // Trim both values to ensure whitespace doesn't trigger unnecessary changes
      const normalizedCurrentName = normalizeSpaces(currentName);
      const normalizedOriginalName = normalizeSpaces(department.name);
      setIsNameChanged(normalizedCurrentName !== normalizedOriginalName);
    }
  }, [currentName, department, mode]);

  const onSubmit = async (data: DepartmentFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      const requestData: DepartmentForm = {
        name: normalizeSpaces(data.name),
      };

      let response: IApiResponseCommonInterface<IDepartmentAlter>;

      try {
        if (mode === DEPARTMENT_ALTER_MODE.ADD) {
          response = await addDepartment(requestData);
        } else if (mode === DEPARTMENT_ALTER_MODE.EDIT && department) {
          response = await updateDepartment(department.id, requestData);
        } else if (mode === DEPARTMENT_ALTER_MODE.DELETE && department) {
          response = await deleteDepartment(department.id);
        } else {
          throw new Error("Invalid operation");
        }

        const result = response.data;

        if (result && result.success) {
          // Call onSubmitSuccess with the success message
          const successKey =
            mode === DEPARTMENT_ALTER_MODE.ADD
              ? "department_added"
              : mode === DEPARTMENT_ALTER_MODE.EDIT
                ? "department_updated"
                : "department_deleted";
          onSubmitSuccess(t(successKey));
          onClickCancel();
        } else {
          setSubmitError(result.message);
        }
      } catch (error) {
        const apiError = error as { response?: { status?: number; data?: { message?: string } } };
        if (apiError.response?.status === 401) {
          setSubmitError(t("department_auth_error"));
        } else {
          setSubmitError(t("department_operation_failed", { operation: mode }));
        }
      }
    } catch (error) {
      console.error(error);
      setSubmitError(t("department_unexpected_error"));
    } finally {
      setIsSubmitting(false);
    }
  };
  const getModalTitle = () => {
    switch (mode) {
      case DEPARTMENT_ALTER_MODE.ADD:
        return t("department_add");
      case DEPARTMENT_ALTER_MODE.EDIT:
        return t("department_update");
      case DEPARTMENT_ALTER_MODE.DELETE:
        return t("department_delete");
      default:
        return t("department_default");
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>{getModalTitle()}</h2>
            <Button className="modal-close-btn" onClick={onClickCancel} disabled={isSubmitting}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body">
            {mode === DEPARTMENT_ALTER_MODE.DELETE ? (
              <div>
                <p className="text-center mb-4">{t("department_delete_confirmation", { name: department?.name || "" })}</p>

                {submitError && (
                  <div className="alert alert-danger mb-3" role="alert">
                    {submitError}
                  </div>
                )}

                <div className="button-align mt-4">
                  <Button type="button" className="danger-btn rounded-md w-100" onClick={handleSubmit(onSubmit)} disabled={isSubmitting || disabled}>
                    <div className="d-flex align-items-center justify-content-center">
                      {isSubmitting && <Loader />}
                      <span className={isSubmitting ? "ms-2" : ""}>{getModalTitle()}</span>
                    </div>
                  </Button>
                  <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onClickCancel} disabled={isSubmitting || disabled}>
                    {t("cancel")}
                  </Button>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)}>
                <InputWrapper className="mb-4">
                  <InputWrapper.Label htmlFor="name" required className="fw-bold">
                    {t("department_name")}
                  </InputWrapper.Label>
                  <Textbox
                    className="form-control"
                    control={control}
                    name="name"
                    type="text"
                    placeholder={t("department_name_placeholder")}
                    disabled={isSubmitting || disabled}
                  />
                  <InputWrapper.Error message={errors?.name?.message || ""} />
                </InputWrapper>

                {submitError && (
                  <div className="alert alert-danger mb-3" role="alert">
                    {submitError}
                  </div>
                )}

                <div className="button-align mt-4">
                  <Button
                    type="submit"
                    className={`primary-btn rounded-md w-100 ${isSubmitting || disabled || (mode === DEPARTMENT_ALTER_MODE.EDIT && !isNameChanged) || !isValid || normalizeSpaces(currentName) === "" ? "truly-disabled" : ""}`}
                    disabled={
                      isSubmitting ||
                      disabled ||
                      (mode === DEPARTMENT_ALTER_MODE.EDIT && !isNameChanged) ||
                      !isValid ||
                      normalizeSpaces(currentName) === ""
                    }
                    title={
                      mode === DEPARTMENT_ALTER_MODE.EDIT && !isNameChanged
                        ? t("department_name_change_hint")
                        : normalizeSpaces(currentName) === ""
                          ? t("department_name_req")
                          : ""
                    }
                  >
                    <div className="d-flex align-items-center justify-content-center">
                      {isSubmitting && <Loader />}
                      <span className={isSubmitting ? "ms-2" : ""}>{getModalTitle()}</span>
                    </div>
                  </Button>
                  <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onClickCancel} disabled={isSubmitting || disabled}>
                    {t("cancel")}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DepartmentModal;
