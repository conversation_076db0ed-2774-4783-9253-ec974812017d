import Joi from "joi";
import { LABELS, EMAIL_REGEX } from "../../utils/constants";

const joiObject = Joi.object().options({ abortEarly: false });

export const emailValidation = joiObject.keys({
  email: Joi.string().regex(EMAIL_REGEX).label(LABELS.email).required(),
});

export const passwordValidation = joiObject.keys({
  password: Joi.string().label(LABELS.password).required(),
});

export const otpValidation = joiObject.keys({
  otp: Joi.string().required(),
});

export const verifyOtpValidation = joiObject
  .concat(otpValidation)
  .concat(emailValidation);

export const forgotPasswordValidation = joiObject.concat(emailValidation);
export const resendOtpValidation = joiObject.concat(emailValidation);

export const resetPasswordValidation = joiObject
  .concat(passwordValidation)
  .concat(emailValidation)
  .concat(otpValidation);

export const signInValidation = joiObject
  .concat(emailValidation)
  .concat(passwordValidation)
  .keys({
    fcmToken: Joi.string().optional(),
    timezone: Joi.string().optional(),
  });

export const updateTimeZoneValidation = joiObject.keys({
  timezone: Joi.string().required(),
});

export const deleteSessionValidation = joiObject.keys({
  userId: Joi.number().required(),
});
