{"name": "stratum9-interview", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "stratum9-interview", "version": "0.1.0", "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@reduxjs/toolkit": "^2.8.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@types/file-saver": "^2.0.7", "@types/html2canvas": "^0.5.35", "@types/js-cookie": "^3.0.6", "agora-rtc-react": "^2.4.0", "agora-rtc-sdk-ng": "^4.23.4", "axios": "^1.9.0", "bootstrap": "^5.3.6", "chart.js": "^4.5.0", "ckeditor5": "^45.1.0", "core-js": "^3.42.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "fullcalendar": "^6.1.17", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "next": "15.2.0", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "react": "^19.0.0", "react-avatar": "^5.0.4", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-jwt": "^1.3.0", "react-loading-skeleton": "^3.5.0", "react-otp-input": "^3.1.1", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-time-picker": "^7.0.0", "react-tooltip": "^5.28.1", "redux-persist": "^6.0.0", "sass": "^1.88.0", "secure-ls": "^2.0.0", "socket.io-client": "^4.8.1", "suneditor": "^2.47.5", "suneditor-react": "^3.6.1", "swiper": "^11.2.8", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/crypto-js": "^4.1.1", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19.1.5", "@types/react-infinite-scroll-component": "^4.2.5", "@types/tinymce": "^4.6.9", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-config-next": "^15.2.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "husky": "^8.0.0", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "typescript": "^5"}}, "node_modules/@agora-js/media": {"version": "4.23.4", "resolved": "https://registry.npmjs.org/@agora-js/media/-/media-4.23.4.tgz", "integrity": "sha512-5YEIT7zMsdojWN7ta7hve3JiKdH58tC69ofgiLbZKVF/ErqUhGoFi36FOllvvZocYB/hUFvFlCFXGTqHF/KeLA==", "license": "MIT", "dependencies": {"@agora-js/report": "4.23.4", "@agora-js/shared": "4.23.4", "agora-rte-extension": "^1.2.4", "axios": "^1.8.3", "webrtc-adapter": "8.2.0"}}, "node_modules/@agora-js/report": {"version": "4.23.4", "resolved": "https://registry.npmjs.org/@agora-js/report/-/report-4.23.4.tgz", "integrity": "sha512-3FPB6cRxEh2uAgIJIUtq4MYP+Y3XGXg9CvVTP52KMHSXy4CuImY4lN5B0rZAnYdmtGaJsjvsK4wA1ja5psYF3A==", "license": "MIT", "dependencies": {"@agora-js/shared": "4.23.4", "axios": "^1.8.3"}}, "node_modules/@agora-js/shared": {"version": "4.23.4", "resolved": "https://registry.npmjs.org/@agora-js/shared/-/shared-4.23.4.tgz", "integrity": "sha512-f/2qFGGHAs9X8OBoDiXzEoB2m7B+er8KNiCDlBTYAzRYNThPAa+sSWT6XP3csZQ+DSQtwh6O/sGNORuFNqTfTg==", "license": "MIT", "dependencies": {"axios": "^1.8.3", "ua-parser-js": "^0.7.34"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/generator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.1", "@babel/types": "^7.27.1", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/runtime": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/template": "^7.27.1", "@babel/types": "^7.27.1", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/globals": {"version": "11.12.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/types": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@ckeditor/ckeditor5-adapter-ckfinder": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-adapter-ckfinder/-/ckeditor5-adapter-ckfinder-45.2.0.tgz", "integrity": "sha512-wMDFBDGcUpq8jN/yftki1RxEz79FePfBedeS572+sWdvjm4lPDhkFQETOH0RwZ0TySNen3GJxFe8rKc4LI+iZA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-upload": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-alignment": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-alignment/-/ckeditor5-alignment-45.2.0.tgz", "integrity": "sha512-fARxsVWFZpWgTTiWdaY5bxrXnDokafTNk2cEZul5YtVBG3qFXz7MA/MzPc9+NFlu1Kju3WDos9synwxtDrMKTQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-autoformat": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-autoformat/-/ckeditor5-autoformat-45.2.0.tgz", "integrity": "sha512-CWu/oAdsKaAoe4vsoh4ghip9aLopjJGKxF2RuW7k423JUFXufLIB+Dz2Y/hWjtm2NnpcDMXsS18NePe3b33t1g==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-heading": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-autosave": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-autosave/-/ckeditor5-autosave-45.2.0.tgz", "integrity": "sha512-7qAyz/cj4EMDYbF1GzJN3fi8yvFsP+Ul7rbkmB1w6OXXnrxxgzv/H86obKylQLW/vCwa28pVjeNUvPwt2UxL7w==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-basic-styles": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-basic-styles/-/ckeditor5-basic-styles-45.2.0.tgz", "integrity": "sha512-MmL+25jt9pe6NmU7gUK1p8Kh/BTZejFCRFgfsUFmNz6BpVnD0GKMZ3aVdPIlkhyNlEQdcqfPoGnuTpdMAn7bMw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-block-quote": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-block-quote/-/ckeditor5-block-quote-45.2.0.tgz", "integrity": "sha512-mMwFBDMYTREzh0xdn3WyMPxEF5VLsfcu/Rc/bGLBEkvDqxPcqcXvRpMsW/ilmgFjtexsEkgUb/FbqvHDPRfH+A==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-enter": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-bookmark": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-bookmark/-/ckeditor5-bookmark-45.2.0.tgz", "integrity": "sha512-mpMXvRA/ZWHR7K9nFOUd517/7CgJxSNfbJpgibEcXJMvWQxeKBAMgLtrA8hKO3uX22qbxbBxw5EipyGz3b1cYA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-link": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-ckbox": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-ckbox/-/ckeditor5-ckbox-45.2.0.tgz", "integrity": "sha512-HTwlpWUiozn4OubRCavQ0BJMZKvBBQ8bwjdbsC3hmRHMQvh62808r454wj6fETy4JuTBCaRNqaw9sO3j2eCpsQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-cloud-services": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-image": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-upload": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "blurhash": "2.0.5", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-ckfinder": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-ckfinder/-/ckeditor5-ckfinder-45.2.0.tgz", "integrity": "sha512-16lsbm+evNJhQKXMI51fFgkjITQ74a/xOtRBdoGbE6ui1V8T3K1eM7woFMguEwEAUMevgfUWOgAlBo95GjEZEw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-image": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-clipboard": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-clipboard/-/ckeditor5-clipboard-45.2.0.tgz", "integrity": "sha512-9o1xdbQvkiCeOxIMFpJBgZQSpvBqQK9w/A1WtjzN64Wmdjr5brtdfm1j0TwaZXXGkoLKdvNPJZXuBwQYU2SWhA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-cloud-services": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-cloud-services/-/ckeditor5-cloud-services-45.2.0.tgz", "integrity": "sha512-Cjbyk0sU1hox/683E/NGZEaFFBLzPbfNhg4go+T/P1ig6rUc0VdlRitxNI5byvGlKA/36EprRSqoVecVCjWnPg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-code-block": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-code-block/-/ckeditor5-code-block-45.2.0.tgz", "integrity": "sha512-lU5PIc7hDUqZ7GEVrZQBI863cMeoIFWhuY9yVBzOgsGj4J6FcO1GeRGOUNeedCnwjIIRh9+hGEBgYfc8dMa5yg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-enter": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-core": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-core/-/ckeditor5-core-45.2.0.tgz", "integrity": "sha512-mNHNCReBAeBLkMSffq54iD/l5p+eViYWOo+iRoUyXw+qNStmNRWac2Gkl4oWvwn05At1Gpprzs4LqHeWTEMNcw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-watchdog": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-easy-image": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-easy-image/-/ckeditor5-easy-image-45.2.0.tgz", "integrity": "sha512-6wPMwLiYtYQU3SuPQv/B0ujz7IMBEzETbKfqNtYzDk1MvzSdvDSfI5GjIH1tqTyGmK7wtEaJVZLI2B1Wu/R1mA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-cloud-services": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-upload": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-editor-balloon": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-balloon/-/ckeditor5-editor-balloon-45.2.0.tgz", "integrity": "sha512-z+aT3uHtZ10KbQnHYaAkZK4J7A17jjX9yU6JTnDAeMH5pFblXgvroe1zzMXv9SSOxmr2v5p95gE8GgNsEWSG1w==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-editor-classic": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-classic/-/ckeditor5-editor-classic-45.2.0.tgz", "integrity": "sha512-6WbaINZsmCdXGftHGVu9qA7i+qnuEpm4LcQYfBthAdV9k0C/fp8j9IV26SR0Q9PfPk/KaT2XG5wB0uBxn72q3g==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-editor-decoupled": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-decoupled/-/ckeditor5-editor-decoupled-45.2.0.tgz", "integrity": "sha512-utIwy3xFdpgbZ+cBTnqdwRguYhENjFOHDTR3cukAE8mEm+Wq7hFy0muybQ/HnnRFJVZBRnEZTXr/ZQFEGg42bA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-editor-inline": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-inline/-/ckeditor5-editor-inline-45.2.0.tgz", "integrity": "sha512-JX0YCj8mCV7SLZCFrkIhXvK42up9eKRQDcLOj9i5XxO5K+J8i+frqMCJCptHK8VH4ibxCRNojiGoudY5LADRMQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-editor-multi-root": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-multi-root/-/ckeditor5-editor-multi-root-45.2.0.tgz", "integrity": "sha512-MmVq3Dis/3gKME26aVeIHWp+ESqoi9VJTazzjTBuqtuplwCfG2/NhaYvMSX4goN9HGkmXGtfgRONlfWSaLQiPA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-emoji": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-emoji/-/ckeditor5-emoji-45.2.0.tgz", "integrity": "sha512-NtUTMRyv26iWd14Rr6G0zbwLeRsInj51WzsjPSf2YPw0/yqhV3ul9q4mYELfKBEG4op/uReH0ZTPbF7DQNUstg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-mention": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0", "fuzzysort": "3.1.0"}}, "node_modules/@ckeditor/ckeditor5-engine": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-engine/-/ckeditor5-engine-45.2.0.tgz", "integrity": "sha512-jr9/pT5TaY1rew6ncNX4z7Dtjni5RmRf7u4mEt1TKPKhzDA5vszN+ZMj7wwa3nLtLLMiOUej5da2z3aDjvWz6w==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-utils": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-enter": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-enter/-/ckeditor5-enter-45.2.0.tgz", "integrity": "sha512-E1fAKr3rFknUPFfVH1lbemLtFCMmysNXY1BKT+LFBhpomk+aJ6yk53EJdqPSYQdbPCQHg9ebqAA5JVMxxaFshA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-essentials": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-essentials/-/ckeditor5-essentials-45.2.0.tgz", "integrity": "sha512-z0CSUMTPHSr29agNg76K3rwI5mh3EAYvlu7BIzgzfZqrKVdAiJHwzbdBqU/LU2JorhLl4ZTHVb3H4wk4fHzxug==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-enter": "45.2.0", "@ckeditor/ckeditor5-select-all": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-undo": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-find-and-replace": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-find-and-replace/-/ckeditor5-find-and-replace-45.2.0.tgz", "integrity": "sha512-Fn7vhHX8baV7MQgm5fr15sG8LS6QUyqvqdvY05gQuv4+CVs3HBgK2zdNznQwUFoI+Zlyk0erEhKnWdyTCYRcxQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-font": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-font/-/ckeditor5-font-45.2.0.tgz", "integrity": "sha512-EEamXmHw+Kckhjk7JmaxA6fDVK7P6rwAWGqmZiJzwfO0g7BRwzp/BtjTmXysbWVvaCxm7bbnib/OKX6MP8ZyWw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-fullscreen": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-fullscreen/-/ckeditor5-fullscreen-45.2.0.tgz", "integrity": "sha512-bYj3jt42X7qPpkJUGc4N7n+EzwsoHIVInVAHYXHeJLkN8axuiLraGAe+pguZvwRalMJOkIDqXj/flpm8go2x9w==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-editor-classic": "45.2.0", "@ckeditor/ckeditor5-editor-decoupled": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-heading": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-heading/-/ckeditor5-heading-45.2.0.tgz", "integrity": "sha512-yURkKMAtr9RY4LutfLSTSw8L/uRpWRi77NDFeknRv2h90/ouOImXBQuluwmIWfSgQ2o6HoTPvb+nd1EItF1uqQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-paragraph": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-highlight": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-highlight/-/ckeditor5-highlight-45.2.0.tgz", "integrity": "sha512-+JRQ9wfO0dBmla7iA5KVbMWHAgIf7vxRaAxnRixzeZJyY46p39rZmKK6h/olwMzdfiiAoufGnJSlQRsjiPTC8g==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-horizontal-line": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-horizontal-line/-/ckeditor5-horizontal-line-45.2.0.tgz", "integrity": "sha512-wOdM53nqs9rTNHj3I5jfQnac2rrEqirMEu7wCDLBzGKDCsr7X7wBeH6oMLM7ML/+g041p3xXLRKrX3K1mBsXZg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-html-embed": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-html-embed/-/ckeditor5-html-embed-45.2.0.tgz", "integrity": "sha512-SfkmolUysYvCGc05A2t8AP7F6DH3+kuO2bKLu38mv0mVkm4bD6/7CcZh+InN75YQD42uAL5DhmGqUZklrgNdSQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-html-support": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-html-support/-/ckeditor5-html-support-45.2.0.tgz", "integrity": "sha512-MRKO/hMQXwOOcXWbQra5dD2ztSFnnAQAO7hlKB0vYbhRcGjruhivDOHNrDjQkkdckJHv/C/xTKuvtE6RFX34tw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-enter": "45.2.0", "@ckeditor/ckeditor5-heading": "45.2.0", "@ckeditor/ckeditor5-image": "45.2.0", "@ckeditor/ckeditor5-list": "45.2.0", "@ckeditor/ckeditor5-table": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-icons": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-icons/-/ckeditor5-icons-45.2.0.tgz", "integrity": "sha512-8BSpnnBYnhfRWd5QZuqzwTRmG8BL+WXB612Alo26T+EWJi3tiuEgNq1qm9EUVKj6j9dNiq87dNXKyQQY7B/7Gw==", "license": "SEE LICENSE IN LICENSE.md"}, "node_modules/@ckeditor/ckeditor5-image": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-image/-/ckeditor5-image-45.2.0.tgz", "integrity": "sha512-x218ePIog5HZ83H7m3OeAuH4gdclRN+sJczXZAi9zGSYuRzUxHmqcXh2s4rDyrLc0tFf2TB9W6jjrsqokmXZTA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-undo": "45.2.0", "@ckeditor/ckeditor5-upload": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-indent": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-indent/-/ckeditor5-indent-45.2.0.tgz", "integrity": "sha512-gdJQugvCrT5OpDdQCvCIl+zsgDxWFh8q+Aj+aL7We2LDDXg2rlbDuT/lBbXPEBObyuLBBo0/MthlwcirkBoXKg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-heading": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-list": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-language": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-language/-/ckeditor5-language-45.2.0.tgz", "integrity": "sha512-1HW4Uz/7DwfKUvs7X9j5oN4+U6MgxH/uyrGbA3CZ9U0UyWDvddB0sUhiMVm1lv56tup6rnlilwGZRetwd6YXrA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-link": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-link/-/ckeditor5-link-45.2.0.tgz", "integrity": "sha512-0KHaoF+/tpHcJljdxdnrKRrPgtn8YVVPz/wyuTITeg0aO+K6r7UbtrB/LEsLOv8MUO/KLcb9LtlgCZw7Jxm4VQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-image": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-list": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-list/-/ckeditor5-list-45.2.0.tgz", "integrity": "sha512-pXGpmkPhHxVmIDJnfjAaCuSS1MxbGJ29vJ+tHy9avTdN4oQjQJX3HMaKg7Fxq8iqsHKrXvMFM3k3kLq+ff2IFw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-enter": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-markdown-gfm": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-markdown-gfm/-/ckeditor5-markdown-gfm-45.2.0.tgz", "integrity": "sha512-o6II688bVNBEC/or4v1JwSW0O1aAroyIbv0L5eimoMhCDPp9/6HRK4gSLKcC9nemwKKko0X+fSj0R+E/6aXpXw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@types/marked": "4.3.2", "@types/turndown": "5.0.5", "ckeditor5": "45.2.0", "marked": "4.0.12", "turndown": "7.2.0", "turndown-plugin-gfm": "1.0.2"}}, "node_modules/@ckeditor/ckeditor5-media-embed": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-media-embed/-/ckeditor5-media-embed-45.2.0.tgz", "integrity": "sha512-VIoc/1T6XtM6M6PblWHu/hKShVlVOXx+dzMfg2W5dX99JfWXoevrABIqVNLlHnDhhM9SNDqW94Sos2yfZ+BeVQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-undo": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-mention": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-mention/-/ckeditor5-mention-45.2.0.tgz", "integrity": "sha512-nBBEArPfYiopARSOI2TwbYCxLk9ALdKc/3qh2D2rjQwvoJ6zn7nBBoiDBMbhO2XC6J+AkUYQNEqcNVS4j3HIew==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-minimap": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-minimap/-/ckeditor5-minimap-45.2.0.tgz", "integrity": "sha512-24X53UrzhzppPHECa17Xb69EcnXuV/ktaSTbYFncIDbBFef4GwQjkQweplmXdgNRh630GLlhQc/GCCtbhIvDlA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-page-break": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-page-break/-/ckeditor5-page-break-45.2.0.tgz", "integrity": "sha512-hKjc/pJS8CbCwQXJMmBb0EbqBjCb9811E+08SNo4o0AztZfFlxvZPcua4GjckWWQYYtjdG7GviTe+pmnR4vT+w==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-paragraph": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-paragraph/-/ckeditor5-paragraph-45.2.0.tgz", "integrity": "sha512-6Cb7PdZigDe/wxDbIA6ZEmQFqDzOXfkzS8frXiQZ5EoU9DcylrF9GgRQhCO3vC2v0SoymqBZ72ldxGxHGhaVCw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-paste-from-office": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-paste-from-office/-/ckeditor5-paste-from-office-45.2.0.tgz", "integrity": "sha512-lEvFlWW1xSzwX9+d8cfES7quvIjifbkKp3zy2x4pW3hSsk7ba4zNuV0ML1JRea95u79RjCNLYnK2FU3q0AXKUg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-remove-format": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-remove-format/-/ckeditor5-remove-format-45.2.0.tgz", "integrity": "sha512-NHHsbjSw0gXGgDW8eyuOIHRTjs6BtU3YJwhXFqLXP6h+RM1gs9xTypZoKAPF6IYqS8F9+73SNy50/bmSzGfcFg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-restricted-editing": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-restricted-editing/-/ckeditor5-restricted-editing-45.2.0.tgz", "integrity": "sha512-HegnRobGiLHWkBtZcQVuPyUmbU6R5sFU0w2CKLLjQzqpgs9i1g2mTqcBr3GBPUdOty0v0nfc3r0Q0BM2X8Rtow==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-select-all": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-select-all/-/ckeditor5-select-all-45.2.0.tgz", "integrity": "sha512-Q7qjnwnkDvev7wsIYV7kcqtap3Ki5PIUlEBisB8IBrnS+6JjZtkbVSbuRYK2E6Qfe0gDdz//tnOoUc4Hb65uWA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-show-blocks": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-show-blocks/-/ckeditor5-show-blocks-45.2.0.tgz", "integrity": "sha512-CCgj9R27PqMAat62COD/vfnX/YHWmFYzQCqyT88XuUIxB3GltsHEimxsZ+4uXo8G433bHeHhQWuOc9zWBh6CzQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-source-editing": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-source-editing/-/ckeditor5-source-editing-45.2.0.tgz", "integrity": "sha512-3DZPIvuvjgpfD3kr6/TE210+wVTFHwML1RAKU0fcBfWbgz/OPr81DJAU35kcpaU4vwahurLAwc15iM7D+O/TGg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-theme-lark": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-special-characters": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-special-characters/-/ckeditor5-special-characters-45.2.0.tgz", "integrity": "sha512-ZTGiLAiIrYRXJnnXjM0s4VyEt3Pj5BIsHvdfEy0sM2XdslQ3xjPHDvBz2r0hOSfysSWr1SZ4kPf2dwU1256Bxg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-style": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-style/-/ckeditor5-style-45.2.0.tgz", "integrity": "sha512-JQRGAf2FeQQp90oFNfg/Xq65tizF28Wbt25AXxT1g/WvaCeY8QW31cJc0joC8CKVvYGlFrzj/cN25TAwx/B8vQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-html-support": "45.2.0", "@ckeditor/ckeditor5-list": "45.2.0", "@ckeditor/ckeditor5-table": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-table": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-table/-/ckeditor5-table-45.2.0.tgz", "integrity": "sha512-sMYpAaHTx418RQwIsiHEQ1RXOhkmxXZxfVoThfFdCvdSVhVruCRmrXc6Thn809pJEFqAnAOe3t7Pf5gNIdFnrQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.2.0", "@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@ckeditor/ckeditor5-widget": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-theme-lark": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-theme-lark/-/ckeditor5-theme-lark-45.2.0.tgz", "integrity": "sha512-oxW8jaa8YZcFoq9EgZUpRZhJS/YDlq7FBDahl92CYjhfHeCCXNWLpG48bx8OSLTLviFE+5wAR2fwvetwMhEkeA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-ui": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-typing": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-typing/-/ckeditor5-typing-45.2.0.tgz", "integrity": "sha512-kIolXzb60SV9+oAA2tRz0gjsZgb/Tjyra6Jy1S+K7YPh8tMGaDZEhWzwNf4IZl6J0Ue840XwStZgA7Wnf1y1fw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-ui": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-ui/-/ckeditor5-ui-45.2.0.tgz", "integrity": "sha512-nUyE4miGnBguqWpLkbtyQlGxmTq1Z9HndZ4J3r2+Db85CXVAQNvnBT62T1wdQlUp9hArEp16BgtYO8PwXTT6og==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-editor-multi-root": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "@types/color-convert": "2.0.4", "color-convert": "2.0.1", "color-parse": "1.4.2", "es-toolkit": "1.32.0", "vanilla-colorful": "0.7.2"}}, "node_modules/@ckeditor/ckeditor5-undo": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-undo/-/ckeditor5-undo-45.2.0.tgz", "integrity": "sha512-5ySaYtW2wgyYJCZauwJF96TbrCJBghzq28mbIay0M8G0BjQQNtSo8uDFlhYWVyoO0HF0qOFw5mbTsK27ggRS4A==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-upload": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-upload/-/ckeditor5-upload-45.2.0.tgz", "integrity": "sha512-t4oYi3OK/dYzVjPzZI55VxmHqfc9rA8GeVonQrnLOpQfBW3G9BHxDn4xzLhgGbKqyMKwDqYb1XIUoFrI84pvMg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0"}}, "node_modules/@ckeditor/ckeditor5-utils": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-utils/-/ckeditor5-utils-45.2.0.tgz", "integrity": "sha512-SFRh25D5szN+AhnmzNwgef8zJFQisl9tP2f3GR7Mr4bVHzzyz/9SY/ZkGTwOG1Q5lt0kbwQyL3WWpDLfVkWPpg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-ui": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-watchdog": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-watchdog/-/ckeditor5-watchdog-45.2.0.tgz", "integrity": "sha512-AnypB1eLI6xb6FEWzqkNMzYfmowE0VFlbtUAve6EPHQkRgBM1AAYoxoF00GpFDfpVanBdsE5T2g2HHndCvaR/g==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-editor-multi-root": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-widget": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-widget/-/ckeditor5-widget-45.2.0.tgz", "integrity": "sha512-Iu0mIp+T1Vb0FstIMW1w8qh3WeCE9jnUUEvTDKQEAp46r3ldjH9znWgkXqYqxyMGME6d8x2pL1Ewb14sAoUiHw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-engine": "45.2.0", "@ckeditor/ckeditor5-enter": "45.2.0", "@ckeditor/ckeditor5-icons": "45.2.0", "@ckeditor/ckeditor5-typing": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@ckeditor/ckeditor5-word-count": {"version": "45.2.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-word-count/-/ckeditor5-word-count-45.2.0.tgz", "integrity": "sha512-zGhP62Nfebr7EKlGX509fkeO588DHL2YnkQzgEBZA4CkvU/5lden1pZ1gEK/dD3qKuLgujCXTWt+TuiCn8Z8QQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.2.0", "@ckeditor/ckeditor5-ui": "45.2.0", "@ckeditor/ckeditor5-utils": "45.2.0", "ckeditor5": "45.2.0", "es-toolkit": "1.32.0"}}, "node_modules/@emnapi/core": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/@emnapi/core/-/core-1.4.3.tgz", "integrity": "sha512-4m62DuCE07lw01soJwPiBGC0nAww0Q+RY70VZ+n49yDIO13yyinhbWCeNnaob0lakDtWQzSdtNWzJeOJt2ma+g==", "dev": true, "optional": true, "dependencies": {"@emnapi/wasi-threads": "1.0.2", "tslib": "^2.4.0"}}, "node_modules/@emnapi/runtime": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.4.3.tgz", "integrity": "sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==", "optional": true, "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@emnapi/wasi-threads": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@emnapi/wasi-threads/-/wasi-threads-1.0.2.tgz", "integrity": "sha512-5n3nTJblwRi8LlXkJ9eBzu+kZR8Yxcc7ubakyQTFzPMtIhFpUBRbsnc2Dv88IZDIbCDlBiWrknhB4Lsz7mg6BA==", "dev": true, "optional": true, "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@emotion/babel-plugin": {"version": "11.13.5", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.16.7", "@babel/runtime": "^7.18.3", "@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/serialize": "^1.3.3", "babel-plugin-macros": "^3.1.0", "convert-source-map": "^1.5.0", "escape-string-regexp": "^4.0.0", "find-root": "^1.1.0", "source-map": "^0.5.7", "stylis": "4.2.0"}}, "node_modules/@emotion/cache": {"version": "11.14.0", "license": "MIT", "dependencies": {"@emotion/memoize": "^0.9.0", "@emotion/sheet": "^1.4.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "stylis": "4.2.0"}}, "node_modules/@emotion/hash": {"version": "0.9.2", "license": "MIT"}, "node_modules/@emotion/memoize": {"version": "0.9.0", "license": "MIT"}, "node_modules/@emotion/react": {"version": "11.14.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.13.5", "@emotion/cache": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/use-insertion-effect-with-fallbacks": "^1.2.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "hoist-non-react-statics": "^3.3.1"}, "peerDependencies": {"react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@emotion/serialize": {"version": "1.3.3", "license": "MIT", "dependencies": {"@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/unitless": "^0.10.0", "@emotion/utils": "^1.4.2", "csstype": "^3.0.2"}}, "node_modules/@emotion/sheet": {"version": "1.4.0", "license": "MIT"}, "node_modules/@emotion/unitless": {"version": "0.10.0", "license": "MIT"}, "node_modules/@emotion/use-insertion-effect-with-fallbacks": {"version": "1.2.0", "license": "MIT", "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/@emotion/utils": {"version": "1.4.2", "license": "MIT"}, "node_modules/@emotion/weak-memoize": {"version": "0.4.0", "license": "MIT"}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.20.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-helpers": {"version": "0.2.2", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.13.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/js": {"version": "9.26.0", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.2.8", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.13.0", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@floating-ui/core": {"version": "1.7.0", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/dom": {"version": "1.7.0", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.7.0", "@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/react": {"version": "0.27.12", "license": "MIT", "dependencies": {"@floating-ui/react-dom": "^2.1.3", "@floating-ui/utils": "^0.2.9", "tabbable": "^6.0.0"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}}, "node_modules/@floating-ui/react-dom": {"version": "2.1.3", "license": "MIT", "dependencies": {"@floating-ui/dom": "^1.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.9", "license": "MIT"}, "node_modules/@formatjs/ecma402-abstract": {"version": "2.3.4", "license": "MIT", "dependencies": {"@formatjs/fast-memoize": "2.2.7", "@formatjs/intl-localematcher": "0.6.1", "decimal.js": "^10.4.3", "tslib": "^2.8.0"}}, "node_modules/@formatjs/ecma402-abstract/node_modules/@formatjs/intl-localematcher": {"version": "0.6.1", "license": "MIT", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@formatjs/fast-memoize": {"version": "2.2.7", "license": "MIT", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@formatjs/icu-messageformat-parser": {"version": "2.11.2", "license": "MIT", "dependencies": {"@formatjs/ecma402-abstract": "2.3.4", "@formatjs/icu-skeleton-parser": "1.8.14", "tslib": "^2.8.0"}}, "node_modules/@formatjs/icu-skeleton-parser": {"version": "1.8.14", "license": "MIT", "dependencies": {"@formatjs/ecma402-abstract": "2.3.4", "tslib": "^2.8.0"}}, "node_modules/@formatjs/intl-localematcher": {"version": "0.5.10", "license": "MIT", "dependencies": {"tslib": "2"}}, "node_modules/@fullcalendar/core": {"version": "6.1.17", "license": "MIT", "dependencies": {"preact": "~10.12.1"}}, "node_modules/@fullcalendar/core/node_modules/preact": {"version": "10.12.1", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/preact"}}, "node_modules/@fullcalendar/daygrid": {"version": "6.1.17", "license": "MIT", "peerDependencies": {"@fullcalendar/core": "~6.1.17"}}, "node_modules/@fullcalendar/interaction": {"version": "6.1.17", "license": "MIT", "peerDependencies": {"@fullcalendar/core": "~6.1.17"}}, "node_modules/@fullcalendar/list": {"version": "6.1.17", "license": "MIT", "peerDependencies": {"@fullcalendar/core": "~6.1.17"}}, "node_modules/@fullcalendar/multimonth": {"version": "6.1.17", "license": "MIT", "dependencies": {"@fullcalendar/daygrid": "~6.1.17"}, "peerDependencies": {"@fullcalendar/core": "~6.1.17"}}, "node_modules/@fullcalendar/react": {"version": "6.1.17", "license": "MIT", "peerDependencies": {"@fullcalendar/core": "~6.1.17", "react": "^16.7.0 || ^17 || ^18 || ^19", "react-dom": "^16.7.0 || ^17 || ^18 || ^19"}}, "node_modules/@fullcalendar/timegrid": {"version": "6.1.17", "license": "MIT", "dependencies": {"@fullcalendar/daygrid": "~6.1.17"}, "peerDependencies": {"@fullcalendar/core": "~6.1.17"}}, "node_modules/@hookform/resolvers": {"version": "5.0.1", "license": "MIT", "dependencies": {"@standard-schema/utils": "^0.3.0"}, "peerDependencies": {"react-hook-form": "^7.55.0"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@img/sharp-win32-x64": {"version": "0.33.5", "cpu": ["x64"], "license": "Apache-2.0 AND LGPL-3.0-or-later", "optional": true, "os": ["win32"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@kurkle/color": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.4.tgz", "integrity": "sha512-M5UknZPHRu3DEDWoipU6sE8PdkZ6Z/S+v4dD+Ke8IaNlpdSQah50lz1KtcFBa2vsdOnwbbnxJwVM4wty6udA5w==", "license": "MIT"}, "node_modules/@mixmark-io/domino": {"version": "2.2.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@modelcontextprotocol/sdk": {"version": "1.11.2", "dev": true, "license": "MIT", "dependencies": {"content-type": "^1.0.5", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "eventsource": "^3.0.2", "express": "^5.0.1", "express-rate-limit": "^7.5.0", "pkce-challenge": "^5.0.0", "raw-body": "^3.0.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.1"}, "engines": {"node": ">=18"}}, "node_modules/@next/env": {"version": "15.2.0", "license": "MIT"}, "node_modules/@next/eslint-plugin-next": {"version": "15.2.0", "dev": true, "license": "MIT", "dependencies": {"fast-glob": "3.3.1"}}, "node_modules/@next/swc-darwin-arm64": {"version": "15.2.0", "resolved": "https://registry.npmjs.org/@next/swc-darwin-arm64/-/swc-darwin-arm64-15.2.0.tgz", "integrity": "sha512-rlp22GZwNJjFCyL7h5wz9vtpBVuCt3ZYjFWpEPBGzG712/uL1bbSkS675rVAUCRZ4hjoTJ26Q7IKhr5DfJrHDA==", "cpu": ["arm64"], "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-darwin-x64": {"version": "15.2.0", "resolved": "https://registry.npmjs.org/@next/swc-darwin-x64/-/swc-darwin-x64-15.2.0.tgz", "integrity": "sha512-DiU85EqSHogCz80+sgsx90/ecygfCSGl5P3b4XDRVZpgujBm5lp4ts7YaHru7eVTyZMjHInzKr+w0/7+qDrvMA==", "cpu": ["x64"], "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-arm64-gnu": {"version": "15.2.0", "resolved": "https://registry.npmjs.org/@next/swc-linux-arm64-gnu/-/swc-linux-arm64-gnu-15.2.0.tgz", "integrity": "sha512-VnpoMaGukiNWVxeqKHwi8MN47yKGyki5q+7ql/7p/3ifuU2341i/gDwGK1rivk0pVYbdv5D8z63uu9yMw0QhpQ==", "cpu": ["arm64"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-arm64-musl": {"version": "15.2.0", "resolved": "https://registry.npmjs.org/@next/swc-linux-arm64-musl/-/swc-linux-arm64-musl-15.2.0.tgz", "integrity": "sha512-ka97/ssYE5nPH4Qs+8bd8RlYeNeUVBhcnsNUmFM6VWEob4jfN9FTr0NBhXVi1XEJpj3cMfgSRW+LdE3SUZbPrw==", "cpu": ["arm64"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-x64-gnu": {"version": "15.2.0", "resolved": "https://registry.npmjs.org/@next/swc-linux-x64-gnu/-/swc-linux-x64-gnu-15.2.0.tgz", "integrity": "sha512-zY1JduE4B3q0k2ZCE+DAF/1efjTXUsKP+VXRtrt/rJCTgDlUyyryx7aOgYXNc1d8gobys/Lof9P9ze8IyRDn7Q==", "cpu": ["x64"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-x64-musl": {"version": "15.2.0", "resolved": "https://registry.npmjs.org/@next/swc-linux-x64-musl/-/swc-linux-x64-musl-15.2.0.tgz", "integrity": "sha512-QqvLZpurBD46RhaVaVBepkVQzh8xtlUN00RlG4Iq1sBheNugamUNPuZEH1r9X1YGQo1KqAe1iiShF0acva3jHQ==", "cpu": ["x64"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-win32-arm64-msvc": {"version": "15.2.0", "resolved": "https://registry.npmjs.org/@next/swc-win32-arm64-msvc/-/swc-win32-arm64-msvc-15.2.0.tgz", "integrity": "sha512-ODZ0r9WMyylTHAN6pLtvUtQlGXBL9voljv6ujSlcsjOxhtXPI1Ag6AhZK0SE8hEpR1374WZZ5w33ChpJd5fsjw==", "cpu": ["arm64"], "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-win32-x64-msvc": {"version": "15.2.0", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@nolyfill/is-core-module": {"version": "1.0.39", "dev": true, "license": "MIT", "engines": {"node": ">=12.4.0"}}, "node_modules/@panva/hkdf": {"version": "1.2.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/@parcel/watcher": {"version": "2.5.1", "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1"}}, "node_modules/@parcel/watcher-win32-x64": {"version": "2.5.1", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher/node_modules/detect-libc": {"version": "1.0.3", "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/@pkgr/core": {"version": "0.2.4", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/pkgr"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "license": "MIT", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@reduxjs/toolkit": {"version": "2.8.2", "license": "MIT", "dependencies": {"@standard-schema/spec": "^1.0.0", "@standard-schema/utils": "^0.3.0", "immer": "^10.0.3", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "reselect": "^5.1.0"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18 || ^19", "react-redux": "^7.2.1 || ^8.1.3 || ^9.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-redux": {"optional": true}}}, "node_modules/@rtsao/scc": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/@rushstack/eslint-patch": {"version": "1.11.0", "dev": true, "license": "MIT"}, "node_modules/@schummar/icu-type-parser": {"version": "1.21.5", "license": "MIT"}, "node_modules/@socket.io/component-emitter": {"version": "3.1.2", "license": "MIT"}, "node_modules/@standard-schema/spec": {"version": "1.0.0", "license": "MIT"}, "node_modules/@standard-schema/utils": {"version": "0.3.0", "license": "MIT"}, "node_modules/@stripe/react-stripe-js": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/@stripe/react-stripe-js/-/react-stripe-js-3.7.0.tgz", "integrity": "sha512-PYls/2S9l0FF+2n0wHaEJsEU8x7CmBagiH7zYOsxbBlLIHEsqUIQ4MlIAbV9Zg6xwT8jlYdlRIyBTHmO3yM7kQ==", "dependencies": {"prop-types": "^15.7.2"}, "peerDependencies": {"@stripe/stripe-js": ">=1.44.1 <8.0.0", "react": ">=16.8.0 <20.0.0", "react-dom": ">=16.8.0 <20.0.0"}}, "node_modules/@stripe/stripe-js": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@stripe/stripe-js/-/stripe-js-7.4.0.tgz", "integrity": "sha512-lQHQPfXPTBeh0XFjq6PqSBAyR7umwcJbvJhXV77uGCUDD6ymXJU/f2164ydLMLCCceNuPlbV9b+1smx98efwWQ==", "engines": {"node": ">=12.16"}}, "node_modules/@swc/counter": {"version": "0.1.3", "license": "Apache-2.0"}, "node_modules/@swc/helpers": {"version": "0.5.15", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@types/color-convert": {"version": "2.0.4", "license": "MIT", "dependencies": {"@types/color-name": "^1.1.0"}}, "node_modules/@types/color-name": {"version": "1.1.5", "license": "MIT"}, "node_modules/@types/crypto-js": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/@types/crypto-js/-/crypto-js-4.2.2.tgz", "integrity": "sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==", "dev": true, "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.7", "dev": true, "license": "MIT"}, "node_modules/@types/file-saver": {"version": "2.0.7", "license": "MIT"}, "node_modules/@types/html2canvas": {"version": "0.5.35", "license": "MIT", "dependencies": {"@types/jquery": "*"}}, "node_modules/@types/jquery": {"version": "3.5.32", "license": "MIT", "dependencies": {"@types/sizzle": "*"}}, "node_modules/@types/js-cookie": {"version": "3.0.6", "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "dev": true, "license": "MIT"}, "node_modules/@types/json5": {"version": "0.0.29", "dev": true, "license": "MIT"}, "node_modules/@types/lodash": {"version": "4.17.17", "dev": true, "license": "MIT"}, "node_modules/@types/marked": {"version": "4.3.2", "license": "MIT"}, "node_modules/@types/node": {"version": "20.17.46", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.19.2"}}, "node_modules/@types/parse-json": {"version": "4.0.2", "license": "MIT"}, "node_modules/@types/raf": {"version": "3.4.3", "license": "MIT", "optional": true}, "node_modules/@types/react": {"version": "19.1.3", "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "19.1.5", "dev": true, "license": "MIT", "peerDependencies": {"@types/react": "^19.0.0"}}, "node_modules/@types/react-infinite-scroll-component": {"version": "4.2.5", "dev": true, "license": "MIT", "dependencies": {"@types/react": "*"}}, "node_modules/@types/react-transition-group": {"version": "4.4.12", "license": "MIT", "peerDependencies": {"@types/react": "*"}}, "node_modules/@types/sizzle": {"version": "2.3.9", "license": "MIT"}, "node_modules/@types/tinymce": {"version": "4.6.9", "dev": true, "license": "MIT", "dependencies": {"@types/jquery": "*"}}, "node_modules/@types/trusted-types": {"version": "2.0.7", "license": "MIT", "optional": true}, "node_modules/@types/turndown": {"version": "5.0.5", "license": "MIT"}, "node_modules/@types/use-sync-external-store": {"version": "0.0.6", "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "8.32.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.32.1", "@typescript-eslint/type-utils": "8.32.1", "@typescript-eslint/utils": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.0.0 || ^8.0.0-alpha.0", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore": {"version": "7.0.4", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@typescript-eslint/parser": {"version": "8.32.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "8.32.1", "@typescript-eslint/types": "8.32.1", "@typescript-eslint/typescript-estree": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "8.32.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "8.32.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "8.32.1", "@typescript-eslint/utils": "8.32.1", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/types": {"version": "8.32.1", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "8.32.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/fast-glob": {"version": "3.3.3", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@typescript-eslint/utils": {"version": "8.32.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.32.1", "@typescript-eslint/types": "8.32.1", "@typescript-eslint/typescript-estree": "8.32.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "8.32.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.32.1", "eslint-visitor-keys": "^4.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@unrs/resolver-binding-win32-x64-msvc": {"version": "1.7.2", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@wojtekmaj/date-utils": {"version": "1.5.1", "license": "MIT", "funding": {"url": "https://github.com/wojtekmaj/date-utils?sponsor=1"}}, "node_modules/accepts": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.14.1", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/agora-rtc-react": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/agora-rtc-react/-/agora-rtc-react-2.4.0.tgz", "integrity": "sha512-2hNoQiIbRP9pfERILoVb02Eu/7QW3uTiVdsJe7LFnYP/RqI5BBDEXYC2X8hxmmhbWYTuBYR5uYKacbVPt459Jw==", "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"react": ">=16.8"}}, "node_modules/agora-rtc-sdk-ng": {"version": "4.23.4", "resolved": "https://registry.npmjs.org/agora-rtc-sdk-ng/-/agora-rtc-sdk-ng-4.23.4.tgz", "integrity": "sha512-WbcGFZ6bpB+GSPFNGTrUwq4rSHkW7pLfr1XLJZm3EDTgfZI0W9ZHFtIqiBq8mBJ0q5S86JFkJmTRTRB7OpQU+w==", "license": "MIT", "dependencies": {"@agora-js/media": "4.23.4", "@agora-js/report": "4.23.4", "@agora-js/shared": "4.23.4", "agora-rte-extension": "^1.2.4", "axios": "^1.8.3", "formdata-polyfill": "^4.0.7", "pako": "^2.1.0", "ua-parser-js": "^0.7.34", "webrtc-adapter": "8.2.0"}}, "node_modules/agora-rte-extension": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/agora-rte-extension/-/agora-rte-extension-1.2.4.tgz", "integrity": "sha512-0ovZz1lbe30QraG1cU+ji7EnQ8aUu+Hf3F+a8xPml3wPOyUQEK6CTdxV9kMecr9t+fIDrGeW7wgJTsM1DQE7Nw==", "license": "ISC"}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-escapes": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"environment": "^1.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/aria-query": {"version": "5.3.2", "dev": true, "license": "Apache-2.0", "engines": {"node": ">= 0.4"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes": {"version": "3.1.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.4", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.findlast": {"version": "1.2.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.findlastindex": {"version": "1.2.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-shim-unscopables": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flat": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.tosorted": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3", "es-errors": "^1.3.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/ast-types-flow": {"version": "0.0.8", "dev": true, "license": "MIT"}, "node_modules/async-function": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/axe-core": {"version": "4.10.3", "dev": true, "license": "MPL-2.0", "engines": {"node": ">=4"}}, "node_modules/axios": {"version": "1.9.0", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/axobject-query": {"version": "4.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">= 0.4"}}, "node_modules/babel-plugin-macros": {"version": "3.1.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "engines": {"node": ">=10", "npm": ">=6"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/base64-arraybuffer": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/blurhash": {"version": "2.0.5", "license": "MIT"}, "node_modules/body-parser": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/bootstrap": {"version": "5.3.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "license": "MIT", "peerDependencies": {"@popperjs/core": "^2.11.8"}}, "node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "devOptional": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/btoa": {"version": "1.2.1", "license": "(MIT OR Apache-2.0)", "bin": {"btoa": "bin/btoa.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/busboy": {"version": "1.6.0", "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/bytes": {"version": "3.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001717", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/canvg": {"version": "3.0.11", "license": "MIT", "optional": true, "dependencies": {"@babel/runtime": "^7.12.5", "@types/raf": "^3.4.0", "core-js": "^3.8.3", "raf": "^3.4.1", "regenerator-runtime": "^0.13.7", "rgbcolor": "^1.0.1", "stackblur-canvas": "^2.0.0", "svg-pathdata": "^6.0.3"}, "engines": {"node": ">=10.0.0"}}, "node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/charenc": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz", "integrity": "sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/chart.js": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/chart.js/-/chart.js-4.5.0.tgz", "integrity": "sha512-aYeC/jDgSEx8SHWZvANYMioYMZ2KX02W6f6uVfyteuCGcadDLcYVHdfdygsTQkQ4TKn5lghoojAsPj5pu0SnvQ==", "license": "MIT", "dependencies": {"@kurkle/color": "^0.3.0"}, "engines": {"pnpm": ">=8"}}, "node_modules/chokidar": {"version": "4.0.3", "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/ckeditor5": {"version": "45.2.0", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-adapter-ckfinder": "45.1.0", "@ckeditor/ckeditor5-alignment": "45.1.0", "@ckeditor/ckeditor5-autoformat": "45.1.0", "@ckeditor/ckeditor5-autosave": "45.1.0", "@ckeditor/ckeditor5-basic-styles": "45.1.0", "@ckeditor/ckeditor5-block-quote": "45.1.0", "@ckeditor/ckeditor5-bookmark": "45.1.0", "@ckeditor/ckeditor5-ckbox": "45.1.0", "@ckeditor/ckeditor5-ckfinder": "45.1.0", "@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-cloud-services": "45.1.0", "@ckeditor/ckeditor5-code-block": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-easy-image": "45.1.0", "@ckeditor/ckeditor5-editor-balloon": "45.1.0", "@ckeditor/ckeditor5-editor-classic": "45.1.0", "@ckeditor/ckeditor5-editor-decoupled": "45.1.0", "@ckeditor/ckeditor5-editor-inline": "45.1.0", "@ckeditor/ckeditor5-editor-multi-root": "45.1.0", "@ckeditor/ckeditor5-emoji": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-enter": "45.1.0", "@ckeditor/ckeditor5-essentials": "45.1.0", "@ckeditor/ckeditor5-find-and-replace": "45.1.0", "@ckeditor/ckeditor5-font": "45.1.0", "@ckeditor/ckeditor5-fullscreen": "45.1.0", "@ckeditor/ckeditor5-heading": "45.1.0", "@ckeditor/ckeditor5-highlight": "45.1.0", "@ckeditor/ckeditor5-horizontal-line": "45.1.0", "@ckeditor/ckeditor5-html-embed": "45.1.0", "@ckeditor/ckeditor5-html-support": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-image": "45.1.0", "@ckeditor/ckeditor5-indent": "45.1.0", "@ckeditor/ckeditor5-language": "45.1.0", "@ckeditor/ckeditor5-link": "45.1.0", "@ckeditor/ckeditor5-list": "45.1.0", "@ckeditor/ckeditor5-markdown-gfm": "45.1.0", "@ckeditor/ckeditor5-media-embed": "45.1.0", "@ckeditor/ckeditor5-mention": "45.1.0", "@ckeditor/ckeditor5-minimap": "45.1.0", "@ckeditor/ckeditor5-page-break": "45.1.0", "@ckeditor/ckeditor5-paragraph": "45.1.0", "@ckeditor/ckeditor5-paste-from-office": "45.1.0", "@ckeditor/ckeditor5-remove-format": "45.1.0", "@ckeditor/ckeditor5-restricted-editing": "45.1.0", "@ckeditor/ckeditor5-select-all": "45.1.0", "@ckeditor/ckeditor5-show-blocks": "45.1.0", "@ckeditor/ckeditor5-source-editing": "45.1.0", "@ckeditor/ckeditor5-special-characters": "45.1.0", "@ckeditor/ckeditor5-style": "45.1.0", "@ckeditor/ckeditor5-table": "45.1.0", "@ckeditor/ckeditor5-theme-lark": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-undo": "45.1.0", "@ckeditor/ckeditor5-upload": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-watchdog": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "@ckeditor/ckeditor5-word-count": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-adapter-ckfinder": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-adapter-ckfinder/-/ckeditor5-adapter-ckfinder-45.1.0.tgz", "integrity": "sha512-otNU8VFFxKxRJH3t7u1C74BEq615EamHN7B/W+grpVszLqYoEvAZ3A2t1Q+OSg8+SIiTgpYsp8s5QqjWYB/72A==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-upload": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-alignment": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-alignment/-/ckeditor5-alignment-45.1.0.tgz", "integrity": "sha512-wRmR7ljy2t8w9Kv7WTHkQZnp6knK63C9LncwMARkmaTiUBU9QLrH0+M4DXRyv6uNdkGZ4WGtLurrtTBnIQcVUw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-autoformat": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-autoformat/-/ckeditor5-autoformat-45.1.0.tgz", "integrity": "sha512-jdV2ot42GVFcoL2HOqUdxPCj/Cz8SjDBl6N/PAkwaHr7AjPOr78c/4WeGNA8a1rXxy+a1GcEKfQff1wq8xHOfg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-heading": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-autosave": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-autosave/-/ckeditor5-autosave-45.1.0.tgz", "integrity": "sha512-FsMLxlDxxFxBDFFboCxqIPC+xFsOknqB9PpIE7il2sAfrNWZAtpdtQ73v8YqJwRlLwbPcY69ZaZapznl590lBg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-basic-styles": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-basic-styles/-/ckeditor5-basic-styles-45.1.0.tgz", "integrity": "sha512-aykdivtT5NgiQdgTqKlhbmffZuMG6vxEE+/XsmKfyi5mJ6oh9lv50zb0bze7sQvBhynt5CC8tKUI4O4VC3a5dw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-block-quote": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-block-quote/-/ckeditor5-block-quote-45.1.0.tgz", "integrity": "sha512-zsZyuh6w1H/TV2QvJnZ6mScvYMNn6itnFbbTHJWAJoLQTs2XjiUaVy7m+pK81x0VHrH1HPsj/ErccscrHOQTdQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-enter": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-bookmark": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-bookmark/-/ckeditor5-bookmark-45.1.0.tgz", "integrity": "sha512-UtdpprqMuoEstxH0JZIGzyRFqo2gpiiLP/4HozV3ygV2buonigbJgx0fDJvHqTmkcSzXIG7+FUACcXnyVvsDXA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-link": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-ckbox": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-ckbox/-/ckeditor5-ckbox-45.1.0.tgz", "integrity": "sha512-MjZl4R2AXTX+wKGIVAyifzNOJAFUfmdnaQ0j6f25Ezka2V83iY2Zxjt1fuu52OJWAcuiL4WEupZ0F75BN3Lm9w==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-cloud-services": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-image": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-upload": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "blurhash": "2.0.5", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-ckfinder": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-ckfinder/-/ckeditor5-ckfinder-45.1.0.tgz", "integrity": "sha512-iOcz/z8W+sd0Kf4L7R6U16CC2GYDeujrQbtlGEg9gF1REJAeTZBxsK9LAcLSOHF7KjaJSykH1GJpzo2Kzp2lKg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-image": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-clipboard": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-clipboard/-/ckeditor5-clipboard-45.1.0.tgz", "integrity": "sha512-+78bGL0OQPs3n1OTSssnfEvYLSuCNLkGL2c/7IdDtBi3EWVt5CAr8edb6DMSWDfnlCHCsSm6pUQF8GeTPhQLHg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-cloud-services": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-cloud-services/-/ckeditor5-cloud-services-45.1.0.tgz", "integrity": "sha512-dyJ+TaaZQwO33GF6bmMpPRnRttwmrWZH9GCHhVH1LiPPyDr0GnU0YPbwzdhStW1Q9PVvqtgQJAyosvQ4y8qiqQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-code-block": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-code-block/-/ckeditor5-code-block-45.1.0.tgz", "integrity": "sha512-M+WJRmQvbYrj6Vm7p2he0pj4vgAFTqAoWB9aNRDR4JPTOM2ZTYhpk7vVBZpYNbbmaSP+opJIjnw86MEcgFtRFg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-enter": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-core": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-core/-/ckeditor5-core-45.1.0.tgz", "integrity": "sha512-rpJIkorFOMpHwhDjaRPpqI3NB4O44Vab/HH6KAnTTc3ZUoUzUL7+NlBweb5AHzOcmluXneQQrGQXB7J9bJqrBQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-watchdog": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-easy-image": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-easy-image/-/ckeditor5-easy-image-45.1.0.tgz", "integrity": "sha512-Ny<PERSON>0ZilpFQKFGa6KFYlQpIrZpZvKnunuz/moTCOwh3E4yb84Wug6Nk+UWyvHXX7/olU9sgZevWFS9vLKb/yLfA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-cloud-services": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-upload": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-editor-balloon": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-balloon/-/ckeditor5-editor-balloon-45.1.0.tgz", "integrity": "sha512-DNHGX2W16kmT/Y7YG4nSSqBOrEhArC975dlYqGTSBc6HUIB6PTZPbzu5qdFUJ+vziXbtw4YdnqtZCuQHswXypg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-editor-classic": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-classic/-/ckeditor5-editor-classic-45.1.0.tgz", "integrity": "sha512-ZIa6PPMPl4dawjcG4mTZy8SnHaS4kNOybPJDEIziWh+LLlTDnHlwXRZqjOqdLyWR2b9yrrZKnOrVdWIaOGs29Q==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-editor-decoupled": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-decoupled/-/ckeditor5-editor-decoupled-45.1.0.tgz", "integrity": "sha512-wAfN1k+wVSaOMcDdTTdViHWqTzXulB6MEMNFceO7KjwmTqJwHGnVjMQRH5Z27oKnJltBvs+kW9Bl/AH2UcuBLg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-editor-inline": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-inline/-/ckeditor5-editor-inline-45.1.0.tgz", "integrity": "sha512-KQvZ0ObX7Y5AJvfkagaQ7S0eVCF2zHENjENaYraAh5OoaeNTWReh/w57UiijH9dTfE6JEzqDjaXYepyIhmNOYg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-editor-multi-root": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-multi-root/-/ckeditor5-editor-multi-root-45.1.0.tgz", "integrity": "sha512-1arT2NWuzQduDz4OpTr1cc4iS4S036No9YfaJFoUmCMtpZMQck0MyD6dnjXOUoALi9qpVhMtxPsUU8lvEP1Ynw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-emoji": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-emoji/-/ckeditor5-emoji-45.1.0.tgz", "integrity": "sha512-KtUpD/1XBt7oi0NdkQrZxCd/cvc+XFFcZVZfwpz+3O7ypBcJORJpTn+9x/7Vxo/6iyFoI4XBfMVR/4jTFYgzIg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-mention": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0", "fuzzysort": "3.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-engine": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-engine/-/ckeditor5-engine-45.1.0.tgz", "integrity": "sha512-3O1fsxi1wGQBz60vtKgSFNPSzU+R2jxUXDPDPhm88oCpqU5l5Giz+bcM7AVQg6b8wc8caVu5dVeR4/U9JsqGmQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-utils": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-enter": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-enter/-/ckeditor5-enter-45.1.0.tgz", "integrity": "sha512-TWuTFnSwFdnw7FPQBZL8rMEL8YCZ+9FXyNZua2tQ07trMoLHMyP7iiiCNhReDX3DIdnGMAQVMLnTMO3Xs6puhA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-essentials": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-essentials/-/ckeditor5-essentials-45.1.0.tgz", "integrity": "sha512-ktQUTvbGHZdTcv/9Pt+LYi1ur6nuypf3WhosGkoqYCh6MOBlLwrtpsEY7hJpPwfZxFl548gCQXSMy4V2ePoVyQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-enter": "45.1.0", "@ckeditor/ckeditor5-select-all": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-undo": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-find-and-replace": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-find-and-replace/-/ckeditor5-find-and-replace-45.1.0.tgz", "integrity": "sha512-0RwInpUvjFll1Trl0B0KCtoWmWQLrClnTu1Eol1SPnY2C/oYroB8I7UPHChhwvSNaw1l8FYuXx4uyaQoSXSQMQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-font": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-font/-/ckeditor5-font-45.1.0.tgz", "integrity": "sha512-7DqUhpoRYRajDWYFa2hIBypjNJ6Y6XwwiQO1UFZc0TFzbF0pqjRAqrE1Tq8Y5L9IGFUKW/H2pSG/OLcuxdBKoA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-fullscreen": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-fullscreen/-/ckeditor5-fullscreen-45.1.0.tgz", "integrity": "sha512-K+ON+IHuGU0tHOyevwGu71l094NPcLVqmal7fQ8gzydztyR/7SiaBBgi4tKO3gz7T/PCCwszV6dNS2GLFOSVPg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-editor-classic": "45.1.0", "@ckeditor/ckeditor5-editor-decoupled": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-heading": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-heading/-/ckeditor5-heading-45.1.0.tgz", "integrity": "sha512-<PERSON>5<PERSON>B4cMGLIVzyPmDEABuJu4jK93qVdTTzM80w68xPYPsLHJs9qcdix1XbZm52Bd7oEXLMFI7XRw8MxNoYNxgw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-paragraph": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-highlight": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-highlight/-/ckeditor5-highlight-45.1.0.tgz", "integrity": "sha512-zu7SOeKHEiU8XxRvwHC2eliYADERxsLppx7al9MhBUqfBhKGK5clOmvG7gjn88pGazmAs4iAABKy6pV0AJZSpg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-horizontal-line": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-horizontal-line/-/ckeditor5-horizontal-line-45.1.0.tgz", "integrity": "sha512-R2BUcsm46LzV/UtLoXPKBOKQfEkvrfFSsyNR699yK0XdiRBAd54t/PX62vckZ6irU77hk7NhM7R3qnxd6hqV7Q==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-html-embed": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-html-embed/-/ckeditor5-html-embed-45.1.0.tgz", "integrity": "sha512-LIC7FoQcusjp5tJ4PvYp3XwuPIOoEPevJIzXb8OBC1+8FwE9Iqp4Qg+3JIOS+wpmQymq/IpNAXJ4jzZ1QFUoSQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-html-support": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-html-support/-/ckeditor5-html-support-45.1.0.tgz", "integrity": "sha512-U1e9XiHHvcilUJ1bN1Wut/54ZLVxH2+LDhuokeBo7/erY6Q2HEIKSL/92bQykr8mnVSuZoKa7mFXHLMg5tLlQQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-enter": "45.1.0", "@ckeditor/ckeditor5-heading": "45.1.0", "@ckeditor/ckeditor5-image": "45.1.0", "@ckeditor/ckeditor5-list": "45.1.0", "@ckeditor/ckeditor5-table": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-icons": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-icons/-/ckeditor5-icons-45.1.0.tgz", "integrity": "sha512-DeRpYnTn+Eng7HBvvn/y46C/du48dVGs7NHfY8kXLOCMY0G30fb1KrdqaYy92Rdsd/vZKCkPcXHw7PchRPbepQ==", "license": "SEE LICENSE IN LICENSE.md"}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-image": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-image/-/ckeditor5-image-45.1.0.tgz", "integrity": "sha512-FOazPw6gCtWIEJQFnkcfuWZk2Z12q49ZSlegEvPsF8uM6ltayb6ywk5B1EGarmMQmuk7fDaCliHJ54P53LklKA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-undo": "45.1.0", "@ckeditor/ckeditor5-upload": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-indent": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-indent/-/ckeditor5-indent-45.1.0.tgz", "integrity": "sha512-Y3nBgYYIuQfcNvAKb9bo1UtEInynp+VG+DriGIvPClU66EcMwJrYg4meA0mzyMFqEARu3G0mn9Ii1KIW1Ix7Cg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-heading": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-list": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-language": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-language/-/ckeditor5-language-45.1.0.tgz", "integrity": "sha512-97gh<PERSON>bmyHr0Lwd4E8lagKngnfdZEpBG0tJ5C5E1OXhu7uhMX201C7/JX4Mw+ZtyZ0fTxdTDiRiypDIoHlbowA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-link": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-link/-/ckeditor5-link-45.1.0.tgz", "integrity": "sha512-/JEHiklcG8mYEveyzhVAKfNSIqk64dqp8v9OkwB6VPFBMN3q7jemRi0gExBc6+HznDjBlM+0nmRB810sQnBuBQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-image": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-list": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-list/-/ckeditor5-list-45.1.0.tgz", "integrity": "sha512-T4kcaaqAuq0acYnw77G9fFKHDMZSB/X6lqNx0Uh68D/LoXyg44nlP3Bhx2HSeLsBUkrIR1WOTYuya59wF4tkGQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-enter": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-markdown-gfm": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-markdown-gfm/-/ckeditor5-markdown-gfm-45.1.0.tgz", "integrity": "sha512-stJpJC1KYiwYcJwzUmj5eg+g4GPz/rg/guiyN30jXDv0fRoUevsebhjCt+21Iwk9p1fvdcxm4At79k70MSVqtw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@types/marked": "4.3.2", "@types/turndown": "5.0.5", "ckeditor5": "45.1.0", "marked": "4.0.12", "turndown": "7.2.0", "turndown-plugin-gfm": "1.0.2"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-media-embed": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-media-embed/-/ckeditor5-media-embed-45.1.0.tgz", "integrity": "sha512-5mQEVb6P1AOH64CucfyAAE7NRf65VkVQUDfPxfbtXXAnwfVCW5PCoE3ksW3TV7QbZGlL6zlZEfP4wnJjpKN3Zw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-undo": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-mention": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-mention/-/ckeditor5-mention-45.1.0.tgz", "integrity": "sha512-gbcuEqeI+AwQJ4TdCFKPQNWgWdr21Ht/b5KE0n/AvQHHW+KHzTTRQl+gKrt6CGCkC3MpllEokG7xhL7hHyyrpw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-minimap": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-minimap/-/ckeditor5-minimap-45.1.0.tgz", "integrity": "sha512-mBkfs65+3tJUsJQjduW2zPuIsg4Q72Y2JSPr0Gw1+XYaEekZfCbhj1x6LvWQFy1waZQk7TvlBxGNTCQjo4DFBg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-page-break": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-page-break/-/ckeditor5-page-break-45.1.0.tgz", "integrity": "sha512-O2hNGkvz88Kpy9vlhByd/7p81N5w1htuLTqS+fojpoFV13rwbwyX0g3EOVJwk7uSEk0L1iomNo1zDu87sNkKIw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-paragraph": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-paragraph/-/ckeditor5-paragraph-45.1.0.tgz", "integrity": "sha512-FST+msgRQ8M9pEiv9Whhn2v0H1hhwlfccf3BKxZ6BI7o2V1HyOZ5l9NxCKhD4ujN5mw9whAY5TrTApwyXEZqXg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-paste-from-office": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-paste-from-office/-/ckeditor5-paste-from-office-45.1.0.tgz", "integrity": "sha512-AkvINYiuEF+D0DuJT7+tMwYCUmWWys8hwksvotihi6rU574UKf4pBrwYip+1BQuQ9FSb142/LUfixRRU39cKZw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-remove-format": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-remove-format/-/ckeditor5-remove-format-45.1.0.tgz", "integrity": "sha512-+6ml8//kp34HthxS5DA9XGJyJ9dw0Yxay6aqpbDYGIyClJTdwlUPjVuOAnRZfKhY/BW5ZZJrQleNww97YnUlfQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-restricted-editing": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-restricted-editing/-/ckeditor5-restricted-editing-45.1.0.tgz", "integrity": "sha512-qsYa9PpFnRXsBp6LbcsHbADGaVLQP91qGwFLiXNeaR2I0eYbJywRf+wbfgILGvhsQgZNLKld0AIM6h/yBqOTRA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-select-all": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-select-all/-/ckeditor5-select-all-45.1.0.tgz", "integrity": "sha512-Z/DSArfy7MUfHVaYMp4lJZos6XTiTWuyOzQEwp9BzcpiYyXHSRs+cH+JZYd7x5/JuddF9OsEZIqnS+xiw+PqfQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-show-blocks": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-show-blocks/-/ckeditor5-show-blocks-45.1.0.tgz", "integrity": "sha512-61pmHQ82g3qS2ucmxJihFMwKashfJI6xcIHjqu09ctJlwHazq95T76uWH3s0fd5sBBx6cFZ5Dr4Y9Uz+hUc1pQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-source-editing": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-source-editing/-/ckeditor5-source-editing-45.1.0.tgz", "integrity": "sha512-CDZbYt7903FjHOv0EnKVjmIw6g/Lu6gRMBv6HyDKzG+m3r+1blsCPOWlas7CPPeGsifxGE2+fS67DBuHlUelsg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-theme-lark": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-special-characters": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-special-characters/-/ckeditor5-special-characters-45.1.0.tgz", "integrity": "sha512-mf0crAk0x28c39rY/CatI7UMKv8HLFH4CTc/YvjtTBINqHvUoLuOYvJ9WnJh2SVxFS2YkYnxpfyppHaRp2/z0w==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-style": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-style/-/ckeditor5-style-45.1.0.tgz", "integrity": "sha512-MtBgioOzgQJWWoA952o8Y/51LwDaH8KkjYz78qck8fHTCR+aPnUJLs1z53aXq6AKFUFHMpu1QoR+66MIOXpmrg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-html-support": "45.1.0", "@ckeditor/ckeditor5-list": "45.1.0", "@ckeditor/ckeditor5-table": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-table": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-table/-/ckeditor5-table-45.1.0.tgz", "integrity": "sha512-M3FVHFKSBDhW4NVHRTZkbF8LCBIGX2nnX2B13dpLJArNvVcNfHsPsjM+y6O+lKX/bKgJw7bUTbxSvasoPbyvFQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-theme-lark": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-theme-lark/-/ckeditor5-theme-lark-45.1.0.tgz", "integrity": "sha512-QaoVJ75yfi36uCju+rtUcxzkf0UwqHkZutswhiQ5U0CAp+uC5kKjPUSREFmH7yHY560uTR9Qx40//MB9+URbwQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-ui": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-typing": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-typing/-/ckeditor5-typing-45.1.0.tgz", "integrity": "sha512-lItJnsr0cnuM8aFTHHPbiNqh17Z0HDtdiIk9zpv0+t6xIP2kx92vk+ymkhNe44rM2atla6ExqTDkfvm5XOL54g==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-ui": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-ui/-/ckeditor5-ui-45.1.0.tgz", "integrity": "sha512-4GynwAhsBxy/dTmVFG3gD4V25qI5rtN7gPF4UjABKIrMPTfF/1+lWXiS/KLUwQ1ftihR6TrwEOQ/AAQk/qEpRQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-editor-multi-root": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@types/color-convert": "2.0.4", "color-convert": "2.0.1", "color-parse": "1.4.2", "es-toolkit": "1.32.0", "vanilla-colorful": "0.7.2"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-undo": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-undo/-/ckeditor5-undo-45.1.0.tgz", "integrity": "sha512-hahSIH+CWqetZaI1cUssvlDuwuSzXprBSNOPlULoyaBHej7otHzsdqWIyJoip+kOOb2gfPUioriIpKVaVd7itg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-upload": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-upload/-/ckeditor5-upload-45.1.0.tgz", "integrity": "sha512-D/A4U4/MTi5N3XAol00OTDzYc6hVjU25ShajvMkh2EVWhS6tdA+himQy2J2JI6KzORU3oCA1eMvuJ83gFFq3xg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-utils": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-utils/-/ckeditor5-utils-45.1.0.tgz", "integrity": "sha512-YxGWzvlT0Is6L34Ejyl/TLASS9CCM8jDZjkqT2k4Zu9+xmkOsrPGKbL1az60NVD7rSGdY/DBDagxQNLzxMjUiw==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-ui": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-watchdog": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-watchdog/-/ckeditor5-watchdog-45.1.0.tgz", "integrity": "sha512-+qnHn30D9QDbtp1VdzO3gpnUBQKLxQqTS+2O5k9DxwLHErDcQGTudIpwZhdwESzjBjzB8De1lS1Ftzvn7Q+nyQ==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-editor-multi-root": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-widget": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-widget/-/ckeditor5-widget-45.1.0.tgz", "integrity": "sha512-MzAw89pG29QOTBJchtqqr327RdNK28R4b+eBMB2LwAGz9c6wVZjyepn8ZqiKtACvNZ2aC2xgdJOGvpZ4VJiqTA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-enter": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/@ckeditor/ckeditor5-word-count": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/@ckeditor/ckeditor5-word-count/-/ckeditor5-word-count-45.1.0.tgz", "integrity": "sha512-CW7yktPoxakqOAniY+xDY5MAvdpIMvULj6XGWHskgB8VdumcaWF74Nam/mE5Hs2WuKMNBgTnFnsdGoRSGlpJzg==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "ckeditor5": "45.1.0", "es-toolkit": "1.32.0"}}, "node_modules/ckeditor5/node_modules/ckeditor5": {"version": "45.1.0", "resolved": "https://registry.npmjs.org/ckeditor5/-/ckeditor5-45.1.0.tgz", "integrity": "sha512-GvywHMp0uv5sEwkEaB/hqtK1UXDmr5njsLJ4k8ajz4Hx0su/hKuMl+67FnCqMhOxf4OhY/7FRQiiazDTufQ0vA==", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@ckeditor/ckeditor5-adapter-ckfinder": "45.1.0", "@ckeditor/ckeditor5-alignment": "45.1.0", "@ckeditor/ckeditor5-autoformat": "45.1.0", "@ckeditor/ckeditor5-autosave": "45.1.0", "@ckeditor/ckeditor5-basic-styles": "45.1.0", "@ckeditor/ckeditor5-block-quote": "45.1.0", "@ckeditor/ckeditor5-bookmark": "45.1.0", "@ckeditor/ckeditor5-ckbox": "45.1.0", "@ckeditor/ckeditor5-ckfinder": "45.1.0", "@ckeditor/ckeditor5-clipboard": "45.1.0", "@ckeditor/ckeditor5-cloud-services": "45.1.0", "@ckeditor/ckeditor5-code-block": "45.1.0", "@ckeditor/ckeditor5-core": "45.1.0", "@ckeditor/ckeditor5-easy-image": "45.1.0", "@ckeditor/ckeditor5-editor-balloon": "45.1.0", "@ckeditor/ckeditor5-editor-classic": "45.1.0", "@ckeditor/ckeditor5-editor-decoupled": "45.1.0", "@ckeditor/ckeditor5-editor-inline": "45.1.0", "@ckeditor/ckeditor5-editor-multi-root": "45.1.0", "@ckeditor/ckeditor5-emoji": "45.1.0", "@ckeditor/ckeditor5-engine": "45.1.0", "@ckeditor/ckeditor5-enter": "45.1.0", "@ckeditor/ckeditor5-essentials": "45.1.0", "@ckeditor/ckeditor5-find-and-replace": "45.1.0", "@ckeditor/ckeditor5-font": "45.1.0", "@ckeditor/ckeditor5-fullscreen": "45.1.0", "@ckeditor/ckeditor5-heading": "45.1.0", "@ckeditor/ckeditor5-highlight": "45.1.0", "@ckeditor/ckeditor5-horizontal-line": "45.1.0", "@ckeditor/ckeditor5-html-embed": "45.1.0", "@ckeditor/ckeditor5-html-support": "45.1.0", "@ckeditor/ckeditor5-icons": "45.1.0", "@ckeditor/ckeditor5-image": "45.1.0", "@ckeditor/ckeditor5-indent": "45.1.0", "@ckeditor/ckeditor5-language": "45.1.0", "@ckeditor/ckeditor5-link": "45.1.0", "@ckeditor/ckeditor5-list": "45.1.0", "@ckeditor/ckeditor5-markdown-gfm": "45.1.0", "@ckeditor/ckeditor5-media-embed": "45.1.0", "@ckeditor/ckeditor5-mention": "45.1.0", "@ckeditor/ckeditor5-minimap": "45.1.0", "@ckeditor/ckeditor5-page-break": "45.1.0", "@ckeditor/ckeditor5-paragraph": "45.1.0", "@ckeditor/ckeditor5-paste-from-office": "45.1.0", "@ckeditor/ckeditor5-remove-format": "45.1.0", "@ckeditor/ckeditor5-restricted-editing": "45.1.0", "@ckeditor/ckeditor5-select-all": "45.1.0", "@ckeditor/ckeditor5-show-blocks": "45.1.0", "@ckeditor/ckeditor5-source-editing": "45.1.0", "@ckeditor/ckeditor5-special-characters": "45.1.0", "@ckeditor/ckeditor5-style": "45.1.0", "@ckeditor/ckeditor5-table": "45.1.0", "@ckeditor/ckeditor5-theme-lark": "45.1.0", "@ckeditor/ckeditor5-typing": "45.1.0", "@ckeditor/ckeditor5-ui": "45.1.0", "@ckeditor/ckeditor5-undo": "45.1.0", "@ckeditor/ckeditor5-upload": "45.1.0", "@ckeditor/ckeditor5-utils": "45.1.0", "@ckeditor/ckeditor5-watchdog": "45.1.0", "@ckeditor/ckeditor5-widget": "45.1.0", "@ckeditor/ckeditor5-word-count": "45.1.0"}}, "node_modules/classnames": {"version": "2.5.1", "license": "MIT"}, "node_modules/cli-cursor": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"slice-ansi": "^5.0.0", "string-width": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/client-only": {"version": "0.0.1", "license": "MIT"}, "node_modules/clsx": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/color": {"version": "4.2.3", "license": "MIT", "optional": true, "dependencies": {"color-convert": "^2.0.1", "color-string": "^1.9.0"}, "engines": {"node": ">=12.5.0"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/color-parse": {"version": "1.4.2", "license": "MIT", "dependencies": {"color-name": "^1.0.0"}}, "node_modules/color-string": {"version": "1.9.1", "license": "MIT", "optional": true, "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/colorette": {"version": "2.0.20", "dev": true, "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "13.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/content-disposition": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.9.0", "license": "MIT"}, "node_modules/cookie": {"version": "0.7.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.2.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/core-js": {"version": "3.43.0", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-js-pure": {"version": "3.44.0", "resolved": "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.44.0.tgz", "integrity": "sha512-gvMQAGB4dfVUxpYD0k3Fq8J+n5bB6Ytl15lqlZrOIXFzxOhtPaObfkQGHtMRdyjIf7z2IeNULwi1jEwyS+ltKQ==", "hasInstallScript": true, "license": "MIT", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/cors": {"version": "2.8.5", "dev": true, "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cosmiconfig": {"version": "7.1.0", "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/cross-spawn": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypt": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz", "integrity": "sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/crypto-js": {"version": "4.2.0", "license": "MIT"}, "node_modules/css-line-break": {"version": "2.1.0", "license": "MIT", "dependencies": {"utrie": "^1.0.2"}}, "node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "node_modules/damerau-levenshtein": {"version": "1.0.8", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/data-view-buffer": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "node_modules/data-view-byte-offset": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/date-fns": {"version": "4.1.0", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}}, "node_modules/dayjs": {"version": "1.11.13", "license": "MIT"}, "node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.5.0", "license": "MIT"}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/define-data-property": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/detect-element-overflow": {"version": "1.4.2", "license": "MIT", "funding": {"url": "https://github.com/wojtekmaj/detect-element-overflow?sponsor=1"}}, "node_modules/detect-libc": {"version": "2.0.4", "license": "Apache-2.0", "optional": true, "engines": {"node": ">=8"}}, "node_modules/doctrine": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/dom-helpers": {"version": "5.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/dompurify": {"version": "2.5.8", "license": "(MPL-2.0 OR Apache-2.0)", "optional": true}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/engine.io-client": {"version": "6.6.3", "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1", "engine.io-parser": "~5.2.1", "ws": "~8.17.1", "xmlhttprequest-ssl": "~2.1.1"}}, "node_modules/engine.io-client/node_modules/debug": {"version": "4.3.7", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/engine.io-parser": {"version": "5.2.3", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/environment": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/error-ex": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/error-ex/node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/es-abstract": {"version": "1.23.9", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.3", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.0", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-regex": "^1.2.1", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.0", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.3", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.3", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.18"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-iterator-helpers": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-set-tostringtag": "^2.0.3", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.6", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "iterator.prototype": "^1.1.4", "safe-array-concat": "^1.1.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-shim-unscopables": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-toolkit": {"version": "1.32.0", "license": "MIT", "workspaces": ["docs", "benchmarks"]}, "node_modules/es6-promise": {"version": "4.2.8", "license": "MIT"}, "node_modules/escape-html": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.26.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.13.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.26.0", "@eslint/plugin-kit": "^0.2.8", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@modelcontextprotocol/sdk": "^1.8.0", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "zod": "^3.24.2"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-config-next": {"version": "15.2.0", "dev": true, "license": "MIT", "dependencies": {"@next/eslint-plugin-next": "15.2.0", "@rushstack/eslint-patch": "^1.10.3", "@typescript-eslint/eslint-plugin": "^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0", "@typescript-eslint/parser": "^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0", "eslint-import-resolver-node": "^0.3.6", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.0.0"}, "peerDependencies": {"eslint": "^7.23.0 || ^8.0.0 || ^9.0.0", "typescript": ">=3.3.1"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/eslint-config-prettier": {"version": "10.1.5", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "funding": {"url": "https://opencollective.com/eslint-config-prettier"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.9", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-import-resolver-typescript": {"version": "3.10.1", "dev": true, "license": "ISC", "dependencies": {"@nolyfill/is-core-module": "1.0.39", "debug": "^4.4.0", "get-tsconfig": "^4.10.0", "is-bun-module": "^2.0.0", "stable-hash": "^0.0.5", "tinyglobby": "^0.2.13", "unrs-resolver": "^1.6.2"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint-import-resolver-typescript"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}}, "node_modules/eslint-module-utils": {"version": "2.12.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-import": {"version": "2.31.0", "dev": true, "license": "MIT", "dependencies": {"@rtsao/scc": "^1.1.0", "array-includes": "^3.1.8", "array.prototype.findlastindex": "^1.2.5", "array.prototype.flat": "^1.3.2", "array.prototype.flatmap": "^1.3.2", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.0", "hasown": "^2.0.2", "is-core-module": "^2.15.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "object.groupby": "^1.0.3", "object.values": "^1.2.0", "semver": "^6.3.1", "string.prototype.trimend": "^1.0.8", "tsconfig-paths": "^3.15.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9"}}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-import/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eslint-plugin-jsx-a11y": {"version": "6.10.2", "dev": true, "license": "MIT", "dependencies": {"aria-query": "^5.3.2", "array-includes": "^3.1.8", "array.prototype.flatmap": "^1.3.2", "ast-types-flow": "^0.0.8", "axe-core": "^4.10.0", "axobject-query": "^4.1.0", "damerau-levenshtein": "^1.0.8", "emoji-regex": "^9.2.2", "hasown": "^2.0.2", "jsx-ast-utils": "^3.3.5", "language-tags": "^1.0.9", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "safe-regex-test": "^1.0.3", "string.prototype.includes": "^2.0.1"}, "engines": {"node": ">=4.0"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9"}}, "node_modules/eslint-plugin-prettier": {"version": "5.4.0", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint-plugin-prettier"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-plugin-react": {"version": "7.37.5", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.8", "array.prototype.findlast": "^1.2.5", "array.prototype.flatmap": "^1.3.3", "array.prototype.tosorted": "^1.1.4", "doctrine": "^2.1.0", "es-iterator-helpers": "^1.2.1", "estraverse": "^5.3.0", "hasown": "^2.0.2", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.9", "object.fromentries": "^2.0.8", "object.values": "^1.2.1", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.5", "semver": "^6.3.1", "string.prototype.matchall": "^4.0.12", "string.prototype.repeat": "^1.0.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7"}}, "node_modules/eslint-plugin-react-hooks": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0"}}, "node_modules/eslint-plugin-react/node_modules/resolve": {"version": "2.0.0-next.5", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/eslint-plugin-react/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eslint-scope": {"version": "8.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "4.2.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree": {"version": "10.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.14.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "5.0.1", "dev": true, "license": "MIT"}, "node_modules/eventsource": {"version": "3.0.7", "dev": true, "license": "MIT", "dependencies": {"eventsource-parser": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/eventsource-parser": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=18.0.0"}}, "node_modules/express": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express-rate-limit": {"version": "7.5.0", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/express-rate-limit"}, "peerDependencies": {"express": "^4.11 || 5 || ^5.0.0-beta.1"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-glob": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fetch-blob": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/fflate": {"version": "0.8.2", "license": "MIT"}, "node_modules/file-entry-cache": {"version": "8.0.0", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/file-saver": {"version": "2.0.5", "license": "MIT"}, "node_modules/fill-range": {"version": "7.1.1", "devOptional": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-root": {"version": "1.1.0", "license": "MIT"}, "node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatted": {"version": "3.3.3", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.5", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/form-data": {"version": "4.0.2", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/form-data/node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/form-data/node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/formdata-polyfill": {"version": "4.0.10", "resolved": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "license": "MIT", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/forwarded": {"version": "0.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/fullcalendar": {"version": "6.1.17", "license": "MIT", "dependencies": {"@fullcalendar/core": "~6.1.17", "@fullcalendar/daygrid": "~6.1.17", "@fullcalendar/interaction": "~6.1.17", "@fullcalendar/list": "~6.1.17", "@fullcalendar/multimonth": "~6.1.17", "@fullcalendar/timegrid": "~6.1.17"}}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/fuzzysort": {"version": "3.1.0", "license": "MIT"}, "node_modules/get-east-asian-width": {"version": "1.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-symbol-description": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-tsconfig": {"version": "4.10.0", "dev": true, "license": "MIT", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "funding": {"url": "https://github.com/privatenumber/get-tsconfig?sponsor=1"}}, "node_modules/get-user-locale": {"version": "2.3.2", "license": "MIT", "dependencies": {"mem": "^8.0.0"}, "funding": {"url": "https://github.com/wojtekmaj/get-user-locale?sponsor=1"}}, "node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/globals": {"version": "14.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalthis": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/goober": {"version": "2.1.16", "license": "MIT", "peerDependencies": {"csstype": "^3.0.10"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/has-bigints": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/html2canvas": {"version": "1.4.1", "license": "MIT", "dependencies": {"css-line-break": "^2.1.0", "text-segmentation": "^1.0.3"}, "engines": {"node": ">=8.0.0"}}, "node_modules/html2pdf.js": {"version": "0.10.3", "license": "MIT", "dependencies": {"es6-promise": "^4.2.5", "html2canvas": "^1.0.0", "jspdf": "^3.0.0"}}, "node_modules/html2pdf.js/node_modules/dompurify": {"version": "3.2.6", "license": "(MPL-2.0 OR Apache-2.0)", "optional": true, "optionalDependencies": {"@types/trusted-types": "^2.0.7"}}, "node_modules/html2pdf.js/node_modules/jspdf": {"version": "3.0.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.26.7", "atob": "^2.1.2", "btoa": "^1.2.1", "fflate": "^0.8.1"}, "optionalDependencies": {"canvg": "^3.0.11", "core-js": "^3.6.0", "dompurify": "^3.2.4", "html2canvas": "^1.0.0-rc.5"}}, "node_modules/http-errors": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/husky": {"version": "8.0.3", "dev": true, "license": "MIT", "bin": {"husky": "lib/bin.js"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.3.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immer": {"version": "10.1.1", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/immutable": {"version": "5.1.2", "license": "MIT"}, "node_modules/import-fresh": {"version": "3.3.1", "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/internal-slot": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/intl-messageformat": {"version": "10.7.16", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@formatjs/ecma402-abstract": "2.3.4", "@formatjs/fast-memoize": "2.2.7", "@formatjs/icu-messageformat-parser": "2.11.2", "tslib": "^2.8.0"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-array-buffer": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.3.2", "license": "MIT", "optional": true}, "node_modules/is-async-function": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-boolean-object": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "license": "MIT"}, "node_modules/is-bun-module": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.7.1"}}, "node_modules/is-callable": {"version": "1.2.7", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.16.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-view": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finalizationregistry": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-generator-function": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "devOptional": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-map": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-promise": {"version": "4.0.0", "dev": true, "license": "MIT"}, "node_modules/is-regex": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-retina": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/is-retina/-/is-retina-1.0.3.tgz", "integrity": "sha512-/tCmbIETZwCd8uHWO+GvbRa7jxwHFHdfetHfiwoP0aN9UDf3prUJMtKn7iBFYipYhqY1bSTjur8hC/Dakt8eyw==", "license": "MIT"}, "node_modules/is-set": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-string": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.15", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakmap": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/isarray": {"version": "2.0.5", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/iterator.prototype": {"version": "1.1.5", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "get-proto": "^1.0.0", "has-symbols": "^1.1.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/jose": {"version": "4.15.9", "license": "MIT", "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/js-cookie": {"version": "3.0.5", "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/jspdf": {"version": "2.5.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.2", "atob": "^2.1.2", "btoa": "^1.2.1", "fflate": "^0.8.1"}, "optionalDependencies": {"canvg": "^3.0.6", "core-js": "^3.6.0", "dompurify": "^2.5.4", "html2canvas": "^1.0.0-rc.5"}}, "node_modules/jsx-ast-utils": {"version": "3.3.5", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "object.assign": "^4.1.4", "object.values": "^1.1.6"}, "engines": {"node": ">=4.0"}}, "node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/language-subtag-registry": {"version": "0.3.23", "dev": true, "license": "CC0-1.0"}, "node_modules/language-tags": {"version": "1.0.9", "dev": true, "license": "MIT", "dependencies": {"language-subtag-registry": "^0.3.20"}, "engines": {"node": ">=0.10"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lilconfig": {"version": "3.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "license": "MIT"}, "node_modules/lint-staged": {"version": "16.0.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^5.4.1", "commander": "^13.1.0", "debug": "^4.4.0", "lilconfig": "^3.1.3", "listr2": "^8.3.3", "micromatch": "^4.0.8", "nano-spawn": "^1.0.0", "pidtree": "^0.6.0", "string-argv": "^0.3.2", "yaml": "^2.7.1"}, "bin": {"lint-staged": "bin/lint-staged.js"}, "engines": {"node": ">=20.18"}, "funding": {"url": "https://opencollective.com/lint-staged"}}, "node_modules/lint-staged/node_modules/chalk": {"version": "5.4.1", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/lint-staged/node_modules/yaml": {"version": "2.8.0", "dev": true, "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/listr2": {"version": "8.3.3", "dev": true, "license": "MIT", "dependencies": {"cli-truncate": "^4.0.0", "colorette": "^2.0.20", "eventemitter3": "^5.0.1", "log-update": "^6.1.0", "rfdc": "^1.4.1", "wrap-ansi": "^9.0.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "node_modules/lodash.memoize": {"version": "4.1.2", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.reduce": {"version": "4.6.0", "license": "MIT"}, "node_modules/lodash.startswith": {"version": "4.2.1", "license": "MIT"}, "node_modules/log-update": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^7.0.0", "cli-cursor": "^5.0.0", "slice-ansi": "^7.1.0", "strip-ansi": "^7.1.0", "wrap-ansi": "^9.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/log-update/node_modules/is-fullwidth-code-point": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"get-east-asian-width": "^1.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/slice-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "is-fullwidth-code-point": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lottie-react": {"version": "2.4.1", "license": "MIT", "dependencies": {"lottie-web": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/lottie-web": {"version": "5.13.0", "license": "MIT"}, "node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/lz-string": {"version": "1.5.0", "license": "MIT", "bin": {"lz-string": "bin/bin.js"}}, "node_modules/make-event-props": {"version": "1.6.2", "license": "MIT", "funding": {"url": "https://github.com/wojtekmaj/make-event-props?sponsor=1"}}, "node_modules/map-age-cleaner": {"version": "0.1.3", "license": "MIT", "dependencies": {"p-defer": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/marked": {"version": "4.0.12", "license": "MIT", "bin": {"marked": "bin/marked.js"}, "engines": {"node": ">= 12"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/md5": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/md5/-/md5-2.3.0.tgz", "integrity": "sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"charenc": "0.0.2", "crypt": "0.0.2", "is-buffer": "~1.1.6"}}, "node_modules/media-typer": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/mem": {"version": "8.1.1", "license": "MIT", "dependencies": {"map-age-cleaner": "^0.1.3", "mimic-fn": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/mem?sponsor=1"}}, "node_modules/memoize-one": {"version": "6.0.0", "license": "MIT"}, "node_modules/merge-descriptors": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge2": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "devOptional": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime-db": {"version": "1.54.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/mimic-function": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/nano-spawn": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=20.17"}, "funding": {"url": "https://github.com/sindresorhus/nano-spawn?sponsor=1"}}, "node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/napi-postinstall": {"version": "0.2.3", "dev": true, "license": "MIT", "bin": {"napi-postinstall": "lib/cli.js"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/napi-postinstall"}}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/negotiator": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/next": {"version": "15.2.0", "license": "MIT", "dependencies": {"@next/env": "15.2.0", "@swc/counter": "0.1.3", "@swc/helpers": "0.5.15", "busboy": "1.6.0", "caniuse-lite": "^1.0.30001579", "postcss": "8.4.31", "styled-jsx": "5.1.6"}, "bin": {"next": "dist/bin/next"}, "engines": {"node": "^18.18.0 || ^19.8.0 || >= 20.0.0"}, "optionalDependencies": {"@next/swc-darwin-arm64": "15.2.0", "@next/swc-darwin-x64": "15.2.0", "@next/swc-linux-arm64-gnu": "15.2.0", "@next/swc-linux-arm64-musl": "15.2.0", "@next/swc-linux-x64-gnu": "15.2.0", "@next/swc-linux-x64-musl": "15.2.0", "@next/swc-win32-arm64-msvc": "15.2.0", "@next/swc-win32-x64-msvc": "15.2.0", "sharp": "^0.33.5"}, "peerDependencies": {"@opentelemetry/api": "^1.1.0", "@playwright/test": "^1.41.2", "babel-plugin-react-compiler": "*", "react": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", "react-dom": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", "sass": "^1.3.0"}, "peerDependenciesMeta": {"@opentelemetry/api": {"optional": true}, "@playwright/test": {"optional": true}, "babel-plugin-react-compiler": {"optional": true}, "sass": {"optional": true}}}, "node_modules/next-auth": {"version": "4.24.11", "license": "ISC", "dependencies": {"@babel/runtime": "^7.20.13", "@panva/hkdf": "^1.0.2", "cookie": "^0.7.0", "jose": "^4.15.5", "oauth": "^0.9.15", "openid-client": "^5.4.0", "preact": "^10.6.3", "preact-render-to-string": "^5.1.19", "uuid": "^8.3.2"}, "peerDependencies": {"@auth/core": "0.34.2", "next": "^12.2.5 || ^13 || ^14 || ^15", "nodemailer": "^6.6.5", "react": "^17.0.2 || ^18 || ^19", "react-dom": "^17.0.2 || ^18 || ^19"}, "peerDependenciesMeta": {"@auth/core": {"optional": true}, "nodemailer": {"optional": true}}}, "node_modules/next-intl": {"version": "4.1.0", "funding": [{"type": "individual", "url": "https://github.com/sponsors/amannn"}], "license": "MIT", "dependencies": {"@formatjs/intl-localematcher": "^0.5.4", "negotiator": "^1.0.0", "use-intl": "^4.1.0"}, "peerDependencies": {"next": "^12.0.0 || ^13.0.0 || ^14.0.0 || ^15.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc <19.0.0 || ^19.0.0", "typescript": "^5.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/node-addon-api": {"version": "7.1.1", "license": "MIT", "optional": true}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "deprecated": "Use your platform's native DOMException instead", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "engines": {"node": ">=10.5.0"}}, "node_modules/oauth": {"version": "0.9.15", "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.13.4", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.entries": {"version": "1.1.9", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-object-atoms": "^1.1.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.fromentries": {"version": "2.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.groupby": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.values": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/oidc-token-hash": {"version": "5.1.0", "license": "MIT", "engines": {"node": "^10.13.0 || >=12.0.0"}}, "node_modules/on-finished": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-function": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/openid-client": {"version": "5.7.1", "license": "MIT", "dependencies": {"jose": "^4.15.9", "lru-cache": "^6.0.0", "object-hash": "^2.2.0", "oidc-token-hash": "^5.0.3"}, "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/optionator": {"version": "0.9.4", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/own-keys": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/p-defer": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pako": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/pako/-/pako-2.1.0.tgz", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==", "license": "(MIT AND Zlib)"}, "node_modules/parent-module": {"version": "1.0.1", "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parseurl": {"version": "1.3.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-to-regexp": {"version": "8.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/path-type": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT", "optional": true}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pidtree": {"version": "0.6.0", "dev": true, "license": "MIT", "bin": {"pidtree": "bin/pidtree.js"}, "engines": {"node": ">=0.10"}}, "node_modules/pkce-challenge": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=16.20.0"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postcss": {"version": "8.4.31", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/preact": {"version": "10.26.8", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/preact"}}, "node_modules/preact-render-to-string": {"version": "5.2.6", "license": "MIT", "dependencies": {"pretty-format": "^3.8.0"}, "peerDependencies": {"preact": ">=10"}}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "3.5.3", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/pretty-format": {"version": "3.8.0", "license": "MIT"}, "node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/property-expr": {"version": "2.0.6", "license": "MIT"}, "node_modules/proxy-addr": {"version": "2.0.7", "dev": true, "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.14.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/raf": {"version": "3.4.1", "license": "MIT", "optional": true, "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/react": {"version": "19.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-avatar": {"version": "5.0.4", "resolved": "https://registry.npmjs.org/react-avatar/-/react-avatar-5.0.4.tgz", "integrity": "sha512-5oJRv9A02KBJGtVIpCZjU2yWGKetUX0Ft5GMCJ2oDM53XfhuubIGb1WsZJLcCh/QywpAXap8g7R9NVAGtp+Amw==", "license": "MIT", "dependencies": {"is-retina": "^1.0.3", "md5": "^2.0.0"}, "peerDependencies": {"@babel/runtime": ">=7", "core-js-pure": ">=3", "prop-types": "^15.0.0 || ^16.0.0", "react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/react-chartjs-2": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/react-chartjs-2/-/react-chartjs-2-5.3.0.tgz", "integrity": "sha512-UfZZFnDsERI3c3CZGxzvNJd02SHjaSJ8kgW1djn65H1KK8rehwTjyrRKOG3VTMG8wtHZ5rgAO5oTHtHi9GCCmw==", "license": "MIT", "peerDependencies": {"chart.js": "^4.1.1", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/react-circular-progressbar": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/react-circular-progressbar/-/react-circular-progressbar-2.2.0.tgz", "integrity": "sha512-cgyqEHOzB0nWMZjKfWN3MfSa1LV3OatcDjPz68lchXQUEiBD5O1WsAtoVK4/DSL0B4USR//cTdok4zCBkq8X5g==", "license": "MIT", "peerDependencies": {"react": ">=0.14.0"}}, "node_modules/react-clock": {"version": "5.1.0", "license": "MIT", "dependencies": {"@wojtekmaj/date-utils": "^1.5.0", "clsx": "^2.0.0", "get-user-locale": "^2.2.1"}, "funding": {"url": "https://github.com/wojtekmaj/react-clock?sponsor=1"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-datepicker": {"version": "8.4.0", "license": "MIT", "dependencies": {"@floating-ui/react": "^0.27.3", "clsx": "^2.1.1", "date-fns": "^4.1.0"}, "peerDependencies": {"react": "^16.9.0 || ^17 || ^18 || ^19 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17 || ^18 || ^19 || ^19.0.0-rc"}}, "node_modules/react-dom": {"version": "19.1.0", "license": "MIT", "dependencies": {"scheduler": "^0.26.0"}, "peerDependencies": {"react": "^19.1.0"}}, "node_modules/react-fit": {"version": "2.0.1", "license": "MIT", "dependencies": {"detect-element-overflow": "^1.4.0", "warning": "^4.0.0"}, "funding": {"url": "https://github.com/wojtekmaj/react-fit?sponsor=1"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/react-hook-form": {"version": "7.56.3", "license": "MIT", "engines": {"node": ">=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/react-hook-form"}, "peerDependencies": {"react": "^16.8.0 || ^17 || ^18 || ^19"}}, "node_modules/react-hot-toast": {"version": "2.5.2", "license": "MIT", "dependencies": {"csstype": "^3.1.3", "goober": "^2.1.16"}, "engines": {"node": ">=10"}, "peerDependencies": {"react": ">=16", "react-dom": ">=16"}}, "node_modules/react-icons": {"version": "5.5.0", "license": "MIT", "peerDependencies": {"react": "*"}}, "node_modules/react-infinite-scroll-component": {"version": "6.1.0", "license": "MIT", "dependencies": {"throttle-debounce": "^2.1.0"}, "peerDependencies": {"react": ">=16.0.0"}}, "node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/react-jwt": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">=10"}, "optionalDependencies": {"fsevents": "^2.3.2"}, "peerDependencies": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/react-loading-skeleton": {"version": "3.5.0", "license": "MIT", "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/react-otp-input": {"version": "3.1.1", "license": "MIT", "peerDependencies": {"react": ">=16.8.6 || ^17.0.0 || ^18.0.0", "react-dom": ">=16.8.6 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-phone-input-2": {"version": "2.15.1", "license": "MIT", "dependencies": {"classnames": "^2.2.6", "lodash.debounce": "^4.0.8", "lodash.memoize": "^4.1.2", "lodash.reduce": "^4.6.0", "lodash.startswith": "^4.2.1", "prop-types": "^15.7.2"}, "peerDependencies": {"react": "^16.12.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^20.0.0 || ^21.0.0", "react-dom": "^16.12.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^20.0.0 || ^21.0.0"}}, "node_modules/react-redux": {"version": "9.2.0", "license": "MIT", "dependencies": {"@types/use-sync-external-store": "^0.0.6", "use-sync-external-store": "^1.4.0"}, "peerDependencies": {"@types/react": "^18.2.25 || ^19", "react": "^18.0 || ^19", "redux": "^5.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "redux": {"optional": true}}}, "node_modules/react-select": {"version": "5.10.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.0", "@emotion/cache": "^11.4.0", "@emotion/react": "^11.8.1", "@floating-ui/dom": "^1.0.1", "@types/react-transition-group": "^4.4.0", "memoize-one": "^6.0.0", "prop-types": "^15.6.0", "react-transition-group": "^4.3.0", "use-isomorphic-layout-effect": "^1.2.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/react-time-picker": {"version": "7.0.0", "license": "MIT", "dependencies": {"@wojtekmaj/date-utils": "^1.1.3", "clsx": "^2.0.0", "get-user-locale": "^2.2.1", "make-event-props": "^1.6.0", "react-clock": "^5.0.0", "react-fit": "^2.0.0", "update-input-width": "^1.4.0"}, "funding": {"url": "https://github.com/wojtekmaj/react-time-picker?sponsor=1"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-tooltip": {"version": "5.28.1", "license": "MIT", "dependencies": {"@floating-ui/dom": "^1.6.1", "classnames": "^2.3.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}}, "node_modules/react-transition-group": {"version": "4.4.5", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/readdirp": {"version": "4.1.2", "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/redux": {"version": "5.0.1", "license": "MIT"}, "node_modules/redux-persist": {"version": "6.0.0", "license": "MIT", "peerDependencies": {"redux": ">4.0.0"}}, "node_modules/redux-thunk": {"version": "3.1.0", "license": "MIT", "peerDependencies": {"redux": "^5.0.0"}}, "node_modules/reflect.getprototypeof": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT", "optional": true}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/reselect": {"version": "5.1.1", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.10", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve-pkg-maps": {"version": "1.0.0", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/privatenumber/resolve-pkg-maps?sponsor=1"}}, "node_modules/restore-cursor": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^7.0.0", "signal-exit": "^4.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/reusify": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "dev": true, "license": "MIT"}, "node_modules/rgbcolor": {"version": "1.0.1", "license": "MIT OR SEE LICENSE IN FEEL-FREE.md", "optional": true, "engines": {"node": ">= 0.8.15"}}, "node_modules/router": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-array-concat": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-push-apply": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/sass": {"version": "1.88.0", "license": "MIT", "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}}, "node_modules/scheduler": {"version": "0.26.0", "license": "MIT"}, "node_modules/sdp": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/sdp/-/sdp-3.2.1.tgz", "integrity": "sha512-lwsAIzOPlH8/7IIjjz3K0zYBk7aBVVcvjMwt3M4fLxpjMYyy7i3I97SLHebgn4YBjirkzfp3RvRDWSKsh/+WFw==", "license": "MIT"}, "node_modules/secure-ls": {"version": "2.0.0", "license": "MIT", "dependencies": {"crypto-js": "^4.2.0", "lz-string": "^1.5.0"}, "engines": {"node": ">=8.0"}}, "node_modules/semver": {"version": "7.7.1", "devOptional": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/serve-static": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/set-function-length": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-proto": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/setprototypeof": {"version": "1.2.0", "dev": true, "license": "ISC"}, "node_modules/sharp": {"version": "0.33.5", "hasInstallScript": true, "license": "Apache-2.0", "optional": true, "dependencies": {"color": "^4.2.3", "detect-libc": "^2.0.3", "semver": "^7.6.3"}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "0.33.5", "@img/sharp-darwin-x64": "0.33.5", "@img/sharp-libvips-darwin-arm64": "1.0.4", "@img/sharp-libvips-darwin-x64": "1.0.4", "@img/sharp-libvips-linux-arm": "1.0.5", "@img/sharp-libvips-linux-arm64": "1.0.4", "@img/sharp-libvips-linux-s390x": "1.0.4", "@img/sharp-libvips-linux-x64": "1.0.4", "@img/sharp-libvips-linuxmusl-arm64": "1.0.4", "@img/sharp-libvips-linuxmusl-x64": "1.0.4", "@img/sharp-linux-arm": "0.33.5", "@img/sharp-linux-arm64": "0.33.5", "@img/sharp-linux-s390x": "0.33.5", "@img/sharp-linux-x64": "0.33.5", "@img/sharp-linuxmusl-arm64": "0.33.5", "@img/sharp-linuxmusl-x64": "0.33.5", "@img/sharp-wasm32": "0.33.5", "@img/sharp-win32-ia32": "0.33.5", "@img/sharp-win32-x64": "0.33.5"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/simple-swizzle": {"version": "0.2.2", "license": "MIT", "optional": true, "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/slice-ansi": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.0.0", "is-fullwidth-code-point": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/slice-ansi/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/socket.io-client": {"version": "4.8.1", "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.2", "engine.io-client": "~6.6.1", "socket.io-parser": "~4.2.4"}, "engines": {"node": ">=10.0.0"}}, "node_modules/socket.io-client/node_modules/debug": {"version": "4.3.7", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/socket.io-parser": {"version": "4.2.4", "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/socket.io-parser/node_modules/debug": {"version": "4.3.7", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/stable-hash": {"version": "0.0.5", "dev": true, "license": "MIT"}, "node_modules/stackblur-canvas": {"version": "2.7.0", "license": "MIT", "optional": true, "engines": {"node": ">=0.1.14"}}, "node_modules/statuses": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/streamsearch": {"version": "1.1.0", "engines": {"node": ">=10.0.0"}}, "node_modules/string-argv": {"version": "0.3.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.19"}}, "node_modules/string-width": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string-width/node_modules/emoji-regex": {"version": "10.4.0", "dev": true, "license": "MIT"}, "node_modules/string.prototype.includes": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/string.prototype.matchall": {"version": "4.0.12", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "regexp.prototype.flags": "^1.5.3", "set-function-name": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.repeat": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "node_modules/string.prototype.trim": {"version": "1.2.10", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.9", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/strip-bom": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/styled-jsx": {"version": "5.1.6", "license": "MIT", "dependencies": {"client-only": "0.0.1"}, "engines": {"node": ">= 12.0.0"}, "peerDependencies": {"react": ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "babel-plugin-macros": {"optional": true}}}, "node_modules/stylis": {"version": "4.2.0", "license": "MIT"}, "node_modules/suneditor": {"version": "2.47.5", "license": "MIT"}, "node_modules/suneditor-react": {"version": "3.6.1", "license": "MIT", "peerDependencies": {"react": ">= 16.8.0", "react-dom": ">= 16.8.0", "suneditor": "^2.44.10"}}, "node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svg-pathdata": {"version": "6.0.3", "license": "MIT", "optional": true, "engines": {"node": ">=12.0.0"}}, "node_modules/swiper": {"version": "11.2.8", "funding": [{"type": "patreon", "url": "https://www.patreon.com/swiperjs"}, {"type": "open_collective", "url": "http://opencollective.com/swiper"}], "license": "MIT", "engines": {"node": ">= 4.7.0"}}, "node_modules/synckit": {"version": "0.11.6", "dev": true, "license": "MIT", "dependencies": {"@pkgr/core": "^0.2.4"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/synckit"}}, "node_modules/tabbable": {"version": "6.2.0", "license": "MIT"}, "node_modules/text-segmentation": {"version": "1.0.3", "license": "MIT", "dependencies": {"utrie": "^1.0.2"}}, "node_modules/throttle-debounce": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/tiny-case": {"version": "1.0.3", "license": "MIT"}, "node_modules/tinyglobby": {"version": "0.2.13", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.4.4", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinyglobby/node_modules/picomatch": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/to-regex-range": {"version": "5.0.1", "devOptional": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/toposort": {"version": "2.0.2", "license": "MIT"}, "node_modules/ts-api-utils": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18.12"}, "peerDependencies": {"typescript": ">=4.8.4"}}, "node_modules/tsconfig-paths": {"version": "3.15.0", "dev": true, "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/turndown": {"version": "7.2.0", "license": "MIT", "dependencies": {"@mixmark-io/domino": "^2.2.0"}}, "node_modules/turndown-plugin-gfm": {"version": "1.0.2", "license": "MIT"}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "2.19.0", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typescript": {"version": "5.8.3", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/ua-parser-js": {"version": "0.7.40", "resolved": "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.40.tgz", "integrity": "sha512-us1E3K+3jJppDBa3Tl0L3MOJiGhe1C6P0+nIvQAFYbxlMAx0h81eOwLmU57xgqToduDDPx3y5QsdjPfDu+FgOQ==", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "license": "MIT", "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/unbox-primitive": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/undici-types": {"version": "6.19.8", "dev": true, "license": "MIT"}, "node_modules/unpipe": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unrs-resolver": {"version": "1.7.2", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"napi-postinstall": "^0.2.2"}, "funding": {"url": "https://github.com/sponsors/JounQin"}, "optionalDependencies": {"@unrs/resolver-binding-darwin-arm64": "1.7.2", "@unrs/resolver-binding-darwin-x64": "1.7.2", "@unrs/resolver-binding-freebsd-x64": "1.7.2", "@unrs/resolver-binding-linux-arm-gnueabihf": "1.7.2", "@unrs/resolver-binding-linux-arm-musleabihf": "1.7.2", "@unrs/resolver-binding-linux-arm64-gnu": "1.7.2", "@unrs/resolver-binding-linux-arm64-musl": "1.7.2", "@unrs/resolver-binding-linux-ppc64-gnu": "1.7.2", "@unrs/resolver-binding-linux-riscv64-gnu": "1.7.2", "@unrs/resolver-binding-linux-riscv64-musl": "1.7.2", "@unrs/resolver-binding-linux-s390x-gnu": "1.7.2", "@unrs/resolver-binding-linux-x64-gnu": "1.7.2", "@unrs/resolver-binding-linux-x64-musl": "1.7.2", "@unrs/resolver-binding-wasm32-wasi": "1.7.2", "@unrs/resolver-binding-win32-arm64-msvc": "1.7.2", "@unrs/resolver-binding-win32-ia32-msvc": "1.7.2", "@unrs/resolver-binding-win32-x64-msvc": "1.7.2"}}, "node_modules/update-input-width": {"version": "1.4.2", "license": "MIT", "funding": {"url": "https://github.com/wojtekmaj/update-input-width?sponsor=1"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/use-intl": {"version": "4.1.0", "license": "MIT", "dependencies": {"@formatjs/fast-memoize": "^2.2.0", "@schummar/icu-type-parser": "1.21.5", "intl-messageformat": "^10.5.14"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || >=19.0.0-rc <19.0.0 || ^19.0.0"}}, "node_modules/use-isomorphic-layout-effect": {"version": "1.2.0", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/use-sync-external-store": {"version": "1.5.0", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/utrie": {"version": "1.0.2", "license": "MIT", "dependencies": {"base64-arraybuffer": "^1.0.2"}}, "node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/vanilla-colorful": {"version": "0.7.2", "license": "MIT"}, "node_modules/vary": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/warning": {"version": "4.0.3", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/web-streams-polyfill": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/webrtc-adapter": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/webrtc-adapter/-/webrtc-adapter-8.2.0.tgz", "integrity": "sha512-umxCMgedPAVq4Pe/jl3xmelLXLn4XZWFEMR5Iipb5wJ+k1xMX0yC4ZY9CueZUU1MjapFxai1tFGE7R/kotH6Ww==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"sdp": "^3.0.2"}, "engines": {"node": ">=6.0.0", "npm": ">=3.10.0"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-collection": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.19", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/word-wrap": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "string-width": "^7.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/ws": {"version": "8.17.1", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xmlhttprequest-ssl": {"version": "2.1.2", "engines": {"node": ">=0.4.0"}}, "node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/yaml": {"version": "1.10.2", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yup": {"version": "1.6.1", "license": "MIT", "dependencies": {"property-expr": "^2.0.5", "tiny-case": "^1.0.3", "toposort": "^2.0.2", "type-fest": "^2.19.0"}}, "node_modules/zod": {"version": "3.24.4", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}, "node_modules/zod-to-json-schema": {"version": "3.24.5", "dev": true, "license": "ISC", "peerDependencies": {"zod": "^3.24.1"}}}}