import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity("applications_job_skills")
export default class ApplicationsJobSkillsModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "job_application_id", nullable: false })
  jobApplicationId: number;

  @Column({ name: "job_skill_id", nullable: false })
  jobSkillId: number;

  @CreateDateColumn({
    type: "timestamp",
    nullable: false,
    name: "created_ts",
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    nullable: false,
    name: "updated_ts",
  })
  updatedTs: Date;
}
