import * as Sentry from "@sentry/node";
import sendMail from "./sendgrid";

/**
 * Sends an assessment invitation email to a candidate
 * @param {Object} params
 * @param {string} params.candidateEmail - Candidate's email address
 * @param {string} params.candidateName - Candidate's name
 * @param {string} params.jobTitle - Job title
 * @param {string} params.assessmentLink - Link to the assessment
 * @returns {Promise<{isError: boolean, messageId?: string, message?: string, errorMessage?: string}>}
 */
const sendFinalAssessmentEmail = async ({
  candidateEmail,
  candidateName,
  jobTitle,
  assessmentLink,
}: {
  candidateEmail: string;
  candidateName: string;
  jobTitle: string;
  assessmentLink: string;
}) => {
  try {
    // Prepare email content
    const subject = `Assessment Invitation for ${jobTitle} position`;
    const textContent = `Hello ${candidateName},\n\nYou have been invited to complete an assessment for the ${jobTitle} position. Please click on the following link to access your assessment:\n\n${assessmentLink}\n\nThank you,\nRecruitment Team`;

    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Assessment Invitation</h2>
        <p>Hello ${candidateName},</p>
        <p>You have been invited to complete an assessment for the <strong>${jobTitle}</strong> position.</p>
        <p>Please click on the button below to access your assessment:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${assessmentLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Take Assessment</a>
        </div>
        <p>If the button doesn't work, copy and paste the following link into your browser:</p>
        <p style="word-break: break-all;">${assessmentLink}</p>
        <p>Thank you,<br>Recruitment Team</p>
      </div>
    `;

    // Send email
    const emailResult = await sendMail({
      email: candidateEmail,
      subject,
      textContent,
      htmlContent,
    });

    return emailResult;
  } catch (error) {
    Sentry.captureException(error);
    return {
      isError: true,
      errorMessage: "Failed to send assessment email",
    };
  }
};

export default sendFinalAssessmentEmail;
