import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import SubscriptionPlanModel from "./subscription_plans";

/* eslint-disable no-unused-vars */
enum PricingType {
  MONTHLY = "Monthly",
  QUARTERLY = "Quarterly",
  YEARLY = "Yearly",
}

@Entity("subscription_pricing")
class SubscriptionPricingModel {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => SubscriptionPlanModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "subscription_id" })
  subscriptionPlan: SubscriptionPlanModel;

  @Column({ name: "subscription_id", nullable: false })
  subscriptionId: number;

  @Column({
    type: "enum",
    enum: PricingType,
    name: "pricing_type",
    nullable: false,
  })
  pricingType: PricingType;

  @Column({
    name: "price",
    type: "decimal",
    precision: 10,
    scale: 2,
    nullable: false,
  })
  price: number;

  @Column({
    name: "stripe_price_id",
    type: "varchar",
    length: 50,
    nullable: false,
  })
  stripePriceId: string;

  @Column({ name: "currency", type: "varchar", length: 10, default: "USD" })
  currency: string;

  @Column({ name: "is_active", type: "varchar", length: 50, default: true })
  isActive: boolean;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}

export default SubscriptionPricingModel;
