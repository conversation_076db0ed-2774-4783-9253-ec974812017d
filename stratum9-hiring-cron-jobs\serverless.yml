service: stratum-hiring-cron-jobs
provider:
  name: aws
  runtime: nodejs20.x
  memorySize: 512
  timeout: 30
  stage: ${opt:stage, 'development'} # serverless deploy --stage development/production
  environment:
    NODE_ENV: ${opt:stage, self:provider.stage, 'development'}
  region: us-east-1

functions:
  generateInterviewQuestions:
    handler: src/lambdas/generateInterviewQuestions.generateInterviewQuestionsLambda

  generateIntFinalSummary:
    handler: src/lambdas/generateIntFinalSummary.generateIntFinalSummaryLambda

  generateInterviewFeedback:
    handler: src/lambdas/generateInterviewFeedback.generateInterviewFeedbackLambda

  handleSubscriptionLifecycle:
    handler: src/lambdas/handleFreePlanExpiration.handleSubscriptionLifecycleLambda
    # events:
    # Schedule to run daily at midnight UTC
    # - schedule: cron(0 0 * * ? *)

  AgoraDeleteAdditionalFiles:
    handler: src/lambdas/AgoraDeleteAdditionalFiles.AgoraAdditionalFilesDeleteLambda
    # events:
    # Schedule to run daily at midnight UTC
    # - schedule: cron(0 0 * * ? *)

plugins:
  - serverless-plugin-typescript

package:
  individually: true
  # exclude:
  #   - node_modules/**

  include:
    - src/**
