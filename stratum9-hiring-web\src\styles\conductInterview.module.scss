@use "./abstracts" as *;

.conduct_interview_page {
  padding-bottom: 40px;

  // Profile image fallback styles
  .profile_image_fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    background-color: #f0f0f0;
    border-radius: 12px;
    font-size: 36px;
    font-weight: bold;
    color: #666;
    border: 2px solid #e0e0e0;
  }

  .question_info_box {
    ul {
      display: flex;
      gap: 4rem;
      align-items: center;
      @extend %listSpacing;
      margin-bottom: 30px;
      li {
        font-size: $text-sm;
        font-weight: $medium;
        color: $dark;
        display: flex;
        align-items: center;
        gap: 1rem;
        span {
          width: 20px;
          height: 20px;
          border-radius: 100%;
          display: block;
          &.current {
            background: $secondary;
          }
          &.completed {
            background: $primary;
          }
          &.additional {
            background: rgba($green, 1);
          }
        }
      }
    }
  }
}

// .video_call_section {
//   border: 1px solid rgba($dark, 0.1);
//   border-radius: 12px;
//   .interview-content {
//     padding: 20px;

//     .common-page-head-section {
//       .main-heading {
//         margin-bottom: 20px;
//         h2 {
//           font-size: 1.8rem;
//         }
//       }
//     }
//     .interview-question-cards-height {
//       height: 60vh;
//       overflow-y: auto;
//     }
//   }
// }

// .video_container {
//   margin-bottom: 30px;
//   background-color: #f8f9fa;
//   border-radius: 12px;
//   padding: 20px;
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
// }

// .video_grid {
//   display: flex;
//   flex-wrap: wrap;
//   gap: 20px;
//   margin-bottom: 20px;

//   @media (max-width: 768px) {
//     flex-direction: column;
//   }
// }

// .video_box {
//   flex: 1;
//   min-width: 300px;
//   min-height: 300px;
//   border-radius: 8px;
//   position: relative;
//   overflow: hidden;
//   background-color: #222;

//   &.placeholder {
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     background-color: rgba(34, 34, 34, 0.7);
//   }
// }

// .video_player {
//   width: 100%;
//   height: 100%;
//   object-fit: cover;
//   border-radius: 8px;
// }

// .waiting_message {
//   color: white;
//   font-size: 1.2rem;
//   text-align: center;
//   padding: 20px;
// }

// .user_label {
//   position: absolute;
//   bottom: 10px;
//   left: 10px;
//   background-color: rgba(0, 0, 0, 0.6);
//   color: white;
//   padding: 5px 10px;
//   border-radius: 4px;
//   font-size: 0.9rem;
// }

// .controls {
//   display: flex;
//   justify-content: center;
//   gap: 12px;
//   padding: 15px 0;
// }

// .control_btn {
//   padding: 10px 20px;
//   border-radius: 30px;
//   border: none;
//   cursor: pointer;
//   font-weight: $medium;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   min-width: 120px;
//   background-color: $primary;
//   color: white;
//   transition: all 0.3s ease;

//   &:hover {
//     background-color: darken($primary, 10%);
//   }

//   &.off {
//     background-color: #6c757d;

//     &:hover {
//       background-color: darken(#6c757d, 10%);
//     }
//   }

//   &.leave {
//     background-color: #dc3545;

//     &:hover {
//       background-color: darken(#dc3545, 10%);
//     }
//   }
// }

// .recording_timer {
//   text-align: center;
//   padding: 15px 0;
//   background-color: #f8f9fa;
//   border-radius: 8px 8px 0 0;
//   margin-top: 20px;
//   border: 1px solid rgba($dark, 0.1);
//   border-bottom: none;
// }

// .video_call_container {
//   border: 1px solid rgba($dark, 0.1);
//   border-radius: 0 0 8px 8px;
//   background-color: #fff;
//   padding: 20px;
//   margin-bottom: 30px;
//   box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
// }

.video_participants_grid {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  background: #f1f2f4;
  padding: 20px;
  // border: 1px solid #00000033;
  height: 100%;
  justify-content: flex-start;
  .video_participant_box {
    position: relative;
    border-radius: 9px;
    overflow: hidden;
    background-color: #000;
    // aspect-ratio: 16/9;
    aspect-ratio: 13/10;

    .participant_name {
      position: absolute;
      bottom: 5px;
      left: 5px;
      background-color: $primary;
      color: $white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 1rem;
      font-weight: $medium;
      z-index: 10;
    }

    .video_feed {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .empty_video {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: rgba($primary, 0.1);

      p {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
      }
    }
  }
  .video_controls {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 20px;

    .control_btn {
      background-color: $primary;
      color: white;
      border: none;
      border-radius: 6px;
      padding: 10px 15px;
      font-size: 14px;
      font-weight: $medium;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 120px;

      &:hover {
        background-color: darken($primary, 10%);
      }

      &.off {
        background-color: #6c757d;

        &:hover {
          background-color: darken(#6c757d, 10%);
        }
      }
    }
  }
}

// Candidate Confirmation page styles
.candidate_confirmation {
  padding: 0px 5vw;
  .candidate_interview_lottie {
    width: 100%;
    height: 100vh;
    object-fit: contain;
  }

  .confirmation {
    padding: 40px;
    border-radius: 30px;
    background: $white;
    box-shadow: 0px 4px 12px 0px rgba($dark, 0.09);
    text-align: center;
    &_logo {
      width: 16vw;
      height: 7vh;
      object-fit: contain;
    }
    &_title {
      font-size: 1.8vw;
      font-weight: $bold;
      margin-top: 1.5vw;
      color: $dark;
    }
    &_text {
      font-size: 0.8vw;
      margin-top: 1vw;
      line-height: 1.4;
      font-weight: $medium;
      color: rgba($dark, 0.8);
    }
    &_input_group {
      text-align: left;
      margin-top: 20px;
      &_icon {
        position: absolute;
        right: 10px;
        top: 50%;
        // transform: translateY(-50%);
        cursor: pointer;
        z-index: 1;
        padding: 5px 7px;
        margin-top: 1px;
        border-radius: 5px;
        svg {
          width: 15px;
          height: 15px;
        }
      }
      p {
        font-size: 1.4rem;
      }
    }
  }
}
