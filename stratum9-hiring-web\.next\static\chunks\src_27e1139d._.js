(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_27e1139d._.js", {

"[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
;
;
/**
 * Wrapper component for input fields
 * @param {string} className - Class name for the input field
 * @returns {JSX.Element} - Wrapper component
 */ const InputWrapper = ({ className, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `form-group ${className ?? ""}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 10,
        columnNumber: 3
    }, this);
_c = InputWrapper;
/**
 * Label component for input fields
 * @param {string} children - Label text
 * @returns {JSX.Element} - Label component
 */ InputWrapper.Label = function({ children, htmlFor, required, className, onClick, style }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
        htmlFor: htmlFor,
        className: className,
        onClick: onClick,
        style: style,
        children: [
            children,
            required ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("sup", {
                children: "*"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/InputWrapper.tsx",
                lineNumber: 37,
                columnNumber: 19
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
/**
 * Error component for input fields to display error message
 * @param { string } message - Error message
 * @param { React.CSSProperties } style - Optional style object
 * @returns { JSX.Element } - Error component
 */ InputWrapper.Error = function({ message, style }) {
    return message ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        className: "auth-msg error",
        style: style,
        children: message
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this) : null;
};
/**
 * Icon component for input fields
 * @param { string } src - Icon source
 * @param { function } onClick - Function to be called on click
 * @returns { JSX.Element } - Icon component
 */ InputWrapper.Icon = function({ children, // src,
onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "show-icon",
        type: "button",
        onClick: onClick,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = InputWrapper;
var _c;
__turbopack_context__.k.register(_c, "InputWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/LetterFoldIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function LetterFoldIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "50",
        height: "66",
        viewBox: "0 0 50 66",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M0 0.580553L49.5778 0.224609V65.0572L0 0.580553Z",
            fill: "black",
            fillOpacity: "0.1"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/LetterFoldIcon.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/LetterFoldIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = LetterFoldIcon;
const __TURBOPACK__default__export__ = LetterFoldIcon;
var _c;
__turbopack_context__.k.register(_c, "LetterFoldIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/AiMarkIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function AiMarkIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "25",
        height: "25",
        viewBox: "0 0 32 32",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M13.294 7.43666L14.097 9.66666C14.989 12.1417 16.938 14.0907 19.413 14.9827L21.643 15.7857C21.844 15.8587 21.844 16.1437 21.643 16.2157L19.413 17.0187C16.938 17.9107 14.989 19.8597 14.097 22.3347L13.294 24.5647C13.221 24.7657 12.936 24.7657 12.864 24.5647L12.061 22.3347C11.169 19.8597 9.22001 17.9107 6.74501 17.0187L4.51501 16.2157C4.31401 16.1427 4.31401 15.8577 4.51501 15.7857L6.74501 14.9827C9.22001 14.0907 11.169 12.1417 12.061 9.66666L12.864 7.43666C12.936 7.23466 13.221 7.23466 13.294 7.43666Z",
                fill: "black"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/AiMarkIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M23.3321 2.07725L23.7391 3.20625C24.1911 4.45925 25.1781 5.44625 26.4311 5.89825L27.5601 6.30525C27.6621 6.34225 27.6621 6.48625 27.5601 6.52325L26.4311 6.93025C25.1781 7.38225 24.1911 8.36925 23.7391 9.62225L23.3321 10.7513C23.2951 10.8533 23.1511 10.8533 23.1141 10.7513L22.7071 9.62225C22.2551 8.36925 21.2681 7.38225 20.0151 6.93025L18.8861 6.52325C18.7841 6.48625 18.7841 6.34225 18.8861 6.30525L20.0151 5.89825C21.2681 5.44625 22.2551 4.45925 22.7071 3.20625L23.1141 2.07725C23.1511 1.97425 23.2961 1.97425 23.3321 2.07725Z",
                fill: "black"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/AiMarkIcon.tsx",
                lineNumber: 10,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M23.3321 21.2484L23.7391 22.3774C24.1911 23.6304 25.1781 24.6174 26.4311 25.0694L27.5601 25.4764C27.6621 25.5134 27.6621 25.6574 27.5601 25.6944L26.4311 26.1014C25.1781 26.5534 24.1911 27.5404 23.7391 28.7934L23.3321 29.9224C23.2951 30.0244 23.1511 30.0244 23.1141 29.9224L22.7071 28.7934C22.2551 27.5404 21.2681 26.5534 20.0151 26.1014L18.8861 25.6944C18.7841 25.6574 18.7841 25.5134 18.8861 25.4764L20.0151 25.0694C21.2681 24.6174 22.2551 23.6304 22.7071 22.3774L23.1141 21.2484C23.1511 21.1464 23.2961 21.1464 23.3321 21.2484Z",
                fill: "black"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/AiMarkIcon.tsx",
                lineNumber: 14,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/AiMarkIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = AiMarkIcon;
const __TURBOPACK__default__export__ = AiMarkIcon;
var _c;
__turbopack_context__.k.register(_c, "AiMarkIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/EditIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function EditIcon({ className, fillNone, fillColor }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "20",
        viewBox: "0 0 27 26",
        fill: "none",
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M25.6677 25.2539H15.9971C15.4451 25.2539 14.9971 24.8059 14.9971 24.2539C14.9971 23.7019 15.4451 23.2539 15.9971 23.2539H25.6677C26.2197 23.2539 26.6677 23.7019 26.6677 24.2539C26.6677 24.8059 26.2197 25.2539 25.6677 25.2539Z",
                fill: !fillNone ? "#436EB6" : fillColor ? fillColor : ""
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("mask", {
                id: "mask0_11116_355",
                style: {
                    maskType: "luminance"
                },
                maskUnits: "userSpaceOnUse",
                x: "0",
                y: "0",
                width: "24",
                height: "26",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    fillRule: "evenodd",
                    clipRule: "evenodd",
                    d: "M0.666992 0H23.5744V25.2527H0.666992V0Z",
                    fill: "white"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                    lineNumber: 19,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                mask: "url(#mask0_11116_355)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    fillRule: "evenodd",
                    clipRule: "evenodd",
                    d: "M15.4807 2.68886L2.92736 18.3889C2.69936 18.6742 2.61536 19.0422 2.69936 19.3955L3.60736 23.2422L7.65936 23.1915C8.04469 23.1875 8.40069 23.0155 8.63669 22.7222C12.926 17.3555 21.1034 7.12352 21.3354 6.82352C21.554 6.46886 21.6394 5.96752 21.5247 5.48486C21.4074 4.99019 21.0994 4.57019 20.6554 4.30219C20.5607 4.23686 18.314 2.49286 18.2447 2.43819C17.3994 1.76086 16.166 1.87819 15.4807 2.68886ZM2.81802 25.2529C2.35536 25.2529 1.95269 24.9355 1.84469 24.4835L0.752691 19.8555C0.527358 18.8969 0.751358 17.9075 1.36602 17.1395L13.926 1.43019C13.9314 1.42486 13.9354 1.41819 13.9407 1.41286C15.318 -0.23381 17.8087 -0.476476 19.4887 0.871523C19.5554 0.923523 21.786 2.65686 21.786 2.65686C22.5967 3.13952 23.23 4.00219 23.47 5.02352C23.7087 6.03419 23.5354 7.07686 22.9794 7.95819C22.938 8.02352 22.902 8.07952 10.198 23.9729C9.58603 24.7355 8.66869 25.1795 7.68336 25.1915L2.83136 25.2529H2.81802Z",
                    fill: !fillNone ? "#436EB6" : fillColor ? fillColor : ""
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                    lineNumber: 22,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M19.6316 11.5792C19.4182 11.5792 19.2049 11.5112 19.0222 11.3725L11.7529 5.78851C11.3156 5.45251 11.2329 4.82584 11.5689 4.38584C11.9062 3.94851 12.5329 3.86717 12.9716 4.20317L20.2422 9.78584C20.6796 10.1218 20.7622 10.7498 20.4249 11.1885C20.2289 11.4445 19.9316 11.5792 19.6316 11.5792Z",
                fill: !fillNone ? "#436EB6" : fillColor ? fillColor : ""
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
_c = EditIcon;
const __TURBOPACK__default__export__ = EditIcon;
var _c;
__turbopack_context__.k.register(_c, "EditIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/formElements/Textbox.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CommonInput": (()=>CommonInput),
    "default": (()=>Textbox)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
;
;
function Textbox({ children, control, name, iconClass, align, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${iconClass} ${align}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
                control: control,
                name: name,
                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        ...props,
                        value: field.value,
                        onChange: (e)=>{
                            field.onChange(e);
                            props.onChange?.(e);
                        },
                        "aria-label": ""
                    }, void 0, false, {
                        fileName: "[project]/src/components/formElements/Textbox.tsx",
                        lineNumber: 23,
                        columnNumber: 11
                    }, void 0),
                defaultValue: ""
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Textbox.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/Textbox.tsx",
        lineNumber: 18,
        columnNumber: 5
    }, this);
}
_c = Textbox;
function CommonInput({ iconClass, children, align, onChange, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${iconClass} ${align}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                ...props,
                onChange: onChange
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Textbox.tsx",
                lineNumber: 43,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/Textbox.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, this);
}
_c1 = CommonInput;
var _c, _c1;
__turbopack_context__.k.register(_c, "Textbox");
__turbopack_context__.k.register(_c1, "CommonInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/formElements/Textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
;
;
function Textarea({ control, name, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
        control: control,
        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                ...props,
                value: field.value,
                onChange: field.onChange,
                "aria-label": ""
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Textarea.tsx",
                lineNumber: 13,
                columnNumber: 30
            }, void 0),
        name: name,
        defaultValue: ""
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/Textarea.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
_c = Textarea;
var _c;
__turbopack_context__.k.register(_c, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/interviewServices.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addInterviewSkillQuestion": (()=>addInterviewSkillQuestion),
    "conductInterviewStaticInformation": (()=>conductInterviewStaticInformation),
    "endInterview": (()=>endInterview),
    "getCandidateList": (()=>getCandidateList),
    "getInterviewFeedback": (()=>getInterviewFeedback),
    "getInterviewSkillQuestions": (()=>getInterviewSkillQuestions),
    "getInterviewers": (()=>getInterviewers),
    "getJobList": (()=>getJobList),
    "getMyInterviews": (()=>getMyInterviews),
    "getPendingInterviews": (()=>getPendingInterviews),
    "upcomigOrPastInterview": (()=>upcomigOrPastInterview),
    "updateInterviewAnswers": (()=>updateInterviewAnswers),
    "updateInterviewFeedback": (()=>updateInterviewFeedback),
    "updateInterviewSkillQuestion": (()=>updateInterviewSkillQuestion),
    "updateOrScheduleInterview": (()=>updateOrScheduleInterview)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
;
;
const updateOrScheduleInterview = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.UPDATE_OR_SCHEDULE_INTERVIEW, data);
};
const getInterviewers = (searchString, jobId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_INTERVIEWERS, {
        searchString,
        jobId
    });
};
const upcomigOrPastInterview = (params)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_UPCOMING_OR_PAST_INTERVIEW, {
        ...params
    });
};
const getMyInterviews = (monthYear)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_MY_INTERVIEWS, {
        monthYear
    });
};
const getInterviewSkillQuestions = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_INTERVIEW_SKILL_QUESTIONS, data);
};
const updateInterviewSkillQuestion = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.UPDATE_INTERVIEW_SKILL_QUESTION, data);
};
const addInterviewSkillQuestion = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.ADD_INTERVIEW_SKILL_QUESTION, data);
};
const getJobList = (searchString)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_JOB_LIST, {
        searchString
    });
};
const getCandidateList = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_CANDIDATE_LIST, data);
};
const updateInterviewAnswers = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.UPDATE_INTERVIEW_ANSWERS, data);
};
const endInterview = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.END_INTERVIEW, data);
};
const conductInterviewStaticInformation = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.CONDUCT_INTERVIEW_STATIC_INFORMATION);
};
const getInterviewFeedback = (interviewId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_INTERVIEW_FEEDBACK.replace(":interviewId", interviewId.toString()));
};
const updateInterviewFeedback = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.UPDATE_INTERVIEW_FEEDBACK, data);
};
const getPendingInterviews = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_PENDING_INTERVIEWS);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/RejectedIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
const RejectedIcon = (props)=>{
    const { className } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        viewBox: "0 0 118 120",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M36.1415 33.3624L34.335 35.1753L33.3438 34.1841L35.1502 32.3711L36.1415 33.3624Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 5,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M45.2324 29.6365L43.6086 30.6669L40.0152 27.83L39.9891 27.843L42.2977 31.4886L40.7065 32.4994L36.0371 25.1301L38.1109 23.8193C38.5218 23.5584 38.9914 23.3823 39.5065 23.2845C40.0217 23.1867 40.4783 23.2584 40.8761 23.4997C41.2739 23.741 41.5934 24.0475 41.8282 24.4258C42.1151 24.8823 42.2586 25.3192 42.2456 25.7366C42.2325 26.154 42.0891 26.6235 41.8086 27.1322L45.2324 29.6365ZM39.7348 27.4322C40.25 27.1061 40.5109 26.7931 40.5239 26.5061C40.5304 26.2192 40.4391 25.9192 40.2435 25.6062C39.8 24.9018 39.2848 24.7323 38.7109 25.0975L38.3849 25.2997L39.7348 27.4322Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M50.09 27.6372L46.0728 29.3067L42.7207 21.2527L46.4901 19.6875L47.1358 21.2396L45.1011 22.0874L45.7858 23.7243L47.5336 22.9939L48.1596 24.4873L46.4119 25.2177L47.1553 26.9981L49.4444 26.046L50.103 27.6307L50.09 27.6372Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 10,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M53.7427 24.0179C53.9775 24.9635 53.971 25.6939 53.7101 26.2156C53.4493 26.7374 52.9145 27.096 52.0928 27.3047C51.8059 27.3765 51.558 27.4091 51.3559 27.4025C51.1537 27.396 50.8472 27.3373 50.4363 27.233L49.9668 25.3613C50.0124 25.3613 50.0907 25.3939 50.2081 25.4461C50.7037 25.6483 51.1081 25.7135 51.4211 25.6287C51.9102 25.5048 52.0602 25.0809 51.8776 24.3635L50.4168 18.5529L52.2493 18.0898L53.7427 24.0244V24.0179Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 14,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M59.9509 25.8962L55.6206 26.3266L54.7598 17.6465L58.8227 17.2422L58.9857 18.9182L56.788 19.1334L56.964 20.9007L58.8487 20.7116L59.0118 22.3224L57.1271 22.5116L57.3162 24.4289L59.7813 24.1811L59.9509 25.8897V25.8962Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M65.9634 26.0712C65.4743 26.2538 64.933 26.3255 64.3461 26.2864C63.2961 26.2212 62.4418 25.7777 61.7897 24.969C61.1375 24.1604 60.8506 23.0191 60.9484 21.5452C61.0462 20.0323 61.4636 18.9106 62.2136 18.1932C62.957 17.4758 63.7983 17.1432 64.7243 17.2019C65.2852 17.2345 65.8656 17.4171 66.4786 17.7497L66.3612 19.5757C65.8852 19.2953 65.5721 19.1323 65.4287 19.0866C65.2852 19.0475 65.1287 19.0214 64.9591 19.0084C64.4635 18.9758 64.02 19.1518 63.6222 19.5366C63.2244 19.9214 62.9961 20.6127 62.9309 21.617C62.8657 22.6604 62.9961 23.3973 63.3353 23.8212C63.6678 24.2451 64.1048 24.4734 64.6396 24.5125C65.0765 24.5386 65.5591 24.3951 66.0873 24.0821L65.9634 26.0712Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 22,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M73.0791 20.652L71.4292 20.2607L69.8184 27.1083L67.9793 26.6779L69.5836 19.8433L67.8945 19.4455L68.2858 17.7891L73.4639 19.0021L73.0791 20.6455V20.652Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M76.2035 29.4605L72.1797 27.7976L75.5122 19.7305L79.2816 21.2891L78.636 22.8477L76.5947 22.0065L75.9165 23.6434L77.6643 24.3673L77.0447 25.8672L75.297 25.1433L74.56 26.9237L76.8491 27.8693L76.1904 29.4605H76.2035Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 30,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M81.7515 22.5273L83.5123 23.6556C84.4905 24.2816 85.0709 25.0968 85.247 26.0946C85.4231 27.0924 85.1361 28.1815 84.3797 29.3554C83.6166 30.5423 82.7493 31.2792 81.7776 31.5531C80.8059 31.827 79.795 31.6314 78.7451 30.9596L77.043 29.8706L81.7515 22.5273ZM82.508 24.8816L79.5016 29.5706L79.6907 29.6945C80.0102 29.8966 80.4211 29.9814 80.9298 29.9423C81.4385 29.9032 82.0058 29.3945 82.6319 28.4163C83.0949 27.6924 83.3623 27.138 83.4275 26.7533C83.4927 26.3685 83.4536 26.0163 83.3036 25.7098C83.1536 25.4033 82.9449 25.162 82.6841 24.999L82.508 24.8881V24.8816Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M88.046 35.7092L86.2656 33.8767L87.2699 32.8984L89.0503 34.731L88.046 35.7092Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M33.3043 87.6324L31.5957 85.7282L32.6391 84.7891L34.3478 86.6933L33.3043 87.6324Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 39,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M40.2425 96.1423L38.6643 95.0467L39.8773 90.6382L39.8512 90.6252L37.3861 94.1729L35.834 93.0968L40.8099 85.9297L42.825 87.3253C43.2294 87.6057 43.575 87.9644 43.8619 88.4013C44.1489 88.8383 44.2598 89.2883 44.1945 89.7448C44.1293 90.2013 43.9663 90.6186 43.712 90.9838C43.4054 91.4273 43.0533 91.7273 42.6685 91.8773C42.2772 92.0273 41.7946 92.0795 41.2077 92.0142L40.2295 96.1423H40.2425ZM40.1382 90.2143C40.6403 90.5665 41.0251 90.6839 41.299 90.5795C41.5664 90.4752 41.8077 90.273 42.0164 89.9665C42.4924 89.2817 42.4468 88.747 41.8925 88.3557L41.5794 88.1405L40.1382 90.2143Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M46.2945 99.0322L42.3359 97.2257L45.9554 89.2891L49.6661 90.9847L48.9683 92.5172L46.9597 91.6042L46.2227 93.2215L47.9444 94.0041L47.2727 95.478L45.551 94.6889L44.7489 96.4431L47.0053 97.4735L46.2945 99.0387V99.0322Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 44,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M52.477 98.1645C52.2161 99.1036 51.8444 99.7297 51.3618 100.056C50.8792 100.375 50.2336 100.427 49.4184 100.199C49.1315 100.121 48.9032 100.023 48.7271 99.9188C48.5576 99.8079 48.3163 99.6123 48.0098 99.3188L48.525 97.4602C48.5641 97.4863 48.6163 97.5515 48.6945 97.6558C49.0206 98.0732 49.3402 98.3275 49.6597 98.4188C50.1423 98.5558 50.4879 98.2623 50.6836 97.545L52.2944 91.7734L54.1139 92.2821L52.4705 98.1776L52.477 98.1645Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 48,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M58.5933 101.673L54.2695 101.171L55.2804 92.5039L59.3302 92.98L59.1346 94.6495L56.9433 94.3951L56.7347 96.1559L58.6128 96.3777L58.4237 97.9885L56.5455 97.7668L56.3238 99.6841L58.7889 99.971L58.5868 101.68L58.5933 101.673Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 52,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M65.9764 101.314C65.5068 101.549 64.9851 101.686 64.3982 101.719C63.3482 101.777 62.4482 101.438 61.7048 100.708C60.9613 99.9774 60.544 98.8818 60.4657 97.4079C60.3874 95.895 60.6744 94.7341 61.3265 93.932C61.9852 93.1298 62.7743 92.7059 63.7069 92.6538C64.2677 92.6212 64.8677 92.7386 65.5133 92.9929L65.6112 94.8189C65.1025 94.5907 64.7764 94.4668 64.6264 94.4407C64.4764 94.4146 64.3199 94.4081 64.1503 94.4146C63.6547 94.4407 63.2373 94.6689 62.8852 95.0993C62.533 95.5298 62.3895 96.2406 62.4417 97.2449C62.5004 98.2883 62.7156 99.0057 63.1004 99.3905C63.4852 99.7752 63.9417 99.9513 64.4764 99.9187C64.9134 99.8926 65.3764 99.697 65.8655 99.3253L65.9698 101.314H65.9764Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M71.3506 93.1161L69.7007 93.5008L71.305 100.348L69.4659 100.779L67.8616 93.9443L66.1725 94.3421L65.7812 92.6857L70.9593 91.4727L71.3441 93.1161H71.3506Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 60,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M78.9885 97.533L74.9778 99.2286L71.5801 91.1941L75.3365 89.6094L75.9886 91.1615L73.9539 92.0223L74.6452 93.6592L76.3864 92.9223L77.019 94.4157L75.2778 95.1526L76.0277 96.9265L78.3103 95.9613L78.982 97.546L78.9885 97.533Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 64,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M76.3457 89.1197L78.0869 87.9589C79.0586 87.3133 80.0304 87.1111 81.0151 87.3524C81.9998 87.5937 82.8802 88.2915 83.6563 89.4588C84.4389 90.6327 84.7584 91.7218 84.6215 92.7261C84.4845 93.7304 83.8976 94.5782 82.8607 95.263L81.1781 96.3847L76.3457 89.1197ZM78.8043 89.3675L81.889 94.0043L82.0781 93.8804C82.3911 93.6717 82.6324 93.3261 82.802 92.8435C82.9715 92.3609 82.7368 91.637 82.0911 90.6718C81.6151 89.961 81.2173 89.4914 80.8847 89.2762C80.5586 89.061 80.2195 88.9567 79.8804 88.9697C79.5412 88.9827 79.2347 89.0806 78.9804 89.2501L78.8043 89.3675Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M90.1655 85.8636L88.4373 87.7483L87.4004 86.8027L89.1286 84.918L90.1655 85.8636Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M13.0235 41.7754C20.3406 22.4327 39.0313 8.6723 60.937 8.6723C82.8427 8.6723 101.533 22.4327 108.85 41.7754H116.976C117.381 37.7582 115.313 33.7996 111.589 31.9019C108.498 30.3302 106.451 27.2586 106.177 23.8022C105.792 18.9176 101.912 15.0308 97.0204 14.646C93.564 14.3721 90.4989 12.3243 88.9207 9.23315C86.6969 4.86375 81.6232 2.76382 76.9603 4.27681C73.6604 5.34634 70.0475 4.62897 67.4063 2.37906C63.676 -0.803435 58.1849 -0.803435 54.4611 2.37906C51.8264 4.62897 48.207 5.35286 44.9071 4.27681C40.2443 2.76382 35.1705 4.86375 32.9467 9.23315C31.375 12.3243 28.3034 14.3721 24.847 14.646C19.9624 15.0308 16.0756 18.9111 15.6908 23.8022C15.4169 27.2586 13.3692 30.3237 10.278 31.9019C6.56072 33.7996 4.4934 37.7582 4.89122 41.7754H13.017H13.0235ZM60.937 14.3721C79.6015 14.3721 95.6705 25.6673 102.688 41.7754H105.518C98.3443 24.1935 81.0688 11.7635 60.937 11.7635C40.8051 11.7635 23.5297 24.1935 16.356 41.7754H19.1863C26.2035 25.6673 42.2724 14.3721 60.937 14.3721ZM116.546 75.922C116.533 75.8763 116.52 75.8372 116.507 75.7916H109.626C102.935 96.2952 83.6709 111.112 60.937 111.112C38.203 111.112 18.932 96.2952 12.2475 75.7916H5.36729C5.35424 75.8372 5.3412 75.8763 5.32816 75.922C3.81517 80.5849 5.91509 85.6586 10.2845 87.8824C13.3757 89.4541 15.4234 92.5257 15.6973 95.9821C16.0821 100.867 19.9624 104.754 24.8535 105.138C28.3099 105.412 31.375 107.46 32.9532 110.551C35.1771 114.921 40.2508 117.02 44.9137 115.507C48.2135 114.438 51.8264 115.155 54.4676 117.405C58.1979 120.588 63.689 120.588 67.4128 117.405C70.0475 115.155 73.6669 114.431 76.9668 115.507C81.6297 117.02 86.7034 114.921 88.9273 110.551C90.4989 107.46 93.5706 105.412 97.027 105.138C101.912 104.754 105.798 100.873 106.183 95.9821C106.457 92.5257 108.505 89.4606 111.596 87.8824C115.965 85.6586 118.065 80.5849 116.552 75.922H116.546ZM60.937 108.021C81.9036 108.021 99.7725 94.5344 106.353 75.7916H103.581C97.1183 93.0735 80.4428 105.412 60.9305 105.412C41.4181 105.412 24.7427 93.0735 18.2799 75.7916H15.5082C22.0884 94.5409 39.9573 108.021 60.9305 108.021H60.937Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 73,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M5.70631 60.4234V72.0447H0V46.5195H8.65403C9.54095 46.5195 10.2974 46.6369 10.93 46.8782C11.5626 47.1195 12.0583 47.4325 12.4235 47.8238C12.7887 48.2151 13.0756 48.7303 13.2778 49.376C13.4865 50.0216 13.6234 50.6672 13.6886 51.3128C13.7538 51.9585 13.7864 52.741 13.7864 53.6606C13.7864 54.1432 13.7799 54.5345 13.7604 54.841C13.7473 55.1475 13.6886 55.5322 13.5843 55.9953C13.4799 56.4583 13.343 56.8496 13.1604 57.1561C12.9778 57.4691 12.7039 57.7691 12.3322 58.0691C11.9604 58.3691 11.517 58.5973 10.9952 58.7604C11.517 58.8582 11.9539 59.0343 12.293 59.2951C12.6321 59.556 12.88 59.8821 13.03 60.2669C13.1799 60.6516 13.2778 61.0038 13.3365 61.3299C13.3886 61.6559 13.4147 62.0211 13.4147 62.4255C13.4147 66.6449 13.3952 69.8535 13.3495 72.0382H7.83884V62.0864C7.83884 61.6885 7.79971 61.3755 7.72145 61.1407C7.6432 60.906 7.4932 60.7364 7.27147 60.6386C7.04974 60.5342 6.84105 60.4755 6.63889 60.456C6.43672 60.4364 6.12369 60.4234 5.70631 60.4234ZM5.73892 51.2411V56.0018H7.14756C7.89753 56.0018 8.26926 55.2192 8.26926 53.6606C8.26926 52.6237 8.11927 51.965 7.8258 51.6715C7.53233 51.3846 6.93887 51.2411 6.03891 51.2411H5.7324H5.73892Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M16.3027 72.0408V46.5156H27.5849V51.4654H22.0417V56.4479H27.3632V61.2412H22.0417V67.0583H27.9567V72.0408H16.3093H16.3027Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 81,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M36.3301 72.329C35.4301 72.329 34.5758 72.1986 33.7606 71.9443C32.9454 71.6899 32.2085 71.3182 31.5628 70.8356C30.9172 70.353 30.402 69.727 30.0172 68.9574C29.6325 68.1879 29.4434 67.3336 29.4434 66.3945V59.9838H35.0192V65.5793C35.0192 66.0358 35.1236 66.414 35.3257 66.7075C35.5279 67.001 35.8409 67.151 36.2714 67.151C37.1257 67.151 37.5561 66.4075 37.5561 64.9206C37.5561 64.8032 37.5561 64.6532 37.5496 64.4641C37.5496 64.275 37.5431 64.1511 37.5431 64.0859V46.5039H43.2298V65.638C43.2298 67.6923 42.5842 69.3161 41.2929 70.5161C40.0017 71.716 38.3517 72.316 36.3431 72.316L36.3301 72.329Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 85,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M46.2363 72.0408V46.5156H57.5185V51.4654H51.9752V56.4479H57.2968V61.2412H51.9752V67.0583H57.8902V72.0408H46.2429H46.2363Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 89,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M74.0049 61.0472V65.8731C74.0049 68.0969 73.3723 69.7273 72.1136 70.7707C70.855 71.8142 69.0877 72.3359 66.8247 72.3359C64.8161 72.3359 63.1792 71.762 61.9205 70.6142C60.6619 69.4664 60.0293 67.8361 60.0293 65.7166V53.8735C60.0293 51.3432 60.5901 49.4454 61.7053 48.1933C62.8205 46.9412 64.5683 46.3086 66.9551 46.3086C67.9268 46.3086 68.8268 46.426 69.6485 46.6673C70.4702 46.9086 71.2006 47.2607 71.8332 47.7303C72.4658 48.1998 72.9679 48.8194 73.3331 49.5824C73.6984 50.3454 73.881 51.2258 73.881 52.2171V56.7234H68.3051V53.7561C68.3051 52.2301 67.8551 51.4671 66.9616 51.4671C66.4269 51.4671 66.0747 51.6367 65.9052 51.9693C65.7356 52.3084 65.6508 52.7714 65.6508 53.3714V65.4361C65.6508 65.9579 65.7682 66.4144 66.0095 66.7991C66.2508 67.1839 66.5704 67.373 66.9616 67.373C67.3269 67.373 67.6399 67.2296 67.9073 66.9491C68.1746 66.6687 68.3051 66.16 68.3051 65.4361V61.0472H74.0114H74.0049Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 93,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M75.5514 46.5156H87.8901V51.4328H84.5381V72.0408H78.897V51.4328H75.5449V46.5156H75.5514Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 97,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M89.9434 72.0408V46.5156H101.226V51.4654H95.6823V56.4479H101.004V61.2412H95.6823V67.0583H101.597V72.0408H89.9499H89.9434Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 98,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M104.088 72.0408V46.5156H111.862C113.968 46.5156 115.501 47.0504 116.466 48.1264C117.431 49.2025 117.92 50.6698 117.933 52.5415L117.998 64.4628C117.998 67.0192 117.496 68.917 116.498 70.1691C115.501 71.4212 113.89 72.0473 111.666 72.0473H104.088V72.0408ZM109.762 51.035V67.3779C109.762 67.3779 109.859 67.3649 109.951 67.3649C110.016 67.3649 110.133 67.3649 110.309 67.3779C110.485 67.3909 110.629 67.3909 110.733 67.3909C111.809 67.3909 112.351 66.9344 112.351 66.0279V53.4349C112.351 52.5024 112.201 51.8698 111.907 51.5176C111.614 51.172 111.105 50.9959 110.375 50.9959C110.257 50.9959 110.049 51.0089 109.749 51.0285L109.762 51.035Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 102,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M77.0496 37.6406H43.2812V40.3536H77.0496V37.6406Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 106,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M77.0496 76.0898H43.2812V78.8028H77.0496V76.0898Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
                lineNumber: 107,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/RejectedIcon.tsx",
        lineNumber: 4,
        columnNumber: 5
    }, this);
};
_c = RejectedIcon;
const __TURBOPACK__default__export__ = RejectedIcon;
var _c;
__turbopack_context__.k.register(_c, "RejectedIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/ApprovedIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
const ApprovedIcon = (props)=>{
    const { className } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        viewBox: "0 0 118 114",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M35.8856 33.6345L34.303 35.4839L33.2852 34.615L34.8678 32.7656L35.8856 33.6345Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 5,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M44.7228 29.1052L43.3078 30.1478L42.3148 29.2666L40.5522 30.5636L41.0797 31.7863L39.6647 32.8289L36.6484 24.7361L38.1131 23.6562L44.7166 29.1052H44.7228ZM41.3156 28.3357L38.5724 25.847L38.5476 25.8656L40.0247 29.2914L41.3156 28.3419V28.3357Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M41.7305 21.3729L43.8282 20.3613C45.4542 19.5794 46.6458 19.9642 47.403 21.5281C47.7381 22.2294 47.7878 22.8686 47.5458 23.452C47.3037 24.0354 46.801 24.5194 46.0252 24.8918L45.5535 25.1214L46.9748 28.0693L45.3425 28.8575L41.7367 21.3792L41.7305 21.3729ZM44.9701 23.9485L45.2929 23.7933C45.5784 23.6568 45.7583 23.4582 45.8266 23.1975C45.8949 22.9369 45.8514 22.639 45.6901 22.3039C45.3363 21.5653 44.8708 21.3357 44.2999 21.6088L43.9275 21.7888L44.9701 23.9485Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 10,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M48.3031 18.4857L50.5436 17.8589C52.2875 17.3749 53.3922 17.9644 53.8577 19.6339C54.0687 20.3786 54.0004 21.024 53.6591 21.5578C53.3178 22.0915 52.7344 22.4763 51.9089 22.7059L51.4 22.8486L52.2813 26.0013L50.5311 26.4916L48.2969 18.492L48.3031 18.4857ZM51.0338 21.595L51.3814 21.4957C51.6917 21.4088 51.9027 21.2475 52.0144 20.9992C52.1261 20.7572 52.1324 20.4531 52.0331 20.0931C51.8096 19.305 51.4 18.9946 50.7856 19.1622L50.3884 19.2739L51.0338 21.5826V21.595Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 14,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M61.1871 24.5875L59.3686 24.7551L57.5564 20.7956H57.5316L57.9102 24.8916L56.1227 25.0592L55.3594 16.7926L57.6805 16.5816C58.146 16.5382 58.6177 16.5878 59.0956 16.7306C59.5734 16.8733 59.9272 17.134 60.163 17.5063C60.3927 17.8787 60.5292 18.2821 60.5727 18.7041C60.6223 19.2192 60.5478 19.6474 60.3554 19.995C60.163 20.3425 59.8403 20.6776 59.381 20.9879L61.1933 24.5937L61.1871 24.5875ZM57.4819 20.3363C58.0591 20.2805 58.4191 20.1377 58.5494 19.8957C58.6797 19.6536 58.7294 19.3619 58.6984 19.0082C58.6239 18.22 58.2639 17.8539 57.6185 17.9097L57.2585 17.9407L57.4757 20.3301L57.4819 20.3363Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M61.9892 20.4085C62.1009 19.2479 62.3057 18.4101 62.6098 17.8888C62.9139 17.3675 63.2863 16.9827 63.7331 16.722C64.18 16.4614 64.7199 16.3621 65.353 16.4242C65.8805 16.4738 66.2963 16.5979 66.6004 16.8027C66.9045 17.0075 67.1838 17.2868 67.4259 17.6468C67.6679 18.0067 67.8355 18.4349 67.9162 18.9438C67.9968 19.4465 68.0031 20.1044 67.9224 20.9174C67.7672 22.5806 67.3824 23.7039 66.768 24.2811C66.1536 24.8645 65.4088 25.1127 64.5275 25.0258C63.6338 24.9389 62.9449 24.5479 62.4733 23.8405C62.0016 23.133 61.8402 21.991 61.9892 20.4085ZM63.9255 20.0796L63.8759 20.6071C63.7952 21.4759 63.7704 22.0531 63.8014 22.3324C63.8324 22.6116 63.9317 22.8413 64.1055 23.0212C64.2793 23.195 64.4965 23.3005 64.7572 23.3253C64.9682 23.344 65.173 23.2881 65.3592 23.1516C65.5516 23.015 65.6943 22.7916 65.7936 22.4813C65.8929 22.171 65.9798 21.6125 66.0605 20.8057C66.1288 20.0795 66.1474 19.5644 66.1163 19.2541C66.0853 18.9438 65.986 18.6832 65.8246 18.4784C65.6633 18.2736 65.4523 18.1557 65.1916 18.1308C64.813 18.0936 64.5213 18.2363 64.3227 18.5466C64.1241 18.8569 63.9938 19.3721 63.9255 20.0858V20.0796Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 22,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M74.4818 18.5367L76.2133 19.0519L71.5958 26.3378L69.9512 25.8475L70.0505 17.2148L71.8689 17.7548L71.6207 23.2596L74.4818 18.5305V18.5367Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M78.0756 29.2414L74.3828 27.3671L78.1376 19.957L81.6007 21.7134L80.8746 23.1408L79.0003 22.1912L78.2369 23.6993L79.8443 24.5123L79.1492 25.8901L77.5418 25.0771L76.7102 26.7155L78.8141 27.7829L78.0756 29.2414Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 30,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M83.846 23.0648L85.4348 24.2626C86.3223 24.9328 86.8064 25.7458 86.9057 26.7016C86.9988 27.6635 86.645 28.6751 85.8444 29.7364C85.0376 30.81 84.1563 31.443 83.213 31.6292C82.2696 31.8216 81.3263 31.561 80.3767 30.8473L78.8438 29.6867L83.846 23.0586V23.0648ZM84.3984 25.3549L81.2083 29.5812L81.3759 29.7115C81.6614 29.9288 82.0462 30.0343 82.5365 30.0343C83.0206 30.0343 83.5978 29.5936 84.2618 28.7124C84.7521 28.0607 85.0438 27.5518 85.1369 27.1919C85.23 26.8319 85.2176 26.4968 85.0935 26.1927C84.9693 25.8886 84.7956 25.6465 84.5535 25.4728L84.3922 25.3549H84.3984Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M88.8625 36.0292L87.3047 34.1611L88.3287 33.3047L89.8865 35.1727L88.8625 36.0292Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M33.1096 81.1923L31.6387 79.256L32.6999 78.4492L34.1708 80.3855L33.1096 81.1923Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 39,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M39.5344 90.173L38.169 89.0683L38.771 87.8891L37.0705 86.5114L36.0216 87.3306L34.6562 86.2259L41.7314 81.2734L43.1464 82.4216L39.5344 90.1792V90.173ZM39.4165 86.6852L41.1356 83.4021L41.117 83.3835L38.1752 85.6736L39.4227 86.6852H39.4165Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M45.293 83.9531L47.3535 85.0392C48.9547 85.8832 49.3457 87.0686 48.5389 88.6015C48.1789 89.2904 47.6886 89.7062 47.0742 89.8613C46.4598 90.0165 45.7771 89.8924 45.0137 89.4952L44.5482 89.2469L43.0215 92.139L41.4141 91.295L45.2868 83.9531H45.293ZM45.144 88.0864L45.4606 88.254C45.7398 88.4029 46.0067 88.4277 46.2612 88.3284C46.5094 88.2291 46.7204 88.0181 46.8942 87.6892C47.279 86.9631 47.1859 86.4542 46.6273 86.1563L46.2612 85.9639L45.144 88.0864Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 44,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M50.3203 86.4671L52.5421 87.1498C54.2675 87.6835 54.8757 88.7758 54.3668 90.4328C54.1371 91.1714 53.7337 91.6803 53.1628 91.9409C52.5918 92.2078 51.8905 92.214 51.0712 91.9595L50.5685 91.8044L49.6066 94.9323L47.875 94.3985L50.3203 86.4609V86.4671ZM50.9409 90.5632L51.2822 90.6687C51.5864 90.7618 51.8532 90.7369 52.0829 90.5942C52.3125 90.4515 52.4801 90.2032 52.5856 89.8433C52.8276 89.0613 52.6476 88.5772 52.0394 88.3848L51.6484 88.2607L50.9409 90.557V90.5632Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 48,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M59.953 96.8315L58.1346 96.6329L57.1478 92.3941H57.1229L56.6761 96.4778L54.8887 96.2854L55.7886 88.0312L58.1097 88.2857C58.5752 88.3353 59.0221 88.4781 59.4627 88.7139C59.9033 88.9497 60.2012 89.2725 60.3502 89.6883C60.5054 90.1041 60.555 90.5199 60.5116 90.9419C60.4557 91.4508 60.3005 91.8604 60.0461 92.1645C59.7916 92.4686 59.4068 92.7293 58.8917 92.9465L59.953 96.8377V96.8315ZM57.1664 91.9287C57.7436 91.9907 58.1221 91.9163 58.3021 91.7053C58.4821 91.4943 58.5876 91.215 58.6249 90.8674C58.7117 90.0793 58.4325 89.651 57.787 89.5766L57.427 89.5393L57.1664 91.9225V91.9287Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 52,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M61.5162 92.6793C61.4169 91.5187 61.4666 90.6561 61.6714 90.0913C61.8762 89.5266 62.1741 89.0735 62.5651 88.7384C62.9561 88.4033 63.4712 88.2047 64.1042 88.1488C64.6318 88.1054 65.06 88.1488 65.4013 88.2978C65.7365 88.4467 66.0592 88.6701 66.3633 88.9804C66.6674 89.2908 66.9033 89.6879 67.077 90.1658C67.2446 90.6499 67.3687 91.2953 67.437 92.1083C67.5798 93.7716 67.406 94.9445 66.9033 95.6272C66.4006 96.3099 65.7179 96.6884 64.8366 96.7629C63.9429 96.8374 63.1981 96.5767 62.6085 95.9623C62.0189 95.3541 61.6528 94.2556 61.5162 92.6731V92.6793ZM63.3595 92.009L63.4029 92.5365C63.4774 93.4054 63.5581 93.9764 63.6388 94.2494C63.7194 94.5225 63.8622 94.7273 64.0608 94.87C64.2594 95.0128 64.4952 95.0748 64.7559 95.05C64.9669 95.0314 65.1531 94.9383 65.3207 94.7707C65.482 94.6032 65.5875 94.3549 65.631 94.0322C65.6744 93.7095 65.662 93.1447 65.5937 92.3379C65.5317 91.6118 65.4572 91.1029 65.3703 90.7988C65.2834 90.4947 65.1407 90.2589 64.9421 90.0851C64.7435 89.9114 64.5138 89.8369 64.2532 89.8555C63.8746 89.8865 63.6139 90.0789 63.4774 90.4203C63.3409 90.7616 63.2974 91.2953 63.3595 92.009Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M71.6012 86.7244L73.3328 86.2031L73.4879 94.8296L71.8432 95.3261L67.1699 88.065L68.9822 87.5188L71.806 92.2541L71.5888 86.7306L71.6012 86.7244Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 60,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M81.3029 91.3089L77.6288 93.2266L73.7871 85.8662L77.2254 84.0664L77.9701 85.4876L76.1083 86.462L76.8902 87.9576L78.4853 87.126L79.199 88.4914L77.604 89.323L78.4542 90.949L80.5457 89.8567L81.3029 91.3089Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 64,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M78.1488 83.5253L79.7066 82.2965C80.5754 81.6076 81.4878 81.3408 82.4373 81.4959C83.3869 81.6449 84.2806 82.2468 85.0998 83.2895C85.9315 84.3445 86.3225 85.3499 86.2666 86.3118C86.2107 87.2738 85.7204 88.124 84.7957 88.8563L83.2876 90.0479L78.1426 83.5315L78.1488 83.5253ZM80.501 83.5749L83.7841 87.733L83.9517 87.6027C84.2309 87.3793 84.4357 87.0318 84.5599 86.5663C84.684 86.0946 84.4047 85.4306 83.7158 84.5617C83.2131 83.9225 82.7973 83.5129 82.4684 83.3267C82.1394 83.1467 81.8167 83.0722 81.494 83.1157C81.1712 83.1591 80.892 83.2708 80.6561 83.4508L80.501 83.5749Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M91.002 79.3856L89.5062 81.3095L88.4512 80.4903L89.9469 78.5664L91.002 79.3856Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M15.7647 39.7547C22.7281 21.3474 40.5153 8.25252 61.3622 8.25252C82.2091 8.25252 99.9963 21.3474 106.96 39.7547H114.693C115.078 35.9318 113.11 32.1646 109.566 30.3587C106.625 28.863 104.676 25.9399 104.415 22.6507C104.049 18.0023 100.356 14.3035 95.7016 13.9373C92.4122 13.6767 89.4953 11.7279 87.9934 8.78624C85.877 4.62815 81.0485 2.62978 76.611 4.0696C73.4707 5.0874 70.0324 4.40473 67.5188 2.26362C63.9689 -0.764958 58.7432 -0.764958 55.1994 2.26362C52.692 4.40473 49.2476 5.09361 46.1072 4.0696C41.6697 2.62978 36.8412 4.62815 34.7249 8.78624C33.2292 11.7279 30.306 13.6767 27.0167 13.9373C22.3682 14.3035 18.6692 17.9961 18.3031 22.6507C18.0424 25.9399 16.0936 28.8568 13.1518 30.3587C9.61426 32.1646 7.64687 35.9318 8.02545 39.7547H15.7585H15.7647ZM61.3622 13.6767C79.1246 13.6767 94.4169 24.4256 101.095 39.7547H103.788C96.9614 23.0231 80.521 11.1942 61.3622 11.1942C42.2034 11.1942 25.763 23.0231 18.9361 39.7547H21.6296C28.3076 24.4256 43.5998 13.6767 61.3622 13.6767ZM114.283 72.2499C114.271 72.2065 114.258 72.1692 114.246 72.1258H107.698C101.331 91.6378 82.9973 105.738 61.3622 105.738C39.7271 105.738 21.3876 91.6378 15.0261 72.1258H8.47851C8.4661 72.1692 8.45369 72.2065 8.44127 72.2499C7.00142 76.6873 8.99984 81.5156 13.158 83.6319C16.0998 85.1276 18.0486 88.0507 18.3093 91.3399C18.6754 95.9883 22.3682 99.6871 27.0229 100.053C30.3122 100.314 33.2292 102.263 34.7311 105.204C36.8474 109.362 41.6759 111.361 46.1134 109.921C49.2538 108.903 52.692 109.586 55.2056 111.727C58.7556 114.756 63.9813 114.756 67.5251 111.727C70.0324 109.586 73.4769 108.897 76.6172 109.921C81.0547 111.361 85.8832 109.362 87.9996 105.204C89.4953 102.263 92.4184 100.314 95.7078 100.053C100.356 99.6871 104.055 95.9945 104.421 91.3399C104.682 88.0507 106.631 85.1338 109.573 83.6319C113.731 81.5156 115.729 76.6873 114.289 72.2499H114.283ZM61.3622 102.796C81.3154 102.796 98.3206 89.9621 104.583 72.1258H101.945C95.7947 88.572 79.9252 100.314 61.356 100.314C42.7868 100.314 26.9174 88.572 20.7669 72.1258H18.1293C24.3914 89.9683 41.3966 102.796 61.356 102.796H61.3622Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 73,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M11.1465 44.2656L13.8276 68.5563H8.64535L8.26676 64.8947H5.55462L5.21948 68.5563H0L2.74318 44.2656H11.1465ZM7.13722 48.8209H6.64693L5.9332 60.4698H7.85715L7.13722 48.8209Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M15.7891 68.5563V44.2656H22.8828C23.82 44.2656 24.6392 44.3897 25.3405 44.6318C26.0418 44.8738 26.6625 45.271 27.2024 45.8234C27.7424 46.3695 28.152 47.1328 28.4313 48.101C28.7105 49.0692 28.8533 50.2483 28.8533 51.6323C28.8533 52.8238 28.7664 53.8416 28.5988 54.6919C28.4313 55.5421 28.1458 56.2931 27.7362 56.9571C27.3265 57.6212 26.7556 58.1115 26.0046 58.4466C25.2598 58.7755 24.3413 58.9431 23.249 58.9431H21.0706V68.5687H15.7953L15.7891 68.5563ZM21.0706 48.6844V54.5554H21.3747C21.6602 54.5554 21.8774 54.5491 22.0202 54.5305C22.1691 54.5181 22.3429 54.4809 22.5415 54.425C22.7401 54.3692 22.889 54.2761 22.9821 54.152C23.0814 54.0278 23.1745 53.8541 23.28 53.6306C23.3793 53.4134 23.4476 53.1279 23.4848 52.7866C23.5221 52.4391 23.5407 52.0233 23.5407 51.533C23.5407 50.4655 23.398 49.7208 23.1187 49.305C22.8394 48.8892 22.3553 48.6782 21.6602 48.6782H21.0644L21.0706 48.6844Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 81,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M30.7461 68.5563V44.2656H37.8399C38.777 44.2656 39.5962 44.3897 40.2976 44.6318C40.9989 44.8738 41.6195 45.271 42.1594 45.8234C42.6994 46.3695 43.109 47.1328 43.3883 48.101C43.6676 49.0692 43.8103 50.2483 43.8103 51.6323C43.8103 52.8238 43.7234 53.8416 43.5559 54.6919C43.3883 55.5421 43.1028 56.2931 42.6932 56.9571C42.2836 57.6212 41.7064 58.1115 40.9616 58.4466C40.2169 58.7755 39.2983 58.9431 38.206 58.9431H36.0276V68.5687H30.7523L30.7461 68.5563ZM36.0214 48.6844V54.5554H36.3255C36.611 54.5554 36.8282 54.5491 36.971 54.5305C37.1199 54.5181 37.2937 54.4809 37.4923 54.425C37.6909 54.3692 37.8399 54.2761 37.933 54.152C38.0323 54.0278 38.1254 53.8541 38.2309 53.6306C38.3302 53.4134 38.3984 53.1279 38.4357 52.7866C38.4729 52.4391 38.4915 52.0233 38.4915 51.533C38.4915 50.4655 38.3488 49.7208 38.0695 49.305C37.7902 48.8892 37.3061 48.6782 36.611 48.6782H36.0152L36.0214 48.6844Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 85,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M51.1278 57.5049V68.5641H45.6973V44.2734H53.933C54.7771 44.2734 55.497 44.3851 56.099 44.6148C56.701 44.8444 57.1727 45.1423 57.5202 45.5147C57.8678 45.887 58.1409 46.3773 58.3333 46.9917C58.5319 47.6061 58.6622 48.2205 58.7243 48.8349C58.7863 49.4493 58.8173 50.1941 58.8173 51.0691C58.8173 51.5284 58.8111 51.9007 58.7925 52.1924C58.7801 52.4841 58.7242 52.8503 58.6249 53.2909C58.5256 53.7315 58.3953 54.1039 58.2215 54.3956C58.0478 54.6935 57.7871 54.979 57.4333 55.2645C57.0796 55.5499 56.6576 55.7672 56.1611 55.9223C56.6576 56.0154 57.0734 56.183 57.3961 56.4312C57.7188 56.6794 57.9547 56.9898 58.0974 57.3559C58.2402 57.7221 58.3333 58.0572 58.3891 58.3675C58.4388 58.6778 58.4636 59.0254 58.4636 59.4101C58.4636 63.4255 58.445 66.4789 58.4015 68.5579H53.1572V59.0874C53.1572 58.7088 53.12 58.411 53.0455 58.1875C52.971 57.9641 52.8283 57.8028 52.6173 57.7097C52.4063 57.6104 52.2077 57.5545 52.0153 57.5359C51.8229 57.5173 51.525 57.5049 51.1278 57.5049ZM51.1588 48.7667V53.2971H52.4993C53.2131 53.2971 53.5668 52.5524 53.5668 51.0691C53.5668 50.0824 53.4241 49.4555 53.1448 49.1763C52.8655 48.9032 52.3007 48.7667 51.4443 48.7667H51.1526H51.1588Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 89,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M60.8965 61.9993V50.4249C60.8965 48.4017 61.4426 46.8316 62.5349 45.7207C63.6272 44.6098 65.2719 44.0326 67.4689 44.0078C69.728 44.0388 71.4037 44.6098 72.496 45.7207C73.5883 46.8316 74.1345 48.4017 74.1345 50.4249V61.9993C74.1345 62.9861 74.0104 63.8798 73.7621 64.6928C73.5138 65.4996 73.1353 66.2195 72.6263 66.8525C72.1174 67.4855 71.4285 67.9696 70.5597 68.3171C69.6908 68.6647 68.6916 68.8198 67.562 68.7888C66.3704 68.8198 65.3402 68.6647 64.4713 68.3171C63.6024 67.9696 62.9135 67.4855 62.4046 66.8525C61.8957 66.2195 61.5171 65.5058 61.2689 64.6928C61.0206 63.886 60.8965 62.9861 60.8965 61.9993ZM67.5124 64.0287C67.7048 64.0287 67.8785 63.9915 68.0213 63.917C68.1702 63.8425 68.2882 63.7246 68.3688 63.5633C68.4557 63.4081 68.524 63.253 68.5736 63.1164C68.6233 62.9737 68.6605 62.7813 68.6792 62.5455C68.6978 62.3096 68.7102 62.1234 68.7164 61.9993C68.7164 61.8752 68.7226 61.6952 68.7226 61.4718V51.5917C68.7226 51.5296 68.7226 51.4117 68.7288 51.2255C68.7288 51.0455 68.7288 50.9028 68.7288 50.8159C68.7288 50.7228 68.7164 50.5925 68.7164 50.4125C68.7164 50.2325 68.6978 50.096 68.673 50.0029C68.6481 49.9036 68.6171 49.7857 68.5799 49.6368C68.5426 49.4878 68.493 49.3761 68.4371 49.3016C68.375 49.2271 68.3068 49.1465 68.2261 49.0596C68.1454 48.9727 68.0461 48.9106 67.922 48.8796C67.7979 48.8424 67.6613 48.8237 67.5124 48.8237C67.3014 48.8237 67.1152 48.861 66.9538 48.9293C66.7987 48.9975 66.6745 49.1154 66.5814 49.2706C66.4883 49.4257 66.4139 49.5685 66.3518 49.6988C66.2897 49.8291 66.2525 50.0153 66.2277 50.2636C66.2091 50.5118 66.1966 50.6918 66.1966 50.7973V61.298C66.1966 61.5214 66.1966 61.7014 66.2029 61.8318C66.2029 61.9621 66.2215 62.1545 66.2401 62.4089C66.2587 62.6634 66.2959 62.8682 66.3518 63.0295C66.4077 63.1847 66.4821 63.3523 66.5752 63.526C66.6683 63.6998 66.7925 63.8301 66.9476 63.917C67.1028 64.0039 67.2889 64.0473 67.5062 64.0473L67.5124 64.0287Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 93,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M86.2169 68.5563H78.8997L75.5918 44.2656H81.0037L82.5118 60.3705L84.113 44.2656H89.5249L86.2169 68.5563Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 97,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M91.3066 68.5563V44.2656H102.044V48.9761H96.7682V53.7175H101.832V58.279H96.7682V63.8149H102.397V68.5563H91.3129H91.3066Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 98,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M104.762 68.5563V44.2656H112.16C114.164 44.2656 115.623 44.7745 116.541 45.7985C117.46 46.8225 117.925 48.2189 117.938 50.0001L118 61.3448C118 63.7776 117.522 65.5836 116.572 66.7752C115.623 67.9667 114.09 68.5625 111.973 68.5625H104.762V68.5563ZM110.161 48.5665V64.119C110.161 64.119 110.254 64.1066 110.341 64.1066C110.403 64.1066 110.515 64.1066 110.683 64.119C110.85 64.1314 110.987 64.1314 111.086 64.1314C112.11 64.1314 112.625 63.6969 112.625 62.8343V50.8503C112.625 49.9628 112.482 49.3608 112.203 49.0257C111.924 48.6968 111.44 48.5292 110.745 48.5292C110.633 48.5292 110.434 48.5416 110.149 48.5603L110.161 48.5665Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 102,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M76.6967 35.8203H44.5605V38.4021H76.6967V35.8203Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 106,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M76.6967 72.4102H44.5605V74.9919H76.6967V72.4102Z",
                fill: "#007733"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
                lineNumber: 107,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/ApprovedIcon.tsx",
        lineNumber: 4,
        columnNumber: 5
    }, this);
};
_c = ApprovedIcon;
const __TURBOPACK__default__export__ = ApprovedIcon;
var _c;
__turbopack_context__.k.register(_c, "ApprovedIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/DeleteIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function DeleteIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "25",
        height: "28",
        viewBox: "0 0 25 28",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M12.3291 27.3361C10.5225 27.3361 8.76112 27.3161 7.01846 27.2801C4.78912 27.2361 3.24646 25.7908 2.99446 23.5081C2.57446 19.7215 1.85579 10.7961 1.84912 10.7068C1.80379 10.1561 2.21446 9.67347 2.76512 9.62947C3.30779 9.6148 3.79846 9.99614 3.84246 10.5455C3.84912 10.6361 4.56646 19.5308 4.98246 23.2881C5.12512 24.5855 5.82512 25.2548 7.05979 25.2801C10.3931 25.3508 13.7945 25.3548 17.4611 25.2881C18.7731 25.2628 19.4825 24.6068 19.6291 23.2788C20.0425 19.5535 20.7625 10.6361 20.7705 10.5455C20.8145 9.99614 21.3011 9.61214 21.8465 9.62947C22.3971 9.6748 22.8078 10.1561 22.7638 10.7068C22.7558 10.7975 22.0331 19.7455 21.6171 23.4988C21.3585 25.8281 19.8198 27.2455 17.4971 27.2881C15.7198 27.3188 14.0051 27.3361 12.3291 27.3361Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M23.6107 7.32031H1C0.448 7.32031 0 6.87231 0 6.32031C0 5.76831 0.448 5.32031 1 5.32031H23.6107C24.1627 5.32031 24.6107 5.76831 24.6107 6.32031C24.6107 6.87231 24.1627 7.32031 23.6107 7.32031Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M19.2538 7.31997C17.7364 7.31997 16.4191 6.23864 16.1204 4.75064L15.7964 3.1293C15.7284 2.88264 15.4471 2.66797 15.1271 2.66797H9.48311C9.16311 2.66797 8.88178 2.88264 8.80045 3.19064L8.48978 4.75064C8.19245 6.23864 6.87378 7.31997 5.35645 7.31997C4.80445 7.31997 4.35645 6.87197 4.35645 6.31997C4.35645 5.76797 4.80445 5.31997 5.35645 5.31997C5.92445 5.31997 6.41778 4.91464 6.52978 4.3573L6.85378 2.73597C7.18311 1.4933 8.25911 0.667969 9.48311 0.667969H15.1271C16.3511 0.667969 17.4271 1.4933 17.7431 2.67597L18.0818 4.3573C18.1924 4.91464 18.6858 5.31997 19.2538 5.31997C19.8058 5.31997 20.2538 5.76797 20.2538 6.31997C20.2538 6.87197 19.8058 7.31997 19.2538 7.31997Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = DeleteIcon;
const __TURBOPACK__default__export__ = DeleteIcon;
var _c;
__turbopack_context__.k.register(_c, "DeleteIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$LetterFoldIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/LetterFoldIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$AiMarkIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/AiMarkIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$EditIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/EditIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textbox$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Textbox.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$interviewServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/interviewServices.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/interviewSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$RejectedIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/RejectedIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ApprovedIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ApprovedIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DeleteIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/DeleteIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const InterviewFeedback = ({ params })=>{
    _s();
    const paramsPromise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["use"])(params);
    const interviewId = paramsPromise?.interviewId ? Number(paramsPromise.interviewId) : 0;
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const initialFetchDone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [processingAdvance, setProcessingAdvance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [processingReject, setProcessingReject] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [careerFeedbackData, setCareerFeedbackData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [roleSpecificSkillsFeedback, setRoleSpecificSkillsFeedback] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [cultureSpecificSkillsFeedback, setCultureSpecificSkillsFeedback] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [feedbackData, setFeedbackData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [originalCareerFeedbackData, setOriginalCareerFeedbackData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [originalRoleSpecificSkillsFeedback, setOriginalRoleSpecificSkillsFeedback] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [originalCultureSpecificSkillsFeedback, setOriginalCultureSpecificSkillsFeedback] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [originalBehavioralNotes, setOriginalBehavioralNotes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [editCareerBasedSkills, setEditCareerBasedSkills] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [editBehavioralNotes, setEditBehavioralNotes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const interviewStaticInformation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "InterviewFeedback.useSelector[interviewStaticInformation]": (state)=>state.interview.interviewStaticInformation
    }["InterviewFeedback.useSelector[interviewStaticInformation]"]);
    const [editMode, setEditMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isEdit: false,
        skillId: null
    });
    // Career feedback form
    const { control: careerControl, setValue: careerSetValue, handleSubmit: careerHandleSubmit } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])();
    // Role and culture specific skills form
    const { control: skillsControl, setValue: skillsSetValue, handleSubmit: skillsHandleSubmit } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])();
    const getInterviewStaticInformation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "InterviewFeedback.useCallback[getInterviewStaticInformation]": async ()=>{
            try {
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$interviewServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["conductInterviewStaticInformation"])();
                if (response?.data?.success) {
                    dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setInterviewStaticInformation"])(response?.data?.data));
                } else {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message));
                }
            } catch  {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
            }
        }
    }["InterviewFeedback.useCallback[getInterviewStaticInformation]"], [
        dispatch,
        t
    ]);
    const aiDecisionForNextRound = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "InterviewFeedback.useMemo[aiDecisionForNextRound]": ()=>{
            return !feedbackData?.aiDecisionForNextRound ? null : feedbackData?.aiDecisionForNextRound;
        }
    }["InterviewFeedback.useMemo[aiDecisionForNextRound]"], [
        feedbackData
    ]);
    console.log("aiDecisionForNextRound===>>>", aiDecisionForNextRound);
    const fetchInterviewFeedback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "InterviewFeedback.useCallback[fetchInterviewFeedback]": async ()=>{
            if (!interviewId) return;
            try {
                setLoading(true);
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$interviewServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInterviewFeedback"])(interviewId);
                if (response.data && response.data.success) {
                    const careerSkills = response.data.data.careerBasedSkills;
                    // Make sure role skills have the type field set properly
                    const roleSkills = (response.data.data.roleSpecificSkills || []).map({
                        "InterviewFeedback.useCallback[fetchInterviewFeedback].roleSkills": (skill)=>({
                                ...skill,
                                type: "role"
                            })
                    }["InterviewFeedback.useCallback[fetchInterviewFeedback].roleSkills"]);
                    // Make sure culture skills have the type field set properly
                    const cultureSkills = (response.data.data.cultureSpecificSkills || []).map({
                        "InterviewFeedback.useCallback[fetchInterviewFeedback].cultureSkills": (skill)=>({
                                ...skill,
                                type: "culture"
                            })
                    }["InterviewFeedback.useCallback[fetchInterviewFeedback].cultureSkills"]);
                    // Set feedback data for different skill types
                    setCareerFeedbackData(careerSkills);
                    setRoleSpecificSkillsFeedback(roleSkills);
                    setCultureSpecificSkillsFeedback(cultureSkills);
                    setFeedbackData(response.data.data);
                    // Show toast message if feedback is already filled
                    if (response.data.data.isFeedbackFilled) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])("Feedback has already been submitted for this candidate.");
                    }
                    // Pre-populate form data with existing feedback
                    if (response.data.data && response.data.data.applicantBehavioralNotes) {
                        careerSetValue("behavioralNotes", response.data.data.applicantBehavioralNotes);
                    }
                } else {
                    const errorMessage = response.data?.message || "Failed to fetch interview feedback";
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(errorMessage);
                }
            } catch (error) {
                console.error("Error fetching interview feedback:", error);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])("Something went wrong while loading interview feedback");
            } finally{
                setLoading(false);
            }
        }
    }["InterviewFeedback.useCallback[fetchInterviewFeedback]"], [
        interviewId,
        careerSetValue
    ]);
    // Fetch interview feedback data when component mounts or interviewId changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InterviewFeedback.useEffect": ()=>{
            if (initialFetchDone.current) return;
            if (interviewId) {
                fetchInterviewFeedback();
            }
            initialFetchDone.current = true;
        }
    }["InterviewFeedback.useEffect"], [
        interviewId,
        fetchInterviewFeedback
    ]);
    // Fetch static information when component mounts or interviewId changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InterviewFeedback.useEffect": ()=>{
            if (!interviewStaticInformation || !interviewStaticInformation.stratumDescription) {
                getInterviewStaticInformation();
            }
        }
    }["InterviewFeedback.useEffect"], [
        getInterviewStaticInformation
    ]);
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "common-page-header",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "common-page-head-section",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "main-heading",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    children: [
                                        "Interview ",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "Feedback"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                            lineNumber: 185,
                                            columnNumber: 27
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 184,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 183,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 182,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "section-heading mt-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    height: 24,
                                    width: 200,
                                    borderRadius: 6
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 190,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    height: 16,
                                    width: "50%",
                                    borderRadius: 6,
                                    className: "mt-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 192,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 189,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                    lineNumber: 181,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "inner-section",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "row",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "col-md-8",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "interview-summary",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "summary-header",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    height: 24,
                                                    width: 250,
                                                    borderRadius: 6
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 201,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 200,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                height: 300,
                                                width: "100%",
                                                borderRadius: 12,
                                                className: "mb-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 203,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 199,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "interview-summary",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "summary-header",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    height: 24,
                                                    width: 250,
                                                    borderRadius: 6
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 207,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 206,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                height: 300,
                                                width: "100%",
                                                borderRadius: 12,
                                                className: "mb-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 209,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 205,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 197,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "col-md-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    height: 550,
                                    width: "100%",
                                    borderRadius: 0,
                                    className: "mb-3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 214,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 212,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 196,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                    lineNumber: 195,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
            lineNumber: 180,
            columnNumber: 7
        }, this);
    } else if (!careerFeedbackData) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "common-page-header",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "common-page-head-section",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "main-heading",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                children: [
                                    "Interview ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Feedback"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 227,
                                        columnNumber: 27
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 226,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 225,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 224,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-5",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            children: "No feedback data available."
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 232,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 231,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                lineNumber: 223,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
            lineNumber: 222,
            columnNumber: 7
        }, this);
    }
    const enableEditMode = (skillId)=>{
        // Store the original states before entering edit mode
        setOriginalRoleSpecificSkillsFeedback(JSON.parse(JSON.stringify(roleSpecificSkillsFeedback)));
        setOriginalCultureSpecificSkillsFeedback(JSON.parse(JSON.stringify(cultureSpecificSkillsFeedback)));
        setEditMode({
            isEdit: true,
            skillId
        });
        // Find and pre-populate the form fields for the selected skill
        const skill = [
            ...roleSpecificSkillsFeedback,
            ...cultureSpecificSkillsFeedback
        ].find((s)=>s.id === skillId);
        if (skill && skill.highlights && skill.highlights.overAllFeedback) {
            skill.highlights.overAllFeedback.forEach((feedback, index)=>{
                skillsSetValue(`skill-${skillId}-${index}`, feedback);
            });
        }
    };
    // Add a new feedback point for a specific skill
    const addSkillFeedbackPoint = (skill)=>{
        if (skill && skill.highlights) {
            // Create a deep copy of the current skill lists to ensure React detects the changes
            const roleSkillsCopy = JSON.parse(JSON.stringify(roleSpecificSkillsFeedback));
            const cultureSkillsCopy = JSON.parse(JSON.stringify(cultureSpecificSkillsFeedback));
            // Choose the correct list based on skill type
            const workingList = skill.type === "role" ? roleSkillsCopy : cultureSkillsCopy;
            const skillIndex = workingList.findIndex((s)=>s.id === skill.id);
            if (skillIndex !== -1) {
                // Ensure overAllFeedback is initialized if it doesn't exist
                if (!workingList[skillIndex].highlights.overAllFeedback) {
                    workingList[skillIndex].highlights.overAllFeedback = [];
                }
                // Add an empty string as a new feedback point
                workingList[skillIndex].highlights.overAllFeedback.push("");
                // Set the empty value for the new field in the form
                const newIndex = workingList[skillIndex].highlights.overAllFeedback.length - 1;
                skillsSetValue(`skill-${skill.id}-${newIndex}`, "");
                // Update the appropriate state with the modified list
                if (skill.type === "role") {
                    setRoleSpecificSkillsFeedback(roleSkillsCopy);
                } else {
                    setCultureSpecificSkillsFeedback(cultureSkillsCopy);
                }
            }
        }
    };
    // Remove a feedback point for a specific skill
    const removeSkillFeedbackPoint = (skill, indexToRemove)=>{
        if (skill && skill.highlights) {
            const updatedSkillsList = skill.type === "role" ? [
                ...roleSpecificSkillsFeedback
            ] : [
                ...cultureSpecificSkillsFeedback
            ];
            const skillIndex = updatedSkillsList.findIndex((s)=>s.id === skill.id);
            if (skillIndex !== -1) {
                // Filter out the removed feedback point
                const updatedFeedbackPoints = updatedSkillsList[skillIndex].highlights.overAllFeedback.filter((_, index)=>index !== indexToRemove);
                // Create updated skill object
                const updatedSkill = {
                    ...updatedSkillsList[skillIndex],
                    highlights: {
                        ...updatedSkillsList[skillIndex].highlights,
                        overAllFeedback: updatedFeedbackPoints
                    }
                };
                // Update the list
                updatedSkillsList[skillIndex] = updatedSkill;
                if (skill.type === "role") {
                    setRoleSpecificSkillsFeedback(updatedSkillsList);
                } else {
                    setCultureSpecificSkillsFeedback(updatedSkillsList);
                }
                // Update form values to match new indices
                updatedFeedbackPoints.forEach((feedback, newIndex)=>{
                    skillsSetValue(`skill-${skill.id}-${newIndex}`, feedback);
                });
            }
        }
    };
    const renderCultureAndRoleSpecificSkills = (skills)=>{
        return skills.length ? skills.map((feedback, index)=>{
            return editMode.isEdit && editMode.skillId === feedback.id ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: index === 0 ? "interview-summary-inner-card saprator-none" : "interview-summary-inner-card",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "section-heading",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            children: feedback.skillTitle
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 333,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 332,
                        columnNumber: 15
                    }, this),
                    feedback.highlights && feedback.highlights?.overAllFeedback?.length ? feedback.highlights && feedback.highlights?.overAllFeedback?.map((feedbackPoint, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "d-flex align-items-center justify-content-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                            htmlFor: `skill-${feedback.id}-${index}`,
                                            required: true,
                                            children: [
                                                "Point ",
                                                index + 1 < 10 ? `0${index + 1}` : index + 1
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                            lineNumber: 340,
                                            columnNumber: 25
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            type: "button",
                                            className: "clear-btn text-danger p-0",
                                            onClick: ()=>removeSkillFeedbackPoint(feedback, index),
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DeleteIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                className: "p-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 345,
                                                columnNumber: 27
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                            lineNumber: 344,
                                            columnNumber: 25
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 339,
                                    columnNumber: 23
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textbox$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    className: "form-control",
                                    control: skillsControl,
                                    name: `skill-${feedback.id}-${index}`,
                                    type: "text",
                                    placeholder: "Enter your Feedback"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 348,
                                    columnNumber: 23
                                }, this)
                            ]
                        }, index, true, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 338,
                            columnNumber: 21
                        }, this)) : null,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        className: "clear-btn secondary p-0 mb-4",
                        onClick: ()=>addSkillFeedbackPoint(feedback),
                        children: "+Add Another Point"
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 359,
                        columnNumber: 15
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "section-heading",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "m-0",
                            children: "Stratum Score"
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 364,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 363,
                        columnNumber: 15
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        className: "number-task",
                        children: [
                            1,
                            2,
                            3,
                            4,
                            5,
                            6,
                            7,
                            8,
                            9,
                            10
                        ].map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: feedback.score === item ? "active" : "",
                                onClick: ()=>{
                                    // Update the skill score when clicked
                                    const updatedSkillsList = feedback.type === "role" ? [
                                        ...roleSpecificSkillsFeedback
                                    ] : [
                                        ...cultureSpecificSkillsFeedback
                                    ];
                                    const skillIndex = updatedSkillsList.findIndex((s)=>s.id === feedback.id);
                                    if (skillIndex !== -1) {
                                        // Create updated skill object with new score
                                        const updatedSkill = {
                                            ...updatedSkillsList[skillIndex],
                                            score: item
                                        };
                                        // Update the appropriate list
                                        updatedSkillsList[skillIndex] = updatedSkill;
                                        if (feedback.type === "role") {
                                            setRoleSpecificSkillsFeedback(updatedSkillsList);
                                        } else {
                                            setCultureSpecificSkillsFeedback(updatedSkillsList);
                                        }
                                    }
                                },
                                style: {
                                    cursor: "pointer"
                                },
                                children: item === 10 ? "Extreme" : item
                            }, index, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 368,
                                columnNumber: 19
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 366,
                        columnNumber: 15
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "number-task-text",
                        children: interviewStaticInformation.stratumDescription[feedback.score]
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 399,
                        columnNumber: 15
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "d-flex mt-5",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                type: "button",
                                className: "secondary-btn button-sm minWidth rounded-md",
                                onClick: ()=>saveSkillFeedback(feedback),
                                children: "Save"
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 401,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                type: "button",
                                className: "dark-outline-btn button-sm minWidth rounded-md ms-3",
                                onClick: ()=>onCancelEditMode(),
                                children: "Cancel"
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 404,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 400,
                        columnNumber: 15
                    }, this)
                ]
            }, feedback.id, true, {
                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                lineNumber: 331,
                columnNumber: 13
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `interview-summary-inner-card ${index === 0 ? "saprator-none" : ""}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "background-heading col-10",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                children: [
                                    feedback.skillTitle,
                                    " Stratum Score: ",
                                    feedback.score < 10 ? `0${feedback.score}` : feedback.score
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 412,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: interviewStaticInformation.stratumDescription[feedback.score]
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 415,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 411,
                        columnNumber: 15
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "summary-highlights pb-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "summary-title d-flex items-center mb-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$AiMarkIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "me-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 419,
                                        columnNumber: 19
                                    }, this),
                                    " Highlights"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 418,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                className: "highlight-list",
                                children: feedback.highlights?.overAllFeedback?.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        className: "highlight-item",
                                        children: item
                                    }, index, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 423,
                                        columnNumber: 21
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 421,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 417,
                        columnNumber: 15
                    }, this),
                    !feedbackData?.isFeedbackFilled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        className: "clear-btn secondary p-0 mt-4",
                        onClick: ()=>enableEditMode(feedback.id),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$EditIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "me-2 p-1",
                                fillNone: true
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 431,
                                columnNumber: 19
                            }, this),
                            " Update Feedback"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 430,
                        columnNumber: 17
                    }, this) : null
                ]
            }, feedback.id, true, {
                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                lineNumber: 410,
                columnNumber: 13
            }, this);
        }) : null;
    };
    // Add a new empty feedback point for career-based skills
    const addNewCareerFeedbackPoint = ()=>{
        if (careerFeedbackData && careerFeedbackData.highlights) {
            const updatedCareerFeedback = {
                ...careerFeedbackData,
                highlights: {
                    ...careerFeedbackData.highlights,
                    overAllFeedback: [
                        ...careerFeedbackData.highlights.overAllFeedback || [],
                        ""
                    ]
                }
            };
            setCareerFeedbackData(updatedCareerFeedback);
            // Set an empty value for the new feedback point in the form
            const newIndex = updatedCareerFeedback.highlights.overAllFeedback.length - 1;
            careerSetValue(`career-${newIndex}`, "");
        }
    };
    // Remove a feedback point by index
    const removeCareerFeedbackPoint = (indexToRemove)=>{
        if (careerFeedbackData && careerFeedbackData.highlights) {
            const updatedFeedbackPoints = careerFeedbackData.highlights.overAllFeedback.filter((_, index)=>index !== indexToRemove);
            const updatedCareerFeedback = {
                ...careerFeedbackData,
                highlights: {
                    ...careerFeedbackData.highlights,
                    overAllFeedback: updatedFeedbackPoints
                }
            };
            setCareerFeedbackData(updatedCareerFeedback);
            // Update the form values to match the new indices
            updatedFeedbackPoints.forEach((feedback, newIndex)=>{
                careerSetValue(`career-${newIndex}`, feedback);
            });
        }
    };
    // Save the career-based feedback changes
    const saveCareerFeedback = careerHandleSubmit((data)=>{
        if (careerFeedbackData && careerFeedbackData.highlights) {
            // Create a new array to store the updated feedback points
            const updatedFeedbackPoints = careerFeedbackData.highlights.overAllFeedback.map((_, index)=>{
                // Get the current value from the form data
                return data[`career-${index}`] || "";
            });
            // Update the career feedback data state
            const updatedCareerFeedback = {
                ...careerFeedbackData,
                highlights: {
                    ...careerFeedbackData.highlights,
                    overAllFeedback: updatedFeedbackPoints
                }
            };
            setCareerFeedbackData(updatedCareerFeedback);
            setEditCareerBasedSkills(false); // Exit edit mode
        }
    });
    // Cancel editing career-based feedback
    const cancelCareerFeedback = ()=>{
        // Restore the original data state before editing started
        if (originalCareerFeedbackData) {
            setCareerFeedbackData(originalCareerFeedbackData);
        }
        setEditCareerBasedSkills(false); // Exit edit mode
    };
    // Enable editing behavioral notes
    const enableEditBehavioralNotes = ()=>{
        // Store the current behavioral notes before editing
        setOriginalBehavioralNotes(feedbackData?.applicantBehavioralNotes || "");
        setEditBehavioralNotes(true);
        // Pre-populate the form with existing notes
        if (feedbackData?.applicantBehavioralNotes) {
            careerSetValue("behavioralNotes", feedbackData.applicantBehavioralNotes);
        }
    };
    // Save behavioral notes changes (only to state, no API call)
    const saveBehavioralNotes = ()=>{
        // Get values from the form using handleSubmit
        careerHandleSubmit((formData)=>{
            const behavioralNotes = formData.behavioralNotes;
            // Update the local data in state
            if (feedbackData) {
                setFeedbackData({
                    ...feedbackData,
                    applicantBehavioralNotes: behavioralNotes
                });
            }
            // Exit edit mode
            setEditBehavioralNotes(false);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])("Behavioral notes saved");
        })();
    };
    // Cancel editing behavioral notes
    const cancelBehavioralNotes = ()=>{
        // Restore the original behavioral notes
        if (feedbackData) {
            careerSetValue("behavioralNotes", originalBehavioralNotes);
        }
        // Exit edit mode
        setEditBehavioralNotes(false);
    };
    // Save the skill-specific (role or culture) feedback changes
    const saveSkillFeedback = (skill)=>{
        const onSubmit = skillsHandleSubmit((data)=>{
            if (skill && skill.highlights) {
                // Determine which skill list to update
                const updatedSkillsList = skill.type === "role" ? [
                    ...roleSpecificSkillsFeedback
                ] : [
                    ...cultureSpecificSkillsFeedback
                ];
                const skillIndex = updatedSkillsList.findIndex((s)=>s.id === skill.id);
                if (skillIndex !== -1) {
                    // Create a new array to store the updated feedback points
                    const updatedFeedbackPoints = updatedSkillsList[skillIndex].highlights.overAllFeedback.map((_, index)=>{
                        // Get the current value from the form data
                        return data[`skill-${skill.id}-${index}`] || "";
                    });
                    // Create updated skill object with new feedback points
                    const updatedSkill = {
                        ...updatedSkillsList[skillIndex],
                        highlights: {
                            ...updatedSkillsList[skillIndex].highlights,
                            overAllFeedback: updatedFeedbackPoints
                        }
                    };
                    // Update the appropriate list
                    updatedSkillsList[skillIndex] = updatedSkill;
                    if (skill.type === "role") {
                        setRoleSpecificSkillsFeedback(updatedSkillsList);
                    } else {
                        setCultureSpecificSkillsFeedback(updatedSkillsList);
                    }
                    // Exit edit mode
                    setEditMode({
                        isEdit: false,
                        skillId: null
                    });
                }
            }
        });
        // Execute the submit handler
        onSubmit();
    };
    // Cancel editing skill-specific feedback
    const onCancelEditMode = ()=>{
        // Restore the original data states before editing started
        setRoleSpecificSkillsFeedback(originalRoleSpecificSkillsFeedback);
        setCultureSpecificSkillsFeedback(originalCultureSpecificSkillsFeedback);
    };
    const onHandleUpdateFeedback = async (isAllowedForNextRound)=>{
        try {
            // Set the appropriate processing state based on which button was clicked
            if (isAllowedForNextRound) {
                setProcessingAdvance(true);
            } else {
                setProcessingReject(true);
            }
            const highlightData = [
                ...roleSpecificSkillsFeedback,
                ...cultureSpecificSkillsFeedback
            ];
            if (careerFeedbackData) {
                highlightData.push(careerFeedbackData);
            }
            console.log("highlightData===>>>", highlightData);
            // Include behavioral notes in the API call to update them in the database
            const behavioralNotes = feedbackData?.applicantBehavioralNotes || "";
            console.log("Sending behavioral notes to backend:", behavioralNotes);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$interviewServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateInterviewFeedback"])({
                interviewId: interviewId,
                feedback: JSON.stringify(highlightData),
                isAdvanced: isAllowedForNextRound,
                behavioralNotes: behavioralNotes
            });
            // For now, the backend will need to be updated separately to handle behavioral notes
            // The notes are already stored in state and can be displayed to the user
            if (response.data && response.data.success) {
                const successMessage = isAllowedForNextRound ? "Feedback submitted successfully. Candidate has been advanced." : "Feedback submitted successfully. Candidate has been rejected.";
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(successMessage);
                // Redirect to the dashboard after successful submission
                setTimeout(()=>{
                    router.push("/dashboard");
                }, 1000); // Short delay to allow the toast message to be read
            } else {
                const errorMessage = response.data?.message || "Failed to submit feedback";
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(errorMessage));
            }
        } catch (error) {
            console.error("Error updating feedback:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])("Something went wrong while submitting your feedback. Please try again.");
        } finally{
            // Reset both processing states regardless of which button was clicked
            setProcessingAdvance(false);
            setProcessingReject(false);
        }
    };
    // ...
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "container",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "common-page-header",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "common-page-head-section",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "main-heading",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                children: [
                                    "Interview ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Feedback"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 671,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 670,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 669,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 668,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "section-heading mt-5",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "mb-3",
                                children: feedbackData?.candidateName
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 676,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "operation-text",
                                children: [
                                    feedbackData?.jobTitle,
                                    " ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "px-2",
                                        children: "|"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 679,
                                        columnNumber: 38
                                    }, this),
                                    "Round ",
                                    feedbackData?.roundNumber,
                                    " ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "px-2",
                                        children: "|"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 679,
                                        columnNumber: 103
                                    }, this),
                                    " ",
                                    feedbackData?.candidateEmail
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 678,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 675,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                lineNumber: 667,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "inner-section",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "row",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "col-md-8",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "interview-summary",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "summary-header",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "summary-heading",
                                                children: "Career Based Feedback"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 691,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                            lineNumber: 690,
                                            columnNumber: 15
                                        }, this),
                                        editCareerBasedSkills ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                careerFeedbackData.highlights && careerFeedbackData.highlights?.overAllFeedback?.length ? careerFeedbackData.highlights && careerFeedbackData.highlights?.overAllFeedback?.map((feedback, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "d-flex align-items-center justify-content-between",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                                        htmlFor: `career-${index}`,
                                                                        required: true,
                                                                        children: [
                                                                            "Point ",
                                                                            index + 1 < 10 ? `0${index + 1}` : index + 1
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                                        lineNumber: 700,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        type: "button",
                                                                        className: "clear-btn text-danger p-0",
                                                                        onClick: ()=>removeCareerFeedbackPoint(index),
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DeleteIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            className: "p-2"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                                            lineNumber: 704,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                                        lineNumber: 703,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                                lineNumber: 699,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textbox$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                className: "form-control",
                                                                control: careerControl,
                                                                name: `career-${index}`,
                                                                type: "text",
                                                                placeholder: "Enter your Feedback"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                                lineNumber: 707,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, index, true, {
                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                        lineNumber: 698,
                                                        columnNumber: 25
                                                    }, this)) : null,
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "clear-btn secondary p-0 mb-4",
                                                    onClick: addNewCareerFeedbackPoint,
                                                    children: "+Add Another Point"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 718,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "section-heading",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                        className: "m-0",
                                                        children: "Stratum Score"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                        lineNumber: 723,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 722,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                    className: "number-task",
                                                    children: [
                                                        1,
                                                        2,
                                                        3,
                                                        4,
                                                        5,
                                                        6,
                                                        7,
                                                        8,
                                                        9,
                                                        10
                                                    ].map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                            className: careerFeedbackData.score === item ? "active" : "",
                                                            onClick: ()=>{
                                                                // Update the career feedback score when clicked
                                                                const updatedCareerFeedback = {
                                                                    ...careerFeedbackData,
                                                                    score: item
                                                                };
                                                                setCareerFeedbackData(updatedCareerFeedback);
                                                            },
                                                            style: {
                                                                cursor: "pointer"
                                                            },
                                                            children: item === 10 ? "Extreme" : item
                                                        }, index, false, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 727,
                                                            columnNumber: 23
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 725,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "number-task-text",
                                                    children: interviewStaticInformation.stratumDescription[careerFeedbackData.score]
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 744,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "d-flex mt-5",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            type: "button",
                                                            className: "secondary-btn button-sm minWidth rounded-md",
                                                            onClick: saveCareerFeedback,
                                                            children: "Save"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 746,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            type: "button",
                                                            className: "dark-outline-btn button-sm minWidth rounded-md ms-3",
                                                            onClick: cancelCareerFeedback,
                                                            children: "Cancel"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 749,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 745,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "background-heading col-10",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            children: [
                                                                "Stratum Score: ",
                                                                careerFeedbackData.score < 10 ? `0${careerFeedbackData.score}` : careerFeedbackData.score
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 757,
                                                            columnNumber: 20
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: interviewStaticInformation.stratumDescription[careerFeedbackData.score]
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 758,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 756,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "summary-highlights pb-3",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                            className: "summary-title d-flex items-center mb-4",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$AiMarkIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    className: "me-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                                    lineNumber: 762,
                                                                    columnNumber: 23
                                                                }, this),
                                                                " Highlights"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 761,
                                                            columnNumber: 21
                                                        }, this),
                                                        careerFeedbackData.highlights && careerFeedbackData.highlights?.overAllFeedback?.length ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                            className: "highlight-list",
                                                            children: careerFeedbackData.highlights.overAllFeedback.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                    className: "highlight-item",
                                                                    children: item
                                                                }, index, false, {
                                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                                    lineNumber: 767,
                                                                    columnNumber: 27
                                                                }, this))
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 765,
                                                            columnNumber: 23
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: "No highlights available."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 773,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 760,
                                                    columnNumber: 19
                                                }, this),
                                                !feedbackData?.isFeedbackFilled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "clear-btn secondary p-0 mt-4",
                                                    onClick: ()=>{
                                                        // Store the original data before entering edit mode
                                                        setOriginalCareerFeedbackData(JSON.parse(JSON.stringify(careerFeedbackData)));
                                                        setEditCareerBasedSkills(true);
                                                        if (careerFeedbackData.highlights && careerFeedbackData.highlights.overAllFeedback) {
                                                            careerFeedbackData.highlights.overAllFeedback.forEach((feedback, index)=>{
                                                                careerSetValue(`career-${index}`, feedback);
                                                            });
                                                        }
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$EditIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            className: "me-2 p-1",
                                                            fillNone: true
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                            lineNumber: 790,
                                                            columnNumber: 23
                                                        }, this),
                                                        " Update Feedback"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 777,
                                                    columnNumber: 21
                                                }, this) : null
                                            ]
                                        }, void 0, true)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 689,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "interview-summary",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "summary-header",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "summary-heading",
                                                children: "Role Specific Performance Based Feedback"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 802,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                            lineNumber: 801,
                                            columnNumber: 15
                                        }, this),
                                        roleSpecificSkillsFeedback.length ? renderCultureAndRoleSpecificSkills(roleSpecificSkillsFeedback) : null
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 800,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "interview-summary",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "summary-header",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "summary-heading",
                                                children: "Culture Specific Performance Based Feedback"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 811,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                            lineNumber: 810,
                                            columnNumber: 15
                                        }, this),
                                        cultureSpecificSkillsFeedback.length ? renderCultureAndRoleSpecificSkills(cultureSpecificSkillsFeedback) : null
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 809,
                                    columnNumber: 13
                                }, this),
                                !aiDecisionForNextRound ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "feedback-result-card",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "row justify-content-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "col-md-10 pe-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "title",
                                                        children: [
                                                            "Innerview ",
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Verdict"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                                lineNumber: 820,
                                                                columnNumber: 33
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                        lineNumber: 819,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "description",
                                                        children: aiDecisionForNextRound === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AIDecisionForNextRound"].APPROVED ? "The AI has assessed the candidate and recommends advancing them to the next round. Please make your final decision." : "The AI has assessed this candidate and recommends not advancing them to the next round."
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                        lineNumber: 822,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 818,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "col-2",
                                                children: aiDecisionForNextRound === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AIDecisionForNextRound"].APPROVED ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ApprovedIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "status-icon"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 830,
                                                    columnNumber: 23
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$RejectedIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "status-icon"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                    lineNumber: 832,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 828,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 817,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                    lineNumber: 816,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 686,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "col-md-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "behavioral-letter-card",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                        children: [
                                            "Behavioral ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Performance"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 842,
                                                columnNumber: 28
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 841,
                                        columnNumber: 15
                                    }, this),
                                    feedbackData && feedbackData.applicantBehavioralNotes ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: feedbackData.applicantBehavioralNotes
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 845,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "Describe Candidate’s Behaviour & Other Observations."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 847,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$LetterFoldIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "fold-svg"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 850,
                                        columnNumber: 15
                                    }, this),
                                    !feedbackData?.isFeedbackFilled ? editBehavioralNotes ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                        htmlFor: "behavioralNotes",
                                                        required: true,
                                                        children: "Behavioral Notes"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                        lineNumber: 855,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        rows: 5,
                                                        name: "behavioralNotes",
                                                        control: careerControl,
                                                        placeholder: "Describe Candidate's Behaviour & Other Observations",
                                                        className: "form-control"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                        lineNumber: 858,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 854,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "d-flex mt-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        type: "button",
                                                        className: "secondary-btn button-sm minWidth rounded-md",
                                                        onClick: saveBehavioralNotes,
                                                        children: "Save"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                        lineNumber: 867,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        type: "button",
                                                        className: "dark-outline-btn button-sm minWidth rounded-md ms-3",
                                                        onClick: cancelBehavioralNotes,
                                                        children: "Cancel"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                        lineNumber: 870,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 866,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 853,
                                        columnNumber: 19
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "clear-btn primary p-0 mt-4 update-btn",
                                        onClick: enableEditBehavioralNotes,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$EditIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                className: "me-2 p-1",
                                                fillNone: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                                lineNumber: 877,
                                                columnNumber: 21
                                            }, this),
                                            " Update Feedback"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                        lineNumber: 876,
                                        columnNumber: 19
                                    }, this) : null
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                                lineNumber: 840,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                            lineNumber: 839,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                    lineNumber: 685,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                lineNumber: 684,
                columnNumber: 7
            }, this),
            !feedbackData?.isFeedbackFilled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "button-align pt-4 pb-5",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        className: "primary-btn rounded-md",
                        onClick: ()=>onHandleUpdateFeedback(true),
                        disabled: processingAdvance || processingReject,
                        children: processingAdvance ? "Processing..." : "Advance"
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 888,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        className: "dark-outline-btn rounded-md",
                        onClick: ()=>onHandleUpdateFeedback(false),
                        disabled: processingAdvance || processingReject,
                        children: processingReject ? "Processing..." : "Reject"
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                        lineNumber: 891,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
                lineNumber: 887,
                columnNumber: 9
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx",
        lineNumber: 666,
        columnNumber: 5
    }, this);
};
_s(InterviewFeedback, "QXyOQB/Aa1LA6QciBDfnKf0VIAg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
_c = InterviewFeedback;
const __TURBOPACK__default__export__ = InterviewFeedback;
var _c;
__turbopack_context__.k.register(_c, "InterviewFeedback");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/interview-feedback/[interviewId]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>InterviewFeedbackPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$interviewFeedback$2f$InterviewFeedback$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/interviewFeedback/InterviewFeedback.tsx [app-client] (ecmascript)");
"use client";
;
;
function InterviewFeedbackPage({ params }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$interviewFeedback$2f$InterviewFeedback$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            params: params
        }, void 0, false, {
            fileName: "[project]/src/app/interview-feedback/[interviewId]/page.tsx",
            lineNumber: 9,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/interview-feedback/[interviewId]/page.tsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
}
_c = InterviewFeedbackPage;
var _c;
__turbopack_context__.k.register(_c, "InterviewFeedbackPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_27e1139d._.js.map