import dbConnection from "../dbConnection";
import InterviewSkillQuestionsAnswersModel, { SourceType } from "../schema/interview_skill_questions_answers";
import JobSkillsModel, { SkillType } from "../schema/job_skills";
import { INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS } from "../utils/constants";
import OpenAiHelper from "../utils/helper";

// module.exports.

export const generateInterviewQuestionsLambda = async (event, _context, _callback) => {
  console.log("generate interview questions LambdaFunction event", event);

  const { jobId, jobApplicationId, roundNumber, interviewId } = event;

  try {
    const interviewSkillQuestionsAnswersRepo =
      await dbConnection.getRepository(
        InterviewSkillQuestionsAnswersModel
      );

    const jobSkillsRepo =
      await dbConnection.getRepository(JobSkillsModel);

    // First check if role_specific and culture_specific questions already exist for this job application
    const existingQuestionsCount = await interviewSkillQuestionsAnswersRepo
      .createQueryBuilder("qa")
      .innerJoin("qa.jobSkill", "js")
      .where("qa.jobApplicationId = :jobApplicationId", {
        jobApplicationId,
      })
      .andWhere("js.type IN (:...types)", {
        types: [SkillType.ROLE_SPECIFIC, SkillType.CULTURE_SPECIFIC],
      })
      .getCount();

    console.log("existingQuestionsCount===>>>", existingQuestionsCount);

    // Get job skills by types
    const getSkillsByType = async (type?: string) => {
      const queryBuilder = jobSkillsRepo
        .createQueryBuilder("jobSkill")
        .leftJoinAndSelect("jobSkill.skill", "skill")
        .where("jobSkill.jobId = :jobId", { jobId });
      if (type) {
        queryBuilder.andWhere("jobSkill.type = :type", { type });
      }
      queryBuilder.select([
        "jobSkill.id",
        "jobSkill.type",
        "skill.title",
        "skill.shortDescription",
      ]);
      return queryBuilder.getRawMany();
    };

    // Create questions for a specific skill type
    const createQuestionsForSkillType = async (
      jobSkillType?: string,
      difficultyLevel = INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS.BEGINNER
    ) => {
      const skillsInfo = await getSkillsByType(jobSkillType);

      const result = await OpenAiHelper.generateQuestionForSkill(
        skillsInfo,
        difficultyLevel
      );

      const skills = JSON.parse(result);

      console.log(
        typeof skills,
        "skills===>>>",
        JSON.stringify(skills, null, 2)
      );

      if (skills && skills.questions) {
        const questionsToInsert = [];

        for (const jobSkillId in skills.questions) {
          const questionList = skills.questions[jobSkillId];
          questionList.forEach((questionObj) => {
            questionsToInsert.push({
              jobSkillId: Number(jobSkillId),
              interviewId:
                questionObj.skillType === SkillType.CAREER_BASED
                  ? interviewId
                  : null,
              jobApplicationId,
              question: questionObj.questionText,
              answer: "",
              source: SourceType.AI,
            });
          });
        }

        if (questionsToInsert.length) {
          await interviewSkillQuestionsAnswersRepo.save(questionsToInsert);
        }
      }
    };

    if (roundNumber > 1) {
      // create career based questions
      await createQuestionsForSkillType(
        SkillType.CAREER_BASED,
        roundNumber > 3
          ? INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS.ADVANCED
          : INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS.INTERMEDIATE
      );
    } else if (roundNumber === 1) {
      await createQuestionsForSkillType(
        existingQuestionsCount ? SkillType.CAREER_BASED : undefined
      );
    }


    return {
      success: true,
      message: "success",
    };
  }
  catch (error) {
    console.error(error);

    return {
      success: false,
    };
  }
}