import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

/* eslint-disable no-unused-vars */
export enum PaymentType {
  FREE = "Free",
  PAID = "Paid",
}

// Benefits structure interface based on subscription plan features
interface Benefits {
  // Job related features
  job_postings?: {
    limit?: number;
    unlimited?: boolean;
  };
  resume_screening?: {
    limit?: number;
    unlimited?: boolean;
  };
  ai_job_description?: boolean;

  // Candidate tracking features
  candidate_tracking?: {
    type?: "basic" | "full" | "customizable";
    description?: string;
  };
  applicant_tracking_system?: boolean;

  // Interview features
  pre_interview_assessment?: boolean;
  ai_generated_questions?: boolean;
  realtime_follow_up_questions?: boolean;
  non_verbal_communication_analysis?: boolean;
  final_analysis_after_interview?: boolean;
  ai_powered_interview_summary?: { customizable?: boolean };
  skill_specific_assessments?: boolean;

  // Access and roles
  role_based_access?: { customizable?: boolean };

  // Support
  support?: "email" | "dedicated_manager" | "contact_us" | "none";

  // Integrations
  integrations?: {
    type?: "limited" | "full" | "customizable";
    platforms?: string[];
  };
  analytics_reporting?: {
    type?: "basic" | "advanced" | "customizable";
    description?: string;
  };

  // Resume handling
  resume_upload?: "single" | "batch" | "api";
  website_resume_parser?: boolean;

  // Security
  data_security?: {
    level?: "standard" | "enhanced" | "advanced";
    compliance?: string[];
  };
}

@Entity("subscription_plans")
class SubscriptionPlanModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "name", nullable: false })
  name: string;

  @Column({
    name: "description",
    type: "varchar",
    length: 500,
    nullable: false,
  })
  description: string;

  @Column({
    name: "stripe_product_id",
    type: "varchar",
    length: 50,
    nullable: false,
  })
  stripeProductId: string;

  @Column({ name: "benefits", type: "json" })
  benefits: Benefits;

  @Column({
    type: "enum",
    enum: PaymentType,
    name: "payment_type",
    nullable: false,
  })
  paymentType: PaymentType;

  @Column({ name: "is_active", type: "varchar", length: 50, default: true })
  isActive: boolean;

  // Additional field to identify free plans (based on payment type but explicitly stored)
  @Column({ name: "is_free", default: false })
  isFree: boolean;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}

export default SubscriptionPlanModel;
