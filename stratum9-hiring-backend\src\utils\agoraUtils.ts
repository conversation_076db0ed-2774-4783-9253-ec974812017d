/**
 * Agora Cloud Recording utility functions
 * Handles authentication and common operations for Agora API
 */

import axios from "axios";
import config from "../config/envConfig";
import { getSecretKeys } from "../config/awsConfig";

// Environment config
const envConfig = config();

// Agora API base URL
const AGORA_API_BASE_URL = "https://api.agora.io/v1/apps/";

/**
 * Generates Base64 encoded Basic auth header for Agora API
 * @returns Authorization header value
 */
export const getAgoraAuthHeader = async (): Promise<string> => {
  const keys = await getSecretKeys();
  const customerKey = keys.agora_customerId;
  const customerSecret = keys.agora_customerSecret;

  if (!customerKey || !customerSecret) {
    return null;
  }

  // Concatenate customer key and customer secret
  const plainCredential = `${customerKey}:${customerSecret}`;

  // Encode with base64
  const encodedCredential = Buffer.from(plainCredential).toString("base64");

  return `Basic ${encodedCredential}`;
};

/**
 * Create axios instance with Agora API authorization headers
 * @returns Axios instance with authorization headers
 */
export const agoraApiClient = async () =>
  axios.create({
    headers: {
      Authorization: await getAgoraAuthHeader(),
      "Content-Type": "application/json",
    },
  });

/**
 * Constructs the base URL for Agora API requests
 * @returns Base URL for Agora API with app ID
 */
export const getAgoraApiBaseUrl = async (): Promise<string> => {
  const keys = await getSecretKeys();
  return `${AGORA_API_BASE_URL}${keys.agora_appId}`;
};

/**
 * Get S3 Storage Configuration for Agora Cloud Recording
 * @returns S3 Storage Configuration object for Agora Cloud Recording
 */
export const getS3StorageConfig = (
  jobId: number,
  jobApplicationId: number,
  interviewId?: number
) => {
  const { s3BucketName, s3AccessKey, s3SecretKey, s3Region, s3FilenamePrefix } =
    envConfig;

  console.log("====", {
    s3BucketName,
    s3AccessKey,
    s3SecretKey,
    s3Region,
    s3FilenamePrefix,
  });

  if (
    !s3BucketName ||
    !s3AccessKey ||
    !s3SecretKey ||
    s3Region === undefined ||
    !s3FilenamePrefix
  ) {
    return null;
  }

  return {
    vendor: 1, // AWS S3
    region: s3Region,
    bucket: s3BucketName,
    accessKey: s3AccessKey,
    secretKey: s3SecretKey,
    fileNamePrefix: [
      s3FilenamePrefix,
      jobId.toString(),
      jobApplicationId.toString(),
      interviewId?.toString(),
    ],
  };
};
