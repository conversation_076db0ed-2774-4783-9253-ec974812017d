import Joi from "joi";

const joiObject = Joi.object().options({ abortEarly: false });

// Validation schema for adding a new user role
export const addUserRoleValidation = Joi.object({
  name: Joi.string().required().trim().min(3).max(50).messages({
    "string.empty": "role_name_required",
    "string.min": "role_name_min_length",
    "string.max": "role_name_max_length",
    "any.required": "role_name_required",
  }),
});

// Validation schema for updating a user role
export const updateUserRoleValidation = Joi.object({
  name: Joi.string().required().trim().min(3).max(50).messages({
    "string.empty": "role_name_required",
    "string.min": "role_name_min_length",
    "string.max": "role_name_max_length",
    "any.required": "role_name_required",
  }),
});

// Validation schema for role ID parameter
export const roleIdParamValidation = Joi.object({
  roleId: Joi.number().positive().required().messages({
    "number.base": "role_id_must_be_number",
    "number.positive": "role_id_must_be_positive",
    "any.required": "role_id_required",
  }),
});

// Validation schema for updating role permissions
export const updateRolePermissionsValidation = Joi.object({
  permissionIds: Joi.array()
    .items(Joi.number().required())
    .min(1)
    .required()
    .messages({
      "array.min": "at_least_one_permission_required",
      "array.base": "permissions_must_be_array",
      "any.required": "permissions_required",
      "number.base": "permission_id_must_be_number",
    }),
});

// Validation schema for role permissions query parameters
export const rolePermissionsParamsValidation = joiObject.keys({
  searchByUserRole: Joi.string().allow("").optional(),
  page: Joi.number().optional(),
  limit: Joi.number().default(10),
});

export const getUserRolesPaginationValidation = Joi.object({
  page: Joi.number().optional(),
  limit: Joi.number().default(10),
});

export const addRoleDepartmentAndPermissionForAdminValidation = Joi.object({
  organizationId: Joi.number().positive().required(),
  userId: Joi.number().positive().required(),
});
