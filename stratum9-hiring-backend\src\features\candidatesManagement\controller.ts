import { Request, Response } from "express";
import {
  DEFAULT_LIMIT,
  CANDIDATE_APPLICATION_MSG,
} from "../../utils/constants";
import CandidateApplicationService from "./services";

// Get all candidates
export const getCandidatesController = async (req: Request, res: Response) => {
  try {
    const orgId = Number(req.orgId);
    const jobId = req.query.jobId ? Number(req.query.jobId) : undefined;
    const searchStr = req.query.searchStr ? String(req.query.searchStr) : "";
    const offset = req.query.page ? Number(req.query.page) : 0;
    const isActive = req.query.isActive === "true";
    const limit = req.query.limit ? Number(req.query.limit) : DEFAULT_LIMIT;
    console.log("isActive controller", isActive);
    console.log("isActive controller", typeof isActive);
    const candidates = await CandidateApplicationService.getAllCandidates(
      orgId,
      jobId,
      isActive,
      searchStr,
      offset,
      limit
    );

    return res.status(200).json({
      success: true,
      data: candidates,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

// Update candidate application status

export const archiveActiveApplication = async (req: Request, res: Response) => {
  const applicationId = Number(req.params.applicationId);
  const orgId = Number(req.orgId);

  const { isActive, reason } = req.body;

  try {
    const result = await CandidateApplicationService.archiveActiveApplication(
      applicationId,
      orgId,
      isActive,
      reason
    );

    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

// Get top candidates

export const getTopCandidatesController = async (
  req: Request,
  res: Response
) => {
  try {
    const orgId = Number(req.orgId);
    const jobId = Number(req.query.jobId);

    const candidates = await CandidateApplicationService.getTopCandidates(
      orgId,
      jobId
    );

    return res.status(200).json({
      ...candidates,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

// Promote/Demote candidate

export const promoteDemoteCandidateController = async (
  req: Request,
  res: Response
) => {
  try {
    const { candidateId, applicationId, action } = req.body;

    // Uncomment and use when actual service is ready
    const data = await CandidateApplicationService.promoteDemoteCandidate(
      candidateId,
      applicationId,
      req.orgId,
      action
    );

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

export const getCandidateDetailsController = async (
  req: Request,
  res: Response
) => {
  try {
    const result = await CandidateApplicationService.getCandidateDetails(
      Number(req.query.jobApplicationId),
      req.orgId
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    console.log("Error in getCandidateDetailsController:", error);

    return res.status(500).json(error);
  }
};

export const addApplicantAdditionalInfoController = async (
  req: Request,
  res: Response
) => {
  try {
    const result = await CandidateApplicationService.addApplicantAdditionalInfo(
      req.orgId,
      req.body
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

// Controller for updating job application status
export const updateJobApplicationStatusController = async (
  req: Request,
  res: Response
) => {
  try {
    const jobApplicationId = Number(req.params.jobApplicationId);
    const { status } = req.body;

    const result = await CandidateApplicationService.updateJobApplicationStatus(
      jobApplicationId,
      status,
      req.orgId,
      req.userId
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Failed to update job application status",
      error: error instanceof Error ? error.message : "Unknown error",
      code: 500,
    });
  }
};

// Get candidate interview history
export const getCandidateInterviewHistoryController = async (
  req: Request,
  res: Response
) => {
  try {
    const interviewerId = Number(req.userId);
    const orgId = Number(req.orgId);
    const roleId = Number(req.roleId);
    const jobApplicationId = Number(req.params.jobApplicationId);

    console.log("interviewerId=============>", interviewerId);
    console.log("jobApplicationId=============>", jobApplicationId);
    console.log("orgId=============>", orgId);

    const result =
      await CandidateApplicationService.getCandidateInterviewHistory(
        interviewerId,
        jobApplicationId,
        orgId,
        roleId
      );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: CANDIDATE_APPLICATION_MSG.get_interview_history_failed,
      error:
        error instanceof Error
          ? error.message
          : CANDIDATE_APPLICATION_MSG.unknown_error,
      code: 500,
    });
  }
};

export const getApplicationFinalSummaryController = async (
  req: Request,
  res: Response
) => {
  try {
    const jobApplicationId = Number(req.params.jobApplicationId);

    const result = await CandidateApplicationService.getApplicationFinalSummary(
      jobApplicationId,
      req.orgId
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
    });
  }
};

// Get skill specific assessment
// export const getApplicationSKillScoreDataController = async (
//   req: Request,
//   res: Response
// ) => {
//   try {
//     const jobApplicationId = Number(req.params.jobApplicationId);

//     const result =
//       await CandidateApplicationService.getCandidateProfileSkillScoreData(
//         jobApplicationId
//       );

//     return res.status(200).json({
//       ...result,
//       code: 200,
//     });
//   } catch (error) {
//     return res.status(500).json({
//       success: false,
//       message: CANDIDATE_APPLICATION_MSG.get_skill_specific_assessment_failed,
//       error:
//         error instanceof Error
//           ? error.message
//           : CANDIDATE_APPLICATION_MSG.unknown_error,
//       code: 500,
//     });
//   }
// };

/**
 * Controller for generating final summary for a candidate application
 *
 * This controller handles the generation of comprehensive final summaries for candidates
 * based on their interview performance and assessment data. It validates the request
 * parameters and delegates the actual generation logic to the service layer.
 *
 * @param {Request} req - Express request object containing candidateId and jobApplicationId in query
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with success/error status
 */
export const generateFinalSummaryController = async (
  req: Request,
  res: Response
) => {
  try {
    const { jobApplicationId } = req.query;
    const { orgId, userId } = req;

    const result = await CandidateApplicationService.generateFinalSummary(
      +jobApplicationId,
      +orgId,
      +userId
    );

    return res.status(200).json(result);
  } catch (error) {
    console.error("Error in generateFinalSummaryController:", error);
    return res.status(500).json({
      success: false,
      message: CANDIDATE_APPLICATION_MSG.generate_final_summary_failed,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      code: 500,
    });
  }
};

export const getAllHiredCandidate = async (req: Request, res: Response) => {
  try {
    const result = await CandidateApplicationService.getAllHiredCandidate(
      req.orgId
    );
    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    console.error("Error in getAllHiredCandidate:", error);
    return res.status(500).json({
      success: false,
      message: CANDIDATE_APPLICATION_MSG.get_hired_candidate_failed,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      code: 500,
    });
  }
};
