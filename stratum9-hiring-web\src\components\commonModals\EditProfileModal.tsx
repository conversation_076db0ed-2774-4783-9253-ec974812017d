"use client";
import React, { FC, useState, useEffect, useRef } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import { toastMessageError, toastMessageSuccess, uploadFileOnS3 } from "@/utils/helper";
import { useTranslations } from "next-intl";
import { UpdateProfilePayload, updateMyProfile } from "@/services/userProfileService";
import { IUserData } from "@/interfaces/authInterfaces";
import { useDispatch, useSelector } from "react-redux";
import { updateUserProfileData, selectProfileData, selectRole, selectDepartment } from "@/redux/slices/authSlice";
import Image from "next/image";
import candidateProfile from "../../../public/assets/images/doctor-strange.png";
import { IMAGE_EXTENSIONS, S3_PATHS, MAX_IMAGE_SIZE } from "@/constants/commonConstants";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { profileValidationSchema } from "@/validations/employeeManagementValidations";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
import Loader from "../loader/Loader";
import EditIcon from "../svgComponents/EditIcon";

interface EditProfileModalProps {
  onClickCancel: () => void;
  onSubmitSuccess: () => void;
}

interface ProfileFormData {
  firstName: string;
  lastName: string;
}

const EditProfileModal: FC<EditProfileModalProps> = ({ onClickCancel, onSubmitSuccess }) => {
  // Get user profile data from Redux store
  const userProfile: IUserData | null = useSelector(selectProfileData);
  const userRole = useSelector(selectRole);
  const userDepartment = useSelector(selectDepartment);
  const t = useTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();

  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(userProfile?.image || null);
  const [isLoading, setIsLoading] = useState(false);
  const [formChanged, setFormChanged] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Set up form with validation
  const {
    control,
    handleSubmit: submitForm,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm<ProfileFormData>({
    defaultValues: {
      firstName: userProfile?.first_name,
      lastName: userProfile?.last_name,
    },
    resolver: yupResolver(profileValidationSchema(t)),
    mode: "onChange",
  });

  // Update form values when userProfile changes
  useEffect(() => {
    if (userProfile) {
      setImagePreview(userProfile.image || null);
    }
  }, [userProfile, setValue]);

  // Watch for changes in form fields
  const watchFirstName = watch("firstName");
  const watchLastName = watch("lastName");

  // Check if form fields have changed
  useEffect(() => {
    if (userProfile) {
      const hasNameChanged = watchFirstName !== userProfile.first_name || watchLastName !== userProfile.last_name;

      // Set form as changed if any field changed or if image was uploaded
      setFormChanged(hasNameChanged || imageFile !== null);
    }
  }, [watchFirstName, watchLastName, imageFile, userProfile]);

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsLoading(true);
    setFormChanged(true); // Mark form as changed when image is updated
    const { files } = e.target;

    if (files?.length && Number(files?.[0].size) < MAX_IMAGE_SIZE) {
      const file = files[0];

      if (file) {
        const extension = file?.type?.split("/")[1];

        if (IMAGE_EXTENSIONS.includes(extension?.toLowerCase())) {
          // Create a preview of the image
          const reader = new FileReader();
          reader.onload = (e) => {
            setImagePreview(e.target?.result as string);
          };
          reader.readAsDataURL(file);

          // Store the file for later upload
          setImageFile(file);

          // If there's an existing image, we'll delete it when submitting the form
        } else {
          toastMessageError(t("invalid_file_format"));
        }
      }
    } else {
      toastMessageError(t("invalid_image_size_format"));
    }

    // Reset the input field
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    setIsLoading(false);
  };

  const uploadImage = async (file: File): Promise<string> => {
    try {
      const fileNameArr = file.name.split(".");
      const extension = fileNameArr.length > 1 ? fileNameArr.pop() : "jpg";
      const fileName = fileNameArr.join(".");
      const filePath = S3_PATHS.PROFILE_IMAGE.replace(":path", `${fileName}-${new Date().getTime()}.${extension}`);

      // Upload the file to S3
      const uploadedFileUrl = (await uploadFileOnS3(file, filePath)) as string;
      return uploadedFileUrl;
    } catch (error) {
      console.error(t("error_uploading_image"), error);
      throw new Error(t("failed_to_upload_image"));
    }
  };

  const onSubmit = async (data: ProfileFormData) => {
    try {
      setIsSubmitting(true);

      let imageUrl = userProfile?.image || null;

      // If a new image is selected
      if (imageFile) {
        // Upload the new image
        imageUrl = await uploadImage(imageFile);
      }

      // Prepare payload for API
      const payload: UpdateProfilePayload = {
        firstName: data.firstName,
        lastName: data.lastName,
        image: imageUrl,
      };

      // Call API to update profile
      const response = await updateMyProfile(payload);

      if (response.data && response.data.success) {
        // Update Redux store with the new profile data
        dispatch(
          updateUserProfileData({
            first_name: data.firstName,
            last_name: data.lastName,
            image: imageUrl,
          })
        );

        toastMessageSuccess(t("profile_updated_successfully"));
        onSubmitSuccess();
      } else {
        const errorMessage = t(response.data?.message || "failed_to_update_profile");
        toastMessageError(errorMessage);
      }
    } catch (error) {
      console.error(t("error_updating_profile"), error);
      const errorMessage = t("an_error_occurred_while_updating_profile");
      toastMessageError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered" style={{ maxWidth: "650px", width: "100%" }}>
        <div className="modal-content rounded-4 shadow">
          <div className="modal-header border-0 pb-0 pt-4 position-relative">
            <div className="text-center w-100">
              <h2 className="m-0">{t("edit_profile")}</h2>
              <p className="mt-2 mb-3">{t("change_your_profile_details")}</p>
            </div>
            <Button className="modal-close-btn" onClick={onClickCancel} disabled={isSubmitting}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body p-5 pt-4">
            {/* Profile Image Upload */}
            <div className="text-center mb-4">
              <div className="position-relative d-inline-block">
                <div
                  className="profile-image-container position-relative mx-auto"
                  style={{
                    width: "100px",
                    height: "100px",
                    border: "2px solid #f0f0f0",
                    borderRadius: "16px",
                    overflow: "hidden",
                    cursor: "pointer",
                  }}
                  onClick={() => !isSubmitting && fileInputRef.current?.click()}
                >
                  <Image
                    src={imagePreview || userProfile?.image || candidateProfile}
                    alt="profile image"
                    className="candidate-image object-fit-cover"
                    width={100}
                    height={100}
                    onError={(e) => {
                      e.currentTarget.src = "../../../public/assets/images/doctor-strange.png"; // Fallback image
                    }}
                  />
                  {/* Semi-transparent dark overlay covering the entire image */}
                  <div className="position-absolute top-0 start-0 w-100 h-100" style={{ backgroundColor: "rgba(0, 0, 0, 0.8)", cursor: "pointer" }}>
                    {/* Update Picture button in center of image */}
                    <div className="position-absolute d-flex justify-content-center align-items-center w-100 h-100">
                      <div
                        style={{
                          backgroundColor: "transparent",
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "center",
                          alignItems: "center",
                          gap: "6px",
                          padding: "8px",
                        }}
                      >
                        <EditIcon fillNone fillColor="white" />
                        <span style={{ color: "white", fontSize: "12px", fontWeight: 300, lineHeight: "1.3" }}>
                          {t("update")}
                          <br />
                          {t("picture")}
                        </span>
                      </div>
                    </div>
                  </div>
                  {isLoading && (
                    <div className="position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-dark bg-opacity-50">
                      <div className="spinner-border spinner-border-sm text-light" role="status">
                        <span className="visually-hidden">{t("loading")}</span>
                      </div>
                    </div>
                  )}
                </div>
                <input
                  type="file"
                  ref={fileInputRef}
                  accept="image/*"
                  onChange={handleImageChange}
                  style={{ display: "none" }}
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Form Fields */}
            <div className="row">
              <div className="col-md-6">
                <InputWrapper>
                  <InputWrapper.Label htmlFor="firstName" required className="fw-bold">
                    {t("first_name")}
                  </InputWrapper.Label>
                  <Textbox
                    id="firstName"
                    name="firstName"
                    control={control}
                    placeholder={t("enter_first_name")}
                    className="form-control"
                    disabled={isSubmitting}
                  />
                  {errors.firstName && <InputWrapper.Error message={errors.firstName.message as string} />}
                </InputWrapper>
              </div>
              <div className="col-md-6">
                <InputWrapper>
                  <InputWrapper.Label htmlFor="lastName" required className="fw-bold">
                    {t("last_name")}
                  </InputWrapper.Label>
                  <Textbox
                    id="lastName"
                    name="lastName"
                    control={control}
                    placeholder={t("enter_last_name")}
                    className="form-control"
                    disabled={isSubmitting}
                  />
                  {errors.lastName && <InputWrapper.Error message={errors.lastName.message as string} />}
                </InputWrapper>
              </div>
            </div>

            <div className="mb-3">
              <InputWrapper>
                <InputWrapper.Label htmlFor="roleName" required className="fw-bold">
                  {t("role_name")}
                </InputWrapper.Label>
                <input
                  type="text"
                  className="form-control"
                  id="roleName"
                  placeholder={t("enter_role_name")}
                  value={userRole?.roleName || ""}
                  disabled
                />
              </InputWrapper>
            </div>

            <div className="mb-3">
              <InputWrapper>
                <InputWrapper.Label htmlFor="emailAddress" required className="fw-bold">
                  {t("email_address")}
                </InputWrapper.Label>
                <input
                  type="email"
                  className="form-control"
                  id="emailAddress"
                  placeholder={t("enter_email_address")}
                  value={userProfile?.email || ""}
                  disabled
                />
              </InputWrapper>
            </div>

            <div className="mb-3">
              <InputWrapper>
                <InputWrapper.Label htmlFor="organizationName" required className="fw-bold">
                  {t("organization_name")}
                </InputWrapper.Label>
                <input
                  type="text"
                  className="form-control"
                  id="organizationName"
                  placeholder={t("enter_organization_name")}
                  value={userProfile?.organizationName || ""}
                  disabled
                />
              </InputWrapper>
            </div>

            <div className="mb-3">
              <InputWrapper className="mb-4">
                <InputWrapper.Label htmlFor="departmentName" required className="fw-bold">
                  {t("department_name")}
                </InputWrapper.Label>
                <input
                  type="text"
                  className="form-control"
                  id="departmentName"
                  placeholder={t("enter_department_name")}
                  value={userDepartment?.departmentName || ""}
                  disabled
                />
              </InputWrapper>
            </div>
            <div className="d-flex gap-3 pt-4 mb-2">
              <Button className="dark-outline-btn rounded-md w-100" onClick={onClickCancel} disabled={isSubmitting}>
                {t("cancel")}
              </Button>
              <Button
                className="primary-btn rounded-md w-100"
                onClick={submitForm(onSubmit)}
                disabled={isSubmitting || !isValid || !formChanged}
                title={!formChanged ? t("no_changes_made") : ""}
              >
                {isSubmitting ? (
                  <>
                    {isLoading ? <Loader className="spinner-border-sm me-2" /> : <Loader className="spinner-border-sm me-2" />}
                    {t("saving_changes")}
                  </>
                ) : (
                  t("save_changes")
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditProfileModal;
