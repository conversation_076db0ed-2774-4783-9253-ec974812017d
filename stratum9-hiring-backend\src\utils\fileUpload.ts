import {
  DeleteObjectCommand,
  DeleteObjectsCommand,
  PutObjectCommand,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { CreateInvalidationCommand } from "@aws-sdk/client-cloudfront";
import envConfig from "../config/envConfig";
import awsConfig, { getSecretKeys } from "../config/awsConfig";
import { parsePdfWithRetries } from "./helper";

/**
 * getting pre url
 */
const CONFIG = envConfig();

export const gettingPreSignedUrl = async (
  filePath: string,
  fileFormat?: string
): Promise<string> => {
  try {
    // Use the properly configured S3 client from awsConfig
    const { s3Client } = awsConfig;

    // Create the command for the operation you want to presign
    const command = new PutObjectCommand({
      Key: `${filePath}`,
      Bucket: CONFIG.s3_bucket,
      ContentType: fileFormat,
    });

    // Generate presigned URL (expires in 10 minutes = 600 seconds)
    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: 600 * 600,
    });

    return signedUrl;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error generating pre-signed URL:", error);
    throw error;
  }
};

export const deleteFileFromS3 = async (keys: string | string[]) => {
  const { s3Client, cloudFrontClient } = awsConfig;
  const secretKeys = await getSecretKeys();

  console.log(secretKeys, "===keys=====>>>>", keys);

  if (typeof keys === "string") {
    const params = {
      Bucket: CONFIG.s3_bucket,
      Key: keys,
    };

    try {
      const command = new DeleteObjectCommand(params);
      const isDelete = await s3Client.send(command);

      console.log("isDelete=====>>>>", isDelete);

      // Invalidate the cached content
      if (isDelete.DeleteMarker) {
        const invalidateParams = {
          DistributionId:
            secretKeys.s3_bucket_cloudfront_distribution_id_for_s9_innerview,
          InvalidationBatch: {
            Paths: {
              Quantity: 1,
              Items: [`/${keys}`],
            },
            CallerReference: `invalidate-${Date.now()}`, // For unique cache invalidation request
          },
        };

        console.log("invalidateParams=====>>>>", invalidateParams);

        const cloudfrontCommand = new CreateInvalidationCommand(
          invalidateParams
        );

        console.log("cloudfrontCommand=====>>>>", cloudfrontCommand);
        await cloudFrontClient.send(cloudfrontCommand);
      }
      return isDelete;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Error deleting: ${error.message}`);
      return error;
    }
  } else {
    const params = {
      Bucket: CONFIG.s3_bucket,
      Delete: {
        Objects: keys.map((Key) => ({ Key })),
      },
    };

    try {
      const command = new DeleteObjectsCommand(params);
      const isDelete = await s3Client.send(command);

      console.log("isDelete Array=====>>>>", isDelete);

      // Invalidate the cached contents
      if (!isDelete.Errors || !isDelete.Errors.length) {
        const invalidateParams = {
          DistributionId:
            secretKeys.s3_bucket_cloudfront_distribution_id_for_s9_innerview,
          InvalidationBatch: {
            Paths: {
              Quantity: keys.length,
              Items: keys.map((Key) => `/${Key}`),
            },
            CallerReference: `invalidate-${Date.now()}`, // For unique cache invalidation request
          },
        };

        const cloudfrontCommand = new CreateInvalidationCommand(
          invalidateParams
        );
        await cloudFrontClient.send(cloudfrontCommand);
      }

      return isDelete;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Error deleting: ${error.message}`);
      return error;
    }
  }
};

/**
 * Download and parse PDF file from S3 URL
 * @param s3Url - The S3 URL of the PDF file
 * @returns Promise<{text: string} | null> - Parsed PDF text or null if parsing fails
 */
export const downloadAndParsePdfFromS3 = async (
  s3Url: string
): Promise<{ text: string } | null> => {
  try {
    const { s3Client } = awsConfig;

    // Extract bucket and key from S3 URL
    const url = new URL(s3Url);
    const bucket = CONFIG.s3_bucket;

    // Extract key from pathname (remove leading slash)
    const key = url.pathname.substring(1);

    console.log(`Downloading PDF from S3: bucket=${bucket}, key=${key}`);

    // Create GetObject command
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: key,
    });

    // Download the file
    const response = await s3Client.send(command);

    if (!response.Body) {
      console.error("No body in S3 response");
      return null;
    }

    // Convert stream to buffer
    const chunks: Uint8Array[] = [];
    const stream = response.Body as any;

    // eslint-disable-next-line no-restricted-syntax
    for await (const chunk of stream) {
      chunks.push(chunk);
    }

    const buffer = Buffer.concat(chunks);

    // Parse PDF with retries
    const pdfParsed = await parsePdfWithRetries(buffer);

    if (!pdfParsed || !pdfParsed.text || pdfParsed.text.trim() === "") {
      console.error("Failed to extract text from PDF or PDF is empty");
      return null;
    }

    console.log(
      `Successfully parsed PDF. Text length: ${pdfParsed.text.length}`
    );
    return pdfParsed;
  } catch (error) {
    console.error("Error downloading and parsing PDF from S3:", error);
    return null;
  }
};
