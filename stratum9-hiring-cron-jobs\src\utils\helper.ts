import OpenAI from "openai";
import { GPT_MODEL } from "./constants";
import s3Client, { getSecretKeys } from "../config/awsConfig";
import { ICreateNotification } from "./commonInterfaces";
import NotificationsModal from "../schema/notifications";
import UserModel from "../schema/s9/user";
import { DeleteObjectsCommand, ListObjectsV2Command } from "@aws-sdk/client-s3";
import envConfig from "../config/envConfig";
import ActivityLogModel from "../schema/activity_logs";
import dbConnection from "../dbConnection";

const CONFIG = envConfig();

export const getUserByUserId = async (userId: number) => {
  try {
    const userRepo = await dbConnection.getS9MainDatabaseRepository(UserModel);

    const user = await userRepo.findOne({ where: { id: userId } });
    return user;
  } catch (error) {
    console.error("getUserByUserId error ===>>>", error);
    return null;
  }
};

export const createNotification = async (
  orgId: number,
  userId: number,
  data: ICreateNotification
) => {
  const { type, title, description, relatedId, additionalInfo } = data;
  try {
    const notificationsRepo = await dbConnection.getRepository(
      NotificationsModal
    );
    const notification = new NotificationsModal();

    notification.organizationId = orgId;
    notification.userId = userId;
    notification.type = type;
    notification.title = title;
    notification.description = description;
    notification.relatedId = relatedId;
    notification.additionalInfo = additionalInfo;
    notification.isWatched = false;

    await notificationsRepo.save(notification);

    return {
      success: true,
      message: "Notification created successfully",
    };
  } catch (error) {
    console.error("createNotification error ===>>>", error);

    return {
      success: false,
      message: "Failed to create notification",
    };
  }
};

class OpenAiHelper {
  private static instance: OpenAiHelper;
  private openAiClient: OpenAI;

  private constructor() {
    getSecretKeys().then((keys) => {
      console.log("OpenAiHelper keys", keys);
      const OPENAI_API_KEY = keys.openai_api_key;
      this.openAiClient = new OpenAI({ apiKey: OPENAI_API_KEY });
    });
  }

  public static getInstance(): OpenAiHelper {
    if (!OpenAiHelper.instance) {
      OpenAiHelper.instance = new OpenAiHelper();
    }
    return OpenAiHelper.instance;
  }

  public getClient(): OpenAI {
    return this.openAiClient;
  }

  public generateQuestionForSkill = async (
    jobSkill: {
      id: number;
      type: string;
      title: string;
      shortDescription: string;
    }[],
    difficultyLevel: string
  ) => {
    try {
      const response = await this.openAiClient.chat.completions.create({
        model: GPT_MODEL,
        messages: [
          {
            role: "system",
            content: `You are an expert HR interviewer and question generator. Generate professional, insightful interview questions that assess candidates effectively.`,
          },
          {
            role: "user",
            content: `
          **Task**: Generate **3 interview questions per skill** (role-specific, culture-specific) and **1 question per skill** (career-based).  
          **Skill Data**:  
          ${JSON.stringify(jobSkill)}  
 
          **Rules**:  
          - For "role_specific" and "culture_specific" skills: Generate 3 questions per skill.  
          - For "career_based" skills: Generate 1 question per skill.  
          - Questions must be:  
            - Professional and clear  
            - 15-25 words per question  
            - Mix of behavioral, situational, and direct assessment types  
            - Tailored to the skill's description  
            - Match the specified difficulty level: "${difficultyLevel}"  
          - Total character limit per question: 500  
 
          **Output Format**:  
          {
            "questions": {
              "jobSkillId": [
                {
                  "questionText": "Question text here",
                  "difficultyLevel": "${difficultyLevel}",
                  "skillType": "career_based | role_specific | culture_specific"
                }
              ]
            }
          }  
        `,
          },
        ],
      });

      const responseContent = response.choices[0].message.content || "";

      // Extract JSON from the response by removing markdown formatting
      let jsonContent = responseContent;

      // Remove markdown code block indicators if present
      if (responseContent.includes("```json")) {
        jsonContent = responseContent.replace(/```json\n|\n```/g, "");
      } else if (responseContent.includes("```")) {
        jsonContent = responseContent.replace(/```\n|\n```/g, "");
      }

      return jsonContent;
    } catch (error) {
      console.error(
        "Error generating questions:generateQuestionForSkill",
        error
      );
      return "";
    }
  };

  public generateInterviewFeedback = async (
    jobSkill: { title: string; jobSkillId: number; score: number }[],
    transcript: string,
    behavioralNotes: string,
    skillQuestionsAndAnswers: {
      question: string;
      answer: string;
      type: string;
      jobSkillId: number;
    }[],
    careerBasedScore: number,
    JobDescription: string
  ) => {
    try {
      const response = await this.openAiClient.chat.completions.create({
        model: GPT_MODEL,
        messages: [
          {
            role: "system",
            content: `You are an expert HR interviewer and behavioral analyst specializing in comprehensive candidate evaluation. Your task is to generate professional, data-driven feedback based on interview performance, behavioral observations, and skill assessments.
        EVALUATION CRITERIA:
        - Technical competency and role-specific skills
        - Cultural fit and soft skills alignment
        - Career progression potential and growth mindset
        - Communication effectiveness and behavioral indicators
        - Overall interview performance consistency

        FEEDBACK STANDARDS:
        - Be specific and evidence-based
        - Focus on actionable insights
        - Maintain professional, constructive tone
        - Provide balanced assessment (strengths + areas for improvement)
        - Use behavioral observations to support evaluations`,
          },
          {
            role: "user",
            content: `
        **TASK**: Generate comprehensive interview feedback with exactly 10 highlights per skill category.

        **CONSTRAINTS**:
        - Each highlight: maximum 100 characters
        - Generate feedback for each skill in jobSkill using this priority:
          1. If interviewer's answer exists: use answer + score as primary source
          2. If answer is null: extract evaluation from transcript based on the question
        - For career-based feedback, set jobSkillId as null
        - Use transcript to provide context and validate interviewer assessments
        - Provide probability scores as integers (0-100)
        - Use JobDescription and skill score to calculate the success probability of each skill based on keywords.

        **INPUT DATA**:

        **Skills Assessment Data**:
        ${JSON.stringify(jobSkill)}
        // Structure: [{ jobSkillId, question, answer (by interviewer), type, score }]

        "**Interview Questions and Answers**":
        ${JSON.stringify(skillQuestionsAndAnswers)}

        **Job Description**:
        ${JobDescription}

        **Complete Interview Transcript**:
        ${transcript}
        // Contains full conversation between interviewer and candidate

        **Behavioral Analysis Notes**:
        ${behavioralNotes}

        **Career Progression Score**:
        ${careerBasedScore}

        **REQUIRED OUTPUT FORMAT**:
        \`\`\`json
        {
          "feedbacks": [
            {
              "jobSkillId": "string|null", // null for career-based feedback
              "skillType": "role|culture|career", // specify skill category
              "highlights": [
                "Evidence-based highlight 1 (max 100 chars)",
                "Evidence-based highlight 2 (max 100 chars)", 
                "Evidence-based highlight 3 (max 100 chars)",
                ...
              ],
              "strengths": [
                "Specific strength with supporting evidence",
                "Specific strength with supporting evidence",
                "Specific strength with supporting evidence"
              ],
              "potentialGaps": [
                "Identified gap with improvement suggestion",
                "Identified gap with improvement suggestion"
              ],
              "skillSuccessProbability": // acurate 
            }
          ],
          "interviewerFeedback": [
            "Professional insight for hiring manager (100 chars max)",
            "Professional insight for hiring manager (100 chars max)",
            "Professional insight for hiring manager (100 chars max)"
          ],
          "candidateFeedback": [
            "Constructive feedback for candidate growth (100 chars max)",
            "Constructive feedback for candidate growth (100 chars max)",
            "Constructive feedback for candidate growth (100 chars max)"
          ],
          "aiDecisionForNextRound": "Approved|Rejected",
        }
        \`\`\`

        **EVALUATION INSTRUCTIONS**:
        1. For each skill in skillData:
          - Check if interviewer's answer exists:
            * If answer provided: Use answer + score as primary assessment
            * If answer is null: Analyze transcript to find candidate's response to the question
          - Generate feedback based on the skill type (role/culture/career)
          - Cross-reference with behavioral notes when available
        2. When using transcript for null answers:
          - Locate the specific question in the conversation
          - Evaluate candidate's response quality and completeness
          - Assign appropriate assessment based on response analysis
        3. For career-based feedback, synthesize overall performance patterns
        4. Ensure highlights reflect specific evidence from chosen evaluation source
        5. Base final decision on combined assessments and behavioral observations
        6. Use JobDescription and skill score to calculate the success probability of each skill based on keywords.`,
          },
        ],
      });

      const responseContent = response.choices[0].message.content || "";

      // Extract JSON from the response by removing markdown formatting
      let jsonContent = responseContent;

      // Remove markdown code block indicators if present
      if (responseContent.includes("```json")) {
        jsonContent = responseContent.replace(/```json\n|\n```/g, "");
      } else if (responseContent.includes("```")) {
        jsonContent = responseContent.replace(/```\n|\n```/g, "");
      }

      return jsonContent;
    } catch (error) {
      console.error(
        "Error generating feedback:generateInterviewFeedback",
        error
      );
      return "";
    }
  };

  public generateInterviewFinalSummary = async (
    interviewSummaries: {
      interviewSummary: { highlight?: Array<String> };
      hardSkillMarks: number;
    }[],
    skillData: {
      jobSkillId: number;
      skillMarks: number;
      strengths: { strengths?: Array<String> };
      potentialsGaps: { potentialGaps?: Array<String> };
    }[],
    additionalInfo: { description: string },
    finalAssessment?: { overallSuccessProbability: number }
  ) => {
    try {
      const response = await this.openAiClient.chat.completions.create({
        model: GPT_MODEL,
        messages: [
          {
            role: "system",
            content: `You are an expert HR interviewer and question generator. Generate professional, insightful interview questions that assess candidates effectively.`,
          },
          {
            role: "user",
            content: `
          **Task**: Generate final summary of 3-6 highlights.

          **Skill Data**:  
          ${JSON.stringify(skillData)}

          **Interview Summaries**
          ${JSON.stringify(interviewSummaries)}

          **Final Assessment**
          ${JSON.stringify(finalAssessment)}

          **Candidate Additional Info**
          ${JSON.stringify(additionalInfo)}
 
          **Rules**:  
          - skillData contains skillMarks as score, strengths and potentialGaps for "role_specific" and "culture_specific" skills.
          - interviewSummaries contains summary of each interview in the highlights array of string and hardSkillMarks which is "career_based" score.
          - finalAssessment contains overallSuccessProbability as score.
          - overallGoodFit must be calculated using interview summaries + career based score + skill(strength,gaps and score).
          - if final assessment exist then use overallSuccessProbability for output generation.
          - if candidate additional info exist use that for output generation
 
          **Output Format**:  
          {
            "finalSummary": ["highlight1", "highlight2", ...], upto 6
            "overallGoodFit": integer (0-100),
            "improvementAreas": [{ title, description }] // 5 data
            "behaviouralScores": {
              Communication: 0-100,
              Relatability: 0-100,
              Integrity and ethics: 0-100,
              Perseverance: 0-100,
              Personal ownership: 0-100
            }
          }  
        `,
          },
        ],
      });

      const responseContent = response.choices[0].message.content || "";

      // Extract JSON from the response by removing markdown formatting
      let jsonContent = responseContent;

      // Remove markdown code block indicators if present
      if (responseContent.includes("```json")) {
        jsonContent = responseContent.replace(/```json\n|\n```/g, "");
      } else if (responseContent.includes("```")) {
        jsonContent = responseContent.replace(/```\n|\n```/g, "");
      }

      return jsonContent;
    } catch (error) {
      console.error(
        "Error generating final summary:generateInterviewFinalSummary",
        error
      );
      return "";
    }
  };
}

export default OpenAiHelper.getInstance();

export const mapDeletableS3Files = async (prefix: string) => {
  const listCommand = new ListObjectsV2Command({
    Bucket: CONFIG.s3_bucket,
    Prefix: prefix,
  });

  const listResponse = await s3Client.send(listCommand);
  console.log("listResponse================>", listResponse);

  if (!listResponse.Contents || listResponse.Contents.length === 0) {
    console.log(`No objects found in folder: ${prefix}`);
    return [];
  }

  const deleteObjects = listResponse.Contents.reduce((acc, obj) => {
    if (obj.Key && !obj.Key.endsWith(".mp4")) {
      acc.push({ Key: obj.Key });
    }
    return acc;
  }, []);

  console.log("Objects to delete:", deleteObjects);

  return deleteObjects;
};

export const deleteS3FolderFilesWithObjects = async (
  objectsToDelete: { Key: string }[]
) => {
  console.log("Objects to delete:", objectsToDelete);

  if (objectsToDelete.length === 0) {
    console.log(`No objects to delete`);
    return [];
  }
  const deleteCommand = new DeleteObjectsCommand({
    Bucket: CONFIG.s3_bucket,
    Delete: { Objects: objectsToDelete },
  });
  const deleteResponse = await s3Client.send(deleteCommand);

  console.log("deleteResponse==============>", deleteResponse);

  const deletedKeys = deleteResponse.Deleted?.map((obj) => obj.Key!) || [];

  console.log("Deleted keys:", deletedKeys);
  return deletedKeys;
};

export async function addActivityLog(params: {
  orgId: number;
  logType: string;
  userId: number;
  entityId: number;
  entityType?: string;
  oldValue?: string;
  newValue?: string;
  comments: string;
}) {
  try {
    const dataSource = await dbConnection.getDataSource();
    const activityLogRepo = dataSource.getRepository(ActivityLogModel);
    const activityLogs = new ActivityLogModel();

    activityLogs.orgId = params.orgId ?? null;
    activityLogs.logType = params.logType ?? null;
    activityLogs.userId = params.userId ?? null;
    activityLogs.entityId = params.entityId ?? null;
    activityLogs.entityType = params.entityType ?? null;
    activityLogs.oldValue = params.oldValue ?? null;
    activityLogs.newValue = params.newValue ?? null;
    activityLogs.comments = params.comments ?? null;

    await activityLogRepo.save(activityLogs);
    return true;
  } catch (error) {
    console.error("Error adding activity log:", error);
    
    return false;
  }
}
