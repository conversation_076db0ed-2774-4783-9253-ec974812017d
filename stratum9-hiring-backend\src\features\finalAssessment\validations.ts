import Jo<PERSON> from "joi";

// Validation schema for creating a new final assessment
export const createFinalAssessmentValidation = Joi.object({
  jobId: Joi.number().required().messages({
    "number.base": "job_id_must_be_number",
    "any.required": "job_id_required",
  }),
  jobApplicationId: Joi.number().required().messages({
    "number.base": "job_application_id_must_be_number",
    "any.required": "job_application_id_required",
  }),
  overallSuccessProbability: Joi.number().required().min(0).max(1).messages({
    "number.base": "overall_success_probability_must_be_number",
    "number.min": "overall_success_probability_min_value",
    "number.max": "overall_success_probability_max_value",
    "any.required": "overall_success_probability_required",
  }),
  skillSummary: Joi.object().required().messages({
    "object.base": "skill_summary_must_be_object",
    "any.required": "skill_summary_required",
  }),
  developmentRecommendations: Joi.object().required().messages({
    "object.base": "development_recommendations_must_be_object",
    "any.required": "development_recommendations_required",
  }),
  questions: Jo<PERSON>.array()
    .items(
      Joi.object({
        question: Joi.string().required().messages({
          "string.empty": "question_required",
          "any.required": "question_required",
        }),
        questionType: Joi.string().required().messages({
          "string.empty": "question_type_required",
          "any.required": "question_type_required",
        }),
        skillId: Joi.number().optional().allow(null),
        options: Joi.object().optional().allow(null),
        correctAnswer: Joi.string().optional().allow(null, ""),
        applicantAnswer: Joi.string().optional().allow(null, ""),
      })
    )
    .optional(),
});
export const getQuestionQueryValidation = Joi.object({
  finalAssessmentId: Joi.number().required().messages({
    "number.base": "final_assessment_id_must_be_number",
    "any.required": "final_assessment_id_required",
  }),
  jobId: Joi.number().required().messages({
    "number.base": "job_id_must_be_number",
    "any.required": "job_id_required",
  }),
  jobApplicationId: Joi.number().required().messages({
    "number.base": "job_application_id_must_be_number",
    "any.required": "job_application_id_required",
  }),
});

export const addManualQuestionValidation = Joi.object({
  finalAssessmentId: Joi.number().required().messages({
    "number.base": "final_assessment_id_must_be_number",
    "any.required": "final_assessment_id_required",
  }),
  question: Joi.string().required().messages({
    "string.empty": "question_required",
    "any.required": "question_required",
  }),
  questionType: Joi.string().required().messages({
    "string.empty": "question_type_required",
    "any.required": "question_type_required",
  }),
  skillId: Joi.number().required().messages({
    "number.base": "skill_id_must_be_number",
    "any.required": "skill_id_required",
  }),
  options: Joi.object().required().messages({
    "object.base": "options_must_be_object",
    "any.required": "options_required",
  }),
  correctAnswer: Joi.string().required().messages({
    "string.empty": "correct_answer_required",
    "any.required": "correct_answer_required",
  }),
});

export const finalAssessmentparamsIdValidation = Joi.object({
  finalAssessmentId: Joi.number().required().messages({
    "number.base": "final_assessment_id_must_be_number",
    "any.required": "final_assessment_id_required",
  }),
});

export const shareAssessmentValidation = Joi.object({
  finalAssessmentId: Joi.number().required().messages({
    "number.base": "final_assessment_id_must_be_number",
    "any.required": "final_assessment_id_required",
  }),
  assessmentLink: Joi.string().required().messages({
    "string.empty": "assessment_link_required",
    "any.required": "assessment_link_required",
  }),
  jobApplicationId: Joi.number().required().messages({
    "number.base": "job_application_id_must_be_number",
    "any.required": "job_application_id_required",
  }),
});

export const candidateAssessmentIdValidation = Joi.object({
  finalAssessmentId: Joi.string().required().messages({
    "string.base": "final_assessment_id_required",
    "any.required": "final_assessment_id_required",
  }),
});

export const getAssessmentStatusValidation = Joi.object({
  jobApplicationId: Joi.number().required().messages({
    "number.base": "job_application_id_must_be_number",
    "any.required": "job_application_id_required",
  }),
});

export const createFinalAssessmentByAiValidation = Joi.object({
  jobId: Joi.number().required().messages({
    "number.base": "job_id_must_be_number",
    "any.required": "job_id_required",
  }),
  jobApplicationId: Joi.number().required().messages({
    "number.base": "job_application_id_must_be_number",
    "any.required": "job_application_id_required",
  }),
});

export const verifyCandidateEmailValidation = Joi.object({
  email: Joi.string().email().required().messages({
    "string.email": "email_must_be_valid",
    "string.empty": "email_required",
    "any.required": "email_required",
  }),
  token: Joi.string().required().messages({
    "string.empty": "token_required",
    "any.required": "token_required",
  }),
});

export const generateAssessmentTokenValidation = Joi.object({
  finalAssessmentId: Joi.number().required().messages({
    "number.base": "final_assessment_id_must_be_number",
    "any.required": "final_assessment_id_required",
  }),
});

export default {
  createFinalAssessmentValidation,
  addManualQuestionValidation,
  finalAssessmentparamsIdValidation,
  shareAssessmentValidation,
  candidateAssessmentIdValidation,
  getAssessmentStatusValidation,
  createFinalAssessmentByAiValidation,
  verifyCandidateEmailValidation,
  generateAssessmentTokenValidation,
};
