"use client";
import React from "react";
import styles from "../../../styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import { useForm } from "react-hook-form";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
const ConductInterview = () => {
  const { control } = useForm();
  const [showChangeRoleModal, setShowChangeRoleModal] = React.useState(false);
  const roles = [
    "Operations Admin",
    "Data Analyst",
    "Project Manager",
    "UI/UX Designer",
    "DevOps Engineer",
    "Product Manager",
    "Marketing Specialist",
    "Business Analyst",
    "Cloud Solutions Architect",
  ];
  console.log("roles", showChangeRoleModal);
  return (
    <>
      <section className={styles.conduct_interview}>
        <div className="container">
          <div className="row">
            <div className="col-md-12">
              <div className="common-page-header">
                <div className="common-page-head-section">
                  <div className="main-heading">
                    <h2>
                      <BackArrowIcon
                        onClick={() => {
                          setShowChangeRoleModal(true);
                        }}
                      />
                      Conduct <span>Interview</span>
                    </h2>
                    <div className="button-align justify-content-end w-50">
                      <InputWrapper className="mb-0 w-100">
                        <div className="icon-align right">
                          <Textbox
                            className="form-control w-100"
                            control={control}
                            name="search"
                            type="text"
                            placeholder="Search using name, user role, department etc"
                          >
                            <InputWrapper.Icon>
                              <SearchIcon />
                            </InputWrapper.Icon>
                          </Textbox>
                        </div>
                      </InputWrapper>

                      <Button className="primary-btn rounded-md button-sm">Add Employees</Button>
                    </div>
                  </div>

                  <p className="description">Select a job title to see the candidates lined-up for further interview rounds.</p>
                </div>
              </div>
              <div className="inner-section">
                <ul className="role-list" aria-label="Job roles list">
                  {roles.map((role) => (
                    <li key={role} className="role-item " tabIndex={0}>
                      {role}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* {showChangeRoleModal && <ChangeRoleModal onClickCancel={() => setShowChangeRoleModal(false)} />} */}
    </>
  );
};

export default ConductInterview;
