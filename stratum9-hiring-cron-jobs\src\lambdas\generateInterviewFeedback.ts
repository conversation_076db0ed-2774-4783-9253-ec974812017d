import { Brackets } from "typeorm";
import dbConnection from "../dbConnection";
import FinalAssessmentsModel from "../schema/final_assessments";
import JobApplicationsModel from "../schema/job_applications";
import InterviewModel, { AIDecisionForNextRound } from "../schema/interview";
import InterviewFeedbackModel from "../schema/interview_feedback";
import InterviewSkillEvaluationModel from "../schema/interview_skill_evaluations";
import InterviewSkillQuestionsAnswersModel from "../schema/interview_skill_questions_answers";
import { SkillType } from "../schema/job_skills";
import SkillsModel from "../schema/skills";
import <PERSON>A<PERSON><PERSON>elper, { getUserByUserId, addActivityLog } from "../utils/helper";
import { NotificationType } from "../schema/notifications";
import NotificationServices from "../notification/services";
import { sendInterviewFeedbackEmail } from "../utils/interviewFeedbackEmail";
import { ACTIVITY_LOG_INTERVIEW_FEEDBACK, ACTIVITY_LOG_TYPE, ENTITY_TYPE } from "../utils/constants";

const generateInterviewFeedbackFunction = async (interviewId: number, notificationData: { orgId: number, scheduledBy: number, interviewerId: number }[], lambdaEvent) => {
    try {
        const interviewEvaluationRepo = await dbConnection.getRepository(
            InterviewSkillEvaluationModel
        )
        const InterviewSkillQuestionsAnswersRepo = await dbConnection.getRepository(
            InterviewSkillQuestionsAnswersModel
        )
        const interviewRepo = await dbConnection.getRepository(InterviewModel);
        const interviewFeedbackRepo = await dbConnection.getRepository(
            InterviewFeedbackModel
        );

        // Get interview and job details
        const interviewJobCandidateDetails = await interviewRepo.
            createQueryBuilder("interview")
            .leftJoinAndSelect("job_applications", "jobApplication", "jobApplication.id = interview.jobApplicationId")
            .leftJoinAndSelect("jobs", "job", "job.id = interview.jobId")
            .leftJoinAndSelect("candidates", "c", "c.id = jobApplication.candidateId")
            .where("interview.id = :interviewId", { interviewId })
            .select([
                "interview.id as id",
                "interview.hardSkillMarks as hardSkillMarks",
                "interview.transcriptText as transcriptText",
                "interview.applicantBehavioralNotes as applicantBehavioralNotes",
                "interview.jobApplicationId as jobApplicationId",
                "interview.scheduledBy as scheduledBy",
                "interview.interviewerId as interviewerId",
                "job.orgId as orgId",
                "job.finalJobDescription as finalJobDescription",
                "job.title as jobTitle",
                "c.name as candidateName"
            ])
            .getRawOne();

        console.log("interviewJobCandidateDetails=====>>>>>", interviewJobCandidateDetails)

        if (interviewJobCandidateDetails) {
            notificationData.push({
                orgId: interviewJobCandidateDetails.orgId,
                scheduledBy: interviewJobCandidateDetails.scheduledBy,
                interviewerId: interviewJobCandidateDetails.interviewerId,
            });
        }

        console.log("JobDescription:", interviewJobCandidateDetails?.finalJobDescription);

        // Get locked skills details
        const query = interviewEvaluationRepo
            .createQueryBuilder("evaluation")
            .leftJoinAndSelect(SkillsModel, "skills", "skills.id = evaluation.skillId")
            .where("evaluation.interviewId = :interviewId", { interviewId })
            .andWhere("evaluation.locked = true")
            .select(["skills.title as title", "evaluation.jobSkillId as jobSkillId", "evaluation.skillMarks as score"]);

        const allSkills = await query.getRawMany();

        const jobSkillsIds = allSkills.map(skill => skill.jobSkillId)

        console.log("Job skills IDs for feedback:", jobSkillsIds);

        // console.log("All skills for feedback:", allSkills);

        if (allSkills.length === 0) return;

        // Fetch skill questions and answers for the interview
        const qaQuery = InterviewSkillQuestionsAnswersRepo
            .createQueryBuilder("isqa")
            .leftJoinAndSelect("job_skills", "jobSkills", "jobSkills.id = isqa.jobSkillId")
            .where("isqa.jobApplicationId = :jobApplicationId", { jobApplicationId: interviewJobCandidateDetails.jobApplicationId })
            .andWhere(new Brackets(qb => {
                qb.where("jobSkills.type = :type AND isqa.interviewId = :interviewId", { type: SkillType.CAREER_BASED, interviewId: interviewJobCandidateDetails.id });
                if (jobSkillsIds.length > 0) {
                    qb.orWhere("isqa.jobSkillId IN (:...jobSkillsIds)", { jobSkillsIds });
                }
            }))
            .select(["isqa.question as question", "isqa.answer as answer", "jobSkills.id as jobSkillId", "jobSkills.type as type"]);


        const skillQuestionsAndAnswers = await qaQuery.getRawMany();

        // console.log("Skill questions and answers for feedback:", skillQuestionsAndAnswers);

        const result = await OpenAiHelper.generateInterviewFeedback(allSkills, interviewJobCandidateDetails.transcriptText, interviewJobCandidateDetails.applicantBehavioralNotes, skillQuestionsAndAnswers, interviewJobCandidateDetails.hardSkillMarks, interviewJobCandidateDetails?.finalJobDescription);

        const feedbackData = JSON.parse(result);

        console.log("Generated feedback data:", JSON.stringify(feedbackData, null, 2));

        if (!feedbackData) return;

        const feedbacksToInsert = [];
        const evaluationsToUpdate = [];

        if (feedbackData.feedbacks) {
            for (const feedback of feedbackData.feedbacks) {
                feedbacksToInsert.push({
                    interviewId,
                    jobSkillId: feedback.jobSkillId ? parseInt(feedback.jobSkillId, 10) : null,
                    highlights: { aiFeedback: feedback.highlights, overAllFeedback: feedback.highlights },
                });

                if (feedback.jobSkillId) {
                    evaluationsToUpdate.push({
                        jobSkillId: feedback.jobSkillId,
                        strengths: feedback.strengths,
                        potentialGaps: feedback.potentialGaps,
                        skillSuccessProbability: feedback.skillSuccessProbability,
                    });
                }
            }
        }

        if (feedbacksToInsert.length > 0) {
            const res = await interviewFeedbackRepo.save(feedbacksToInsert);

            if (res.length > 0) {
                const userInfo = await getUserByUserId(interviewJobCandidateDetails.interviewerId);
                console.log("interviewer email=====>>>>>", userInfo.email)
                sendInterviewFeedbackEmail({
                    interviewerEmail: userInfo.email,
                    interviewerName: `${userInfo.first_name ?? ""}${userInfo.last_name ?? ""}`,
                    candidateName: interviewJobCandidateDetails.candidateName,
                    jobTitle: interviewJobCandidateDetails.jobTitle,
                    interviewId,
                })

                // Add activity log for feedback generation
                await addActivityLog({
                    orgId: lambdaEvent.orgId ?? null,
                    logType: ACTIVITY_LOG_TYPE.INTERVIEW_FEEDBACK,
                    userId: lambdaEvent.userId ?? null,
                    entityId: interviewId,
                    entityType: ENTITY_TYPE.INTERVIEW_FEEDBACK_MODEL,
                    oldValue: null,
                    newValue: feedbackData.aiDecisionForNextRound ? ACTIVITY_LOG_INTERVIEW_FEEDBACK.POSITIVE : ACTIVITY_LOG_INTERVIEW_FEEDBACK.NEGATIVE,
                    comments: `Feedback added for ${interviewJobCandidateDetails.candidateName}.`
                });
            }

            console.log("Inserting feedbacks:", feedbacksToInsert);
            console.log("Feedbacks inserted successfully res", res);
        }

        // update inteview details
        const updateInterviewRes = await interviewRepo.update(interviewId, {
            aiDecisionForNextRound: feedbackData.aiDecisionForNextRound || AIDecisionForNextRound.PENDING,
            interviewerPerformanceNotes: { highlights: feedbackData.interviewerFeedback || {} },
            applicantAiBehavioralAnalysis: feedbackData.candidateFeedback.join(", ") || "",
        });

        console.log("Updated interview with AI decision:", updateInterviewRes);


        console.log("Evaluations to update:", evaluationsToUpdate);
        // update skill evaluations
        if (evaluationsToUpdate.length > 0) {
            for (const updates of evaluationsToUpdate) {
                const updateRes = await interviewEvaluationRepo.update({ jobSkillId: updates.jobSkillId, interviewId }, {
                    potentialsGaps: { potentialGaps: updates.potentialGaps },
                    strengths: { strengths: updates.strengths },
                    probabilityOfSuccessInThisSkill: { probabilityOfSuccessInSkill: updates.skillSuccessProbability },
                });
                console.log(`Updated evaluations for jobSkillId ${updates.jobSkillId}:`, updateRes);
            }
        }

        return true;

    } catch (error) {
        console.log("Error generating interview feedback:", error);
        return false
    }
}

export const generateInterviewFeedbackLambda = async (event, _context, _callback) => {
    console.log("generate interview feedback LambdaFunction event");

    try {
        const interviewRepo = await dbConnection.getRepository(InterviewModel);

        const result = await interviewRepo.
            createQueryBuilder("interview")
            .leftJoin(JobApplicationsModel, "jobApplication", "jobApplication.id = interview.jobApplicationId")
            .leftJoinAndSelect(InterviewFeedbackModel, "feedback", "feedback.interviewId = interview.id")
            .leftJoinAndSelect(FinalAssessmentsModel, "finalAssessment", "finalAssessment.jobApplicationId = jobApplication.id")
            .where("TIMESTAMPDIFF(MINUTE, interview.interview_end_time, NOW()) > :diff", { diff: 30 }) // 30 mins
            .andWhere("finalAssessment.id IS NULL")
            .andWhere("feedback.id IS NULL")
            .select(["interview.id as id"])
            .getRawMany();

        console.log("Interviews to process:", result);

        const interviewIds = new Set(result.map((interview) => interview.id));

        console.log("Interviews to process for feedback:", interviewIds);

        const notificationData = [];
        for (const interviewId of interviewIds) {
            await generateInterviewFeedbackFunction(interviewId, notificationData, event);
        }

        const schedulerNotifications = new Map();
        const interviewerNotifications = new Map();

        console.log("====notificationData", notificationData)

        notificationData.forEach((item) => {
            // for uniqueness
            const schedulerKey = `${item.orgId}-${item.scheduledBy}`;
            const interviewerKey = `${item.orgId}-${item.interviewerId}`;

            if (!schedulerNotifications.has(schedulerKey)) {
                schedulerNotifications.set(schedulerKey, true);
                NotificationServices.createNotification(item.orgId, item.scheduledBy, {
                    type: NotificationType.INTERVIEW_FEEDBACK_PENDING,
                    title: NotificationType.INTERVIEW_FEEDBACK_PENDING,
                    description: "Interview Feedback Pending",
                    relatedId: item.interviewerId,
                });
            }

            if (!interviewerNotifications.has(interviewerKey)) {
                interviewerNotifications.set(interviewerKey, true);
                NotificationServices.createNotification(item.orgId, item.interviewerId, {
                    type: NotificationType.INTERVIEW_FEEDBACK_PENDING,
                    title: NotificationType.INTERVIEW_FEEDBACK_PENDING,
                    description: "Interview Feedback Pending",
                    relatedId: item.interviewerId,
                });
            }
        });

        return {
            success: true,
            message: "success",
        };
    } catch (error) {
        console.error("Error generating interview feedback:", error);
        return {
            success: false,
        };
    }
}