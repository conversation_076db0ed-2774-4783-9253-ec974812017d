import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  verifyOtpValidation,
  forgotPasswordValidation,
  resetPasswordValidation,
  signInValidation,
  resendOtpValidation,
  updateTimeZoneValidation,
  deleteSessionValidation,
} from "./vaildation";
import {
  paramsValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import {
  login,
  forgotPassword,
  verifyOtp,
  resetPassword,
  resendOtp,
  deleteSession,
  updateTimeZone,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";

const authRoutes = express.Router();

authRoutes.post(
  ROUTES.AUTH.RESEND_OTP,
  schemaValidation(resendOtpValidation),
  HandleErrors(resendOtp)
);

/**
 * @swagger
 * /auth/sign-in:
 *   post:
 *     summary: User Signin
 *     tags:
 *       - Auth Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: password
 *               type:
 *                 type: string
 *                 example: user
 *
 *             required:
 *               - email
 *               - password
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: login_successful
 *                  data:
 *                    type: object
 *                    properties:
 *                      token:
 *                        type: string
 *                        example: GtwM8LfoigDSDgj21VZgBI1R0H4iOATWU7HHiezRZ4U=
 *                      userData:
 *                        type: object
 *                        properties:
 *                          id:
 *                            type: number
 *                            example: 21
 *                          account_type:
 *                            type: string
 *                            example: user
 *                          first_name:
 *                            type: string
 *                            example: Tom
 *                          last_name:
 *                            type: string
 *                            example: Cruise
 *                          gender:
 *                            type: string
 *                            example: male
 *                          email:
 *                            type: string
 *                            example: <EMAIL>
 *                          phone:
 *                            type: string
 *                            example: **********
 *                          password:
 *                            type: string
 *                            example: $2a$08$BAJAhD4zAfqrO4E5vzmhL.ysSf8Xek28JoDZXIln4W2qWbLC7fTzm
 *                          age:
 *                            type: number
 *                            example: 22
 *                          assessment_completed:
 *                            type: boolean
 *                            example: true
 *                          image:
 *                            type: string
 *                            example: GtwM8LfoigDSDgj21VZgBI1R0H4iOATWU7HHiezRZ4U=
 *                          dob:
 *                            type: string
 *                            example: null
 *                          created_ts:
 *                            type: string
 *                            example: 2023-07-10T06:55:59.000Z
 *                          updated_ts:
 *                            type: string
 *                            example: 2023-07-10T06:55:59.000Z
 *                          address:
 *                            type: object
 *                            properties:
 *                              id:
 *                                type: number
 *                                example: 77
 *                              user_id:
 *                                type: number
 *                                example: 209
 *                              occupation:
 *                                type: string
 *                                example: Engineer
 *                              contact_number:
 *                                type: string
 *                                example: 4442223355
 *                              state:
 *                                type: string
 *                                example: Rajasthan
 *                              city:
 *                                type: string
 *                                example: Jaipur
 *                              created_ts:
 *                                type: string
 *                                example: 2023-07-10T06:55:59.000Z
 *                              updated_ts:
 *                                type: string
 *                                example: 2023-07-10T06:55:59.000Z
 *
 *       401:
 *          description: Bad Request
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 *
 */
authRoutes.post(
  ROUTES.AUTH.SIGN_IN,
  schemaValidation(signInValidation),
  HandleErrors(login)
);

/**
 * @swagger
 * /auth/verify-otp:
 *   post:
 *     summary: Verfiy OTP
 *     tags:
 *       - Auth Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstname:
 *                 type: string
 *                 firstname: First Name
 *                 example: Davis
 *               lastname:
 *                 type: string
 *                 lastname: Last Name
 *                 example: Abc
 *               email:
 *                 type: string
 *                 email: Email
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 password: Password
 *                 example: password
 *               occupation:
 *                 type: string
 *                 occupation: Occupation
 *                 example: Engineer
 *               city:
 *                 type: string
 *                 city: City
 *                 example: Jaipur
 *               state:
 *                 type: string
 *                 state: State
 *                 example: Rajasthan
 *               gender:
 *                 type: string
 *                 gender: Gender
 *                 example: Male/Female
 *               phone:
 *                 type: string
 *                 phone: Contact No
 *                 example: 5656545425
 *               otp:
 *                 type: string
 *                 otp: OTP
 *                 example: 8955
 *               generatedAt:
 *                 type: number
 *             required:
 *               - firstname
 *               - lastname
 *               - email
 *               - password
 *               - occupation
 *               - city
 *               - state
 *               - gender
 *               - otp
 *               - generatedAt
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: signup_successful
 *
 *       401:
 *          description: Bad Request
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 *
 */
authRoutes.post(
  ROUTES.AUTH.VERIFY_OTP,
  schemaValidation(verifyOtpValidation),
  HandleErrors(verifyOtp)
);

/**
 * @swagger
 * /auth/forgot-password:
 *   post:
 *     summary: Forgot Password
 *     tags:
 *       - Auth Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               phone:
 *                 type: string
 *                 example: 5656545425
 *               type:
 *                 type: string
 *                 example: user
 *
 *             required:
 *               - phone
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: success
 *                  data:
 *                    type: object
 *                    properties:
 *                      otp:
 *                        type: string
 *                        example: 8955
 *                      generatedAt:
 *                        type: number
 *
 *       401:
 *          description: Bad Request
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 *
 */
authRoutes.post(
  ROUTES.AUTH.FORGOT_PASSWORD,
  schemaValidation(forgotPasswordValidation),
  HandleErrors(forgotPassword)
);

authRoutes.post(
  ROUTES.AUTH.RESET_PASSWORD,
  schemaValidation(resetPasswordValidation),
  HandleErrors(resetPassword)
);

authRoutes.delete(
  ROUTES.AUTH.DELETE_SESSION,
  paramsValidation(deleteSessionValidation),
  HandleErrors(deleteSession)
);

authRoutes.post(
  ROUTES.AUTH.UPDATE_TIMEZONE,
  auth,
  schemaValidation(updateTimeZoneValidation),
  HandleErrors(updateTimeZone)
);

export default authRoutes;
