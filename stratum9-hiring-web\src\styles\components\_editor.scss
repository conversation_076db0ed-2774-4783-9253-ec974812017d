@use "../abstracts" as *;

// SunEditor Skeleton Loader Styles
.editor-skeleton {
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  width: 100%;
  min-height: 400px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;

  .skeleton-toolbar {
    padding: 10px;
    display: flex;
    gap: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    position: absolute;
    bottom: 0;
    width: 100%;

    .skeleton-button {
      height: 24px;
      width: 30px;
      background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 3px;
    }
  }

  .skeleton-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: calc(100% - 45px);

    .skeleton-paragraph {
      height: 18px;
      width: 100%;
      background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 3px;

      &:nth-child(2) {
        width: 90%;
      }

      &:nth-child(3) {
        width: 85%;
      }

      &:nth-child(4) {
        width: 95%;
      }

      &:nth-child(5) {
        width: 80%;
      }
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
