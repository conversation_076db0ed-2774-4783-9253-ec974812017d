import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

/**
 * Organization Subscription Benefits Model
 * Tracks the benefit limits for each organization based on their subscription
 */
@Entity("organization_subscription_benefits")
class OrganizationSubscriptionBenefitModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "organization_id", nullable: false })
  organizationId: number;

  @Column({ name: "job_postings", nullable: true, default: 0 })
  jobPostings: number;

  @Column({ name: "resume_screening", nullable: true, default: 0 })
  resumeScreening: number;

  @Column({ name: "manual_resume_upload", nullable: true, default: 0 })
  manualResumeUpload: number;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}

export default OrganizationSubscriptionBenefitModel;
