/**
 * Agora Recording Controller
 * Handles HTTP requests for Agora Cloud Recording operations
 */
import { Request, Response } from "express";
import { RecordingMode } from "./interface";
import AgoraRecordingService from "./services";
import generateAgoraToken from "../../utils/agoraTokenUtils";

/**
 * Start recording for a meeting
 * @param req Request with meetingId in params, optional token in body
 * @param res Response
 */
export const startMeetingRecording = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { channelName, interviewId } = req.body;

    // Generate or use provided recording UID
    const recordingUid = Math.floor(100000 + Math.random() * 900000);

    // Acquire resource ID from Agora
    const resourceId = await AgoraRecordingService.acquireResourceId(
      channelName,
      recordingUid
    );
    console.log("Resource ID acquired successfully:", resourceId);

    // generate token
    const token = await generateAgoraToken(
      interviewId as string,
      channelName as string,
      recordingUid
    );
    // Start recording with the acquired resource
    const mode = RecordingMode.MIX; // Default to composite mode
    const recordingResponse = await AgoraRecordingService.startRecording(
      resourceId,
      channelName,
      recordingUid,
      token.token,
      +interviewId,
      mode
    );

    res.status(200).json({
      ...recordingResponse,
      code: 200,
    });
  } catch (error) {
    console.error("Error starting meeting recording:", error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Stop recording for a meeting
 * @param req Request with meetingId in params
 * @param res Response
 */
export const stopMeetingRecording = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { resourceId, sid, mode, channelName, uid } = req.body;

    // Stop the recording
    const stopResponse = await AgoraRecordingService.stopRecording(
      resourceId,
      sid,
      channelName,
      uid,
      mode as RecordingMode
    );

    // Update recording status

    res.status(200).json({
      ...stopResponse,
    });
  } catch (error) {
    console.error(
      "Error stopping meeting recording:",
      (error as Error).message
    );
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const createAgoraToken = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      interviewId,
      personType,
      channelName,
      candidateEmail,
      candidateId,
      interviewerId,
    } = req.query;
    console.log("req.query", req.query);

    const tokenData = await AgoraRecordingService.createAgoraToken(
      interviewId as string,
      personType as string,
      channelName as string,
      candidateId as string,
      interviewerId as string,
      candidateEmail as string
    );
    res.status(200).json({
      ...tokenData,
    });
  } catch (error) {
    console.error("Error creating Agora token:", error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
