"use client";
import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Button from "@/components/formElements/Button";
import { getCurrentSubscription } from "@/services/subscription";
import { useTranslations } from "next-intl";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { useDispatch, useSelector } from "react-redux";
import { setCurrentPlan } from "@/redux/slices/authSlice";
import { RootState } from "@/redux/store";
import ROUTES from "@/constants/routes";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

// Utility function to format date in MM/DD/YYYY format
const formatDate = (date: string) => {
  const d = new Date(date);
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const day = d.getDate().toString().padStart(2, "0");
  const year = d.getFullYear();
  return `${month}/${day}/${year}`;
};

// Subscription Success Page Component
const SubscriptionSuccess = () => {
  const router = useRouter();
  const t = useTranslations();
  const dispatch = useDispatch();

  const [loading, setLoading] = useState<boolean>(true);
  const [countdown, setCountdown] = useState<number>(10);

  const myCurrentPlan = useSelector((state: RootState) => state.auth.currentPlan);
  const initialFetchDone = useRef(false);

  // Fetch subscription details on component mount
  useEffect(() => {
    if (initialFetchDone.current) return;
    getSubscriptionDetails();
    initialFetchDone.current = true;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Countdown timer for automatic navigation
  useEffect(() => {
    if (loading) return; // Don't start countdown while loading

    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      // Navigate to dashboard when countdown reaches 0
      router.push(ROUTES.PROFILE.MY_PROFILE);
    }
  }, [countdown, loading, router]);

  const getSubscriptionDetails = async () => {
    try {
      setLoading(true);
      const response = await getCurrentSubscription();

      if (response?.data?.success && response.data.data) {
        dispatch(setCurrentPlan(response.data.data));
      } else {
        const errorMessage = response?.data?.message || "failed_to_fetch_subscription_data";
        toastMessageError(t(errorMessage));
      }
    } catch (error) {
      console.error("Error fetching subscription details:", error);
      toastMessageError(t("error_retrieving_subscription"));
    } finally {
      setLoading(false);
      toastMessageSuccess(t("subscription_thank_you"));
    }
  };

  // Navigate to dashboard
  const handleGoToDashboard = () => {
    router.replace(ROUTES.DASHBOARD);
  };

  // Navigate to view subscription details
  const handleViewSubscription = () => {
    router.replace(ROUTES.PROFILE.MY_PROFILE);
  };

  return (
    <div className="subscription-successful-page">
      <div className="subscription-successful">
        <div className="success-icon">
          <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="40" cy="40" r="40" fill="#4BB543" />
            <path d="M56.6668 30L33.3335 53.3333L23.3335 43.3333" stroke="white" strokeWidth="6" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </div>
        <h1 className="subscription-successful-title">Subscription Successful!</h1>
        <div className="subscription-details">
          {myCurrentPlan?.subscriptionPlanId ? (
            <>
              <p className="mb-4">Your subscription has been successfully activated. Enjoy premium features!</p>
              <div className="subscription-card">
                <h3 className="mb-3">Subscription Details</h3>
                <h2 className="subscription-success-title">
                  {loading ? <Skeleton height={18} width={120} borderRadius={6} /> : myCurrentPlan?.subscriptionPlanName}
                </h2>
                <div className="subscription-success-info">
                  <div>
                    <strong>Start Date:</strong>{" "}
                    {loading ? (
                      <Skeleton height={16} width={100} borderRadius={6} />
                    ) : myCurrentPlan.startDate ? (
                      formatDate(myCurrentPlan.startDate)
                    ) : (
                      "N/A"
                    )}
                  </div>
                  <div>
                    <strong>Next Billing Date:</strong>{" "}
                    {loading ? (
                      <Skeleton height={16} width={100} borderRadius={6} />
                    ) : myCurrentPlan.nextBillingDate ? (
                      formatDate(myCurrentPlan.nextBillingDate)
                    ) : (
                      "N/A"
                    )}
                  </div>
                  <div>
                    <strong>Amount Paid:</strong> {loading ? <Skeleton height={16} width={100} borderRadius={6} /> : `$${myCurrentPlan.price}`}
                  </div>
                </div>
              </div>
            </>
          ) : (
            <p className="mb-4">Your subscription has not been activated. Please try again.</p>
          )}
        </div>

        <div className="subscription-successful-buttons">
          <Button className="primary-btn rounded-md" onClick={handleGoToDashboard}>
            Go to Dashboard
          </Button>
          <Button className="dark-outline-btn rounded-md" onClick={handleViewSubscription}>
            View Subscription
          </Button>
        </div>
        {!loading && (
          <p className="mt-4 text-center text-danger">
            <strong>
              You will be redirected to profile in {countdown} second{countdown !== 1 ? "s" : ""}...
            </strong>
          </p>
        )}
      </div>
    </div>
  );
};

export default SubscriptionSuccess;
