/**
 * Agora Recording Services
 * Handles all interactions with the Agora Cloud Recording API and Speech-to-Text API
 */
import axios, { AxiosError } from "axios";
import {
  AcquireResourceRequest,
  AcquireResourceResponse,
  RecordingMode,
  StartRecordingRequest,
  StopRecordingRequest,
  StreamTypes,
  ChannelType,
} from "./interface";
import {
  agoraApiClient,
  getAgoraApiBaseUrl,
  getS3StorageConfig,
} from "../../utils/agoraUtils";
import generateAgoraToken from "../../utils/agoraTokenUtils";
import dbConnection from "../../db/dbConnection";
import InterviewModel from "../../schema/s9-innerview/interview";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import {
  AGORA_API_STATUS,
  AGORA_USER_TYPE,
  API_RESPONSE_MSG,
  REDIS_KEYS,
} from "../../utils/constants";
import Cache from "../../db/cache";
import { getSecretKeys } from "../../config/awsConfig";

class AgoraRecordingService {
  /**
   * Acquire a resource ID from Agora Cloud Recording API
   * @param channelName Channel name where recording will happen
   * @param recordingUid A unique ID for the recording service
   * @returns Resource ID that's valid for 5 minutes
   */
  static acquireResourceId = async (
    channelName: string,
    recordingUid: number
  ): Promise<string> => {
    try {
      const client = await agoraApiClient();
      const baseUrl = await getAgoraApiBaseUrl();

      const requestBody: AcquireResourceRequest = {
        cname: channelName,
        uid: recordingUid.toString(),
        clientRequest: {
          scene: 0,
          resourceExpiredHour: 24,
        },
      };

      const response = await client.post<AcquireResourceResponse>(
        `${baseUrl}/cloud_recording/acquire`,
        requestBody
      );

      return response?.data?.resourceId || "";
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        console.error(
          "Agora acquire resource error:",
          axiosError.response?.data
        );
      }
      return "";
    }
  };

  /**
   * Start recording in a channel
   * @param resourceId Resource ID acquired from Agora
   * @param channelName Channel name to record
   * @param recordingUid Unique ID for the recording service
   * @param token Optional token for secure channels
   * @param mode Recording mode (mix or individual)
   * @returns Object containing resourceId and sid (session ID)
   */
  static startRecording = async (
    resourceId: string,
    channelName: string,
    recordingUid: number,
    token?: string,
    interviewId?: number,
    mode: RecordingMode = RecordingMode.MIX
  ) => {
    try {
      const client = await agoraApiClient();
      const baseUrl = await getAgoraApiBaseUrl();

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interviewData = await interviewRepo.findOne({
        where: { channelName, id: interviewId },
      });

      if (!interviewData) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_doesnt_exist,
        };
      }

      const s3Config = getS3StorageConfig(
        interviewData.jobId,
        interviewData.jobApplicationId,
        interviewId
      );

      // Build request for starting the recording
      const requestBody: StartRecordingRequest = {
        cname: channelName,
        uid: recordingUid.toString(),
        clientRequest: {
          token,
          recordingConfig: {
            streamTypes: StreamTypes.AUDIO_AND_VIDEO,
            channelType: ChannelType.COMMUNICATION,
            maxIdleTime: 300,
            extensionParams: {
              enableNTPtimestamp: true,
            },
            transcodingConfig: {
              width: 1280, // Higher resolution
              height: 720,
              fps: 24,
              bitrate: 1500,
              mixedVideoLayout: 1, // 0: default, 1: vertical, 2: horizontal, 3: grid
            },
            format: ["mp4"],
          },
          recordingFileConfig: {
            avFileType: ["hls", "mp4"],
          },
          storageConfig: s3Config,
        },
      };

      // Send request to start recording
      const response = await client.post(
        `${baseUrl}/cloud_recording/resourceid/${resourceId}/mode/${mode}/start`,
        requestBody
      );
      console.log("response data start recording========>", response.data);
      if (!response.data.sid) {
        return {
          success: false,
          message: API_RESPONSE_MSG.failed_to_start_recording,
        };
      }
      return {
        success: true,
        message: API_RESPONSE_MSG.recording_started,
        data: {
          resourceId,
          sid: response.data.sid,
          uid: recordingUid,
          mode,
          channelName,
        },
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        console.error(
          "Agora start recording error:",
          axiosError.response?.data
        );
        return {
          success: false,
          message: API_RESPONSE_MSG.failed,
        };
      }
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * Stop an active recording
   * @param resourceId Resource ID for the recording
   * @param sid Session ID for the recording
   * @param channelName Channel name being recorded
   * @param recordingUid Unique ID for the recording service
   * @param mode Recording mode (mix or individual)
   * @returns Object containing details about the stopped recording and file URLs
   */
  static stopRecording = async (
    resourceId: string,
    sid: string,
    channelName: string,
    recordingUid: string,
    mode: RecordingMode = RecordingMode.MIX
  ) => {
    try {
      const client = await agoraApiClient();
      const baseUrl = await getAgoraApiBaseUrl();
      const keys = await getSecretKeys();

      // Build request for stopping the recording
      const requestBody: StopRecordingRequest = {
        cname: channelName,
        uid: recordingUid.toString(),
        clientRequest: {
          async_stop: false,
        },
      };
      console.log("Stopping recording with request body:", requestBody);
      // Send request to stop recording
      const response = await client.post(
        `${baseUrl}/cloud_recording/resourceid/${resourceId}/sid/${sid}/mode/${mode}/stop`,
        requestBody
      );
      if (
        response.data.serverResponse.uploadingStatus ===
        AGORA_API_STATUS.UPLOADED
      ) {
        console.log(
          "recording url response====>",
          response.data.serverResponse.fileList[1].fileName
        );
        const bucketRecordingUrl = `${keys.s3_bucket_url_s9_innerview}${response.data.serverResponse.fileList[1].fileName}`;
        const interviewRepo =
          await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

        const interviewData = await interviewRepo.findOne({
          where: { channelName },
        });

        if (!interviewData) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_doesnt_exist,
          };
        }

        console.log("interviewData", interviewData);

        const recordingUrls = interviewData.recordingUrl
          ? [...interviewData.recordingUrl.recordingUrls, bucketRecordingUrl]
          : [bucketRecordingUrl];

        console.log("recordingUrls", recordingUrls);

        const res = await interviewRepo.update(interviewData.id, {
          recordingUrl: { recordingUrls },
        });
        console.log("res", res);
      }
      return {
        success: false,
        message: API_RESPONSE_MSG.recording_stopped,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        console.error("Agora stop recording error:", axiosError.response?.data);
        return {
          success: false,
          message: API_RESPONSE_MSG.failed,
        };
      }
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * Create a new Agora token for a specific interviewer
   * @param interviewId ID of the interview
   * @returns Token data including uid, channelName, and token
   */
  static createAgoraToken = async (
    interviewId: string,
    personType: string,
    channelName: string,
    candidateId?: string,
    interviewerId?: string,
    candidateEmail?: string
  ) => {
    try {
      const cache = new Cache();
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const interviewData = await interviewRepo.findOne({
        where: {
          id: parseInt(interviewId, 10),
          channelName,
        },
      });
      // If no matching interview is found
      if (!interviewData) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_doesnt_exist,
        };
      }
      const uid = Math.floor(100000 + Math.random() * 900000);
      let tokenData;
      if (personType === AGORA_USER_TYPE.Interviewer) {
        const interviewerKey = REDIS_KEYS.INTERVIEW_INTERVIEWER_KEY.replace(
          "{interviewId}",
          interviewId
        ).replace("{interviewerId}", interviewerId);
        const isMeetingInProgress = !!(await cache.get(interviewerKey));
        console.log("interviewerKey", interviewerKey);
        console.log("isMeetingInProgress", isMeetingInProgress);
        if (isMeetingInProgress) {
          return {
            success: false,
            message: API_RESPONSE_MSG.meeting_already_in_progress,
          };
        }
        tokenData = await generateAgoraToken(interviewId, channelName, uid);
      } else {
        const candidateKey = REDIS_KEYS.INTERVIEW_CANDIDATE_KEY.replace(
          "{interviewId}",
          interviewId
        ).replace("{candidateId}", candidateId);
        console.log("candidateKey", candidateKey);
        const isMeetingInProgress = !!(await cache.get(candidateKey));
        console.log("isMeetingInProgress", isMeetingInProgress);
        if (isMeetingInProgress) {
          return {
            success: false,
            message: API_RESPONSE_MSG.meeting_already_in_progress,
          };
        }
        const candidateRepo =
          await dbConnection.getS9InnerViewDatabaseRepository(CandidatesModel);

        const candidateData = await candidateRepo
          .createQueryBuilder("c")
          .innerJoin("job_applications", "ja", "c.id = ja.candidate_id")
          .innerJoin("interview", "i", "i.job_application_id = ja.id")
          .where("i.id = :interviewId", { interviewId })
          .andWhere("i.is_ended = :isEnded", { isEnded: false })
          .andWhere("c.email = :email", { email: candidateEmail })
          .select("c.email")
          .getOne();

        if (!candidateData) {
          return {
            success: false,
            message: API_RESPONSE_MSG.invalid_email,
          };
        }

        tokenData = await generateAgoraToken(interviewId, channelName, uid);
      }

      return {
        success: true,
        message: API_RESPONSE_MSG.token_generated_successfully,
        data: {
          interviewId: parseInt(interviewId, 10),
          channelName,
          uid,
          token: tokenData.token,
        },
      };
    } catch (error) {
      console.error("Error creating Agora token:", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };
}

export default AgoraRecordingService;
