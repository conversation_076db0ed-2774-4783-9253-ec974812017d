import React, { useState, useEffect, useRef } from "react";

import <PERSON><PERSON> from "lottie-react";
import Image from "next/image";

import { useForm, SubmitHandler } from "react-hook-form";
import { useSearchParams } from "next/navigation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useTranslations } from "next-intl";

import {
  LocalUser,
  RemoteUser,
  useJoin,
  useLocalCameraTrack,
  useLocalMicrophoneTrack,
  usePublish,
  useRemoteUsers,
  useConnectionState,
  useRTCClient,
} from "agora-rtc-react";

import Button from "@/components/formElements/Button";
import NextArrowIcon from "@/components/svgComponents/NextArrowIcon";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import Loader from "@/components/loader/Loader";

import { getAgoraToken } from "@/services/agoraService";
import { AGORA_AUDIO_CONSTRAINTS, AGORA_CONNECTION_STATE, AGORA_USER_TYPE, SOCKET_ROUTES } from "@/constants/commonConstants";
import { AgoraTokenData } from "@/interfaces/agoraInterfaces";

import { decryptInfo, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { EmailValidation } from "@/validations/authValidations";

import logo from "../../../../public/assets/images/logo.svg";
import LeaveIcon from "@/components/svgComponents/LeaveIcon";
import candidateInterview from "../../../../public/assets/images/candidate-Interview.json";
import style from "../../../styles/conductInterview.module.scss";
import VideoCallIcon from "@/components/svgComponents/VideoCallIcon";
import MicIcon from "@/components/svgComponents/MicIcon";
import { io, Socket } from "socket.io-client";
import toast from "react-hot-toast";

const CandidateJoin = () => {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const encryptedParams = searchParams?.get("params");

  const [params, setParams] = useState<{
    interviewId: string;
    channelName: string;
    candidateName: string;
    interviewerName: string;
    candidateId: string;
  }>({
    interviewId: "",
    channelName: "",
    candidateName: "",
    interviewerName: "",
    candidateId: "",
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [tokenData, setTokenData] = useState<AgoraTokenData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error] = useState("");
  const [joining, setJoining] = useState(false);
  const [isCameraOn, setIsCameraOn] = useState(false);
  const [isMicOn, setIsMicOn] = useState(true); // Start with mic ON to avoid issues
  const socketRef = useRef<Socket | null>(null);

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<{ email: string }>({ resolver: yupResolver(EmailValidation(t)) });

  // Only join after form submission and token retrieval
  const joinOptions = tokenData
    ? {
        appid: process.env.NEXT_PUBLIC_AGORA_APP_ID!,
        channel: tokenData.channelName,
        token: tokenData.token,
        uid: tokenData.uid,
      }
    : { appid: "", channel: "", token: "", uid: "" };

  const { localMicrophoneTrack } = useLocalMicrophoneTrack(isMicOn, AGORA_AUDIO_CONSTRAINTS);
  const { localCameraTrack } = useLocalCameraTrack(isCameraOn);
  useJoin(joinOptions, joining && !!tokenData);
  usePublish([localMicrophoneTrack, localCameraTrack]);
  const remoteUsers = useRemoteUsers();
  const connectionState = useConnectionState();
  const client = useRTCClient();

  console.log("client########=======>>>>>candidate", client);
  console.log("remoteUsers#########=====>>>>>candidate", remoteUsers);
  console.log("joinOptions######", joinOptions);

  useEffect(() => {
    if (encryptedParams) {
      try {
        console.log("encryptedParams", encryptedParams);
        const params = decryptInfo(encryptedParams);
        console.log("params", params);
        const parsedParams = JSON.parse(params);
        console.log("parsedParams", parsedParams);
        setParams(parsedParams);
      } catch (error) {
        console.error("Error decrypting params:", error);
        toastMessageError(t("invalid_invitation_link"));
      }
    }
  }, [encryptedParams]);

  useEffect(() => {
    toast.dismiss();
    if (tokenData?.token && (connectionState === AGORA_CONNECTION_STATE.DISCONNECTED || connectionState === AGORA_CONNECTION_STATE.DISCONNECTING)) {
      toastMessageError(connectionState);
    } else if (tokenData?.token) {
      toastMessageSuccess(connectionState);
    }
  }, [connectionState]);

  console.log("channelName,,,,,,,,,,,,,", params.channelName);

  const connectSocket = (candidateId: string, interviewId: string) => {
    if (socketRef.current) {
      socketRef.current.disconnect(); // Disconnect the previous socket connection
    }

    // Initialize the socket with the new token
    socketRef.current = io(process.env.NEXT_PUBLIC_SOCKET_CONNECTION_URL!, {
      path: SOCKET_ROUTES.CANDIDATE_CONDUCT_INTERVIEW,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      query: {
        interviewId,
      },
    });

    // Add socket event listeners
    socketRef.current?.on("connect", () => {
      console.log("Connected to server");
      if (socketRef.current) {
        socketRef.current.emit("candidate_joined", {
          candidateId,
        });
      }
    });

    socketRef.current?.on("disconnect", (reason, details) => {
      console.log("Disconnected from server:", reason, details);
    });

    socketRef.current?.on("connect_error", (error) => {
      console.log("Connect error:", error);
    });
  };

  useEffect(() => {
    if (tokenData?.token && params.candidateId && params.interviewId) {
      connectSocket(params.candidateId, params.interviewId);
    }

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [tokenData?.token, params.candidateId, params.interviewId]);

  // send message to server every 10 seconds
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (socketRef.current?.connected && tokenData?.token && params.candidateId) {
      interval = setInterval(() => {
        if (socketRef.current) {
          socketRef.current.emit("message", {
            candidateId: params.candidateId,
          });
        }
      }, 10000);
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [tokenData?.token, params.candidateId, socketRef.current?.connected]);

  // Handle form submission
  const onSubmit: SubmitHandler<{ email: string }> = async (data) => {
    if (!params.interviewId || !params.channelName) {
      toastMessageError(t("something_went_wrong"));
      return;
    }

    try {
      setLoading(true);
      // Call API to get token for candidate
      const response = await getAgoraToken({
        interviewId: params.interviewId,
        personType: AGORA_USER_TYPE.Candidate,
        channelName: params.channelName,
        candidateEmail: data.email,
        candidateId: params.candidateId,
      });
      if (response?.data?.success && response?.data?.data) {
        setTokenData(response.data.data);
        setFormSubmitted(true);
        setJoining(true);
        setIsCameraOn(true);
        setIsMicOn(true);
      } else {
        toastMessageError(t(response?.data?.message));
        setFormSubmitted(false);
      }
    } catch (error) {
      console.log("error on submit====>>>>>", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  // End the call
  const endCall = async () => {
    setJoining(false);
    if (client) {
      await client.leave();
    }
    if (localCameraTrack) {
      localCameraTrack.close();
    }
    if (localMicrophoneTrack) {
      localMicrophoneTrack.close();
    }
    setIsCameraOn(false);
    setIsMicOn(false);
    // Redirect to a thank you page or show a message
    setFormSubmitted(false);
  };

  return (
    <div className={style.conduct_interview}>
      <div className="container-fluid">
        {!error && !formSubmitted ? (
          <div className={style.candidate_confirmation}>
            <div className="row align-items-center">
              <div className="col-md-7">
                <Lottie animationData={candidateInterview} loop={false} className={style.candidate_interview_lottie} />
              </div>
              <div className="col-md-5">
                <div className={style.confirmation}>
                  <Image src={logo} alt="logo" className={style.confirmation_logo} />
                  <h3 className={style.confirmation_title}>{t("join_your_online_interview")}</h3>
                  <p className={style.confirmation_text}>{t("you_re_about_to_start_your_online_interview")}</p>
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <div className={style.confirmation_input_group}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="email" required>
                          {t("email")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="email"
                          type="text"
                          placeholder={t("enter_your_email")}
                          required
                        ></Textbox>
                        <Button
                          className={`primary-btn rounded-md button-sm ${style.confirmation_input_group_icon}`}
                          disabled={loading ? true : false}
                          type="submit"
                        >
                          {loading ? <Loader className="ms-2" /> : <NextArrowIcon />}
                        </Button>
                        <InputWrapper.Error message={errors?.email?.message || ""} />
                      </InputWrapper>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        ) : (
          !error && (
            <div className="container">
              <div className={style.video_call_container}>
                <div className={style.video_participants_grid}>
                  <div className="row">
                    {/* Local User Video */}
                    <div className="col-md-6">
                      <div className={style.video_participant_box}>
                        <div className={style.participant_name}>{params.candidateName}</div>
                        <LocalUser
                          audioTrack={localMicrophoneTrack}
                          videoTrack={localCameraTrack}
                          cameraOn={isCameraOn}
                          micOn={isMicOn}
                          className={style.video_feed}
                          playAudio={false}
                          playVideo={true}
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      {/* Remote User Video (Interviewer) */}
                      <div className={style.video_participant_box}>
                        <div className={style.participant_name}>{params.interviewerName}</div>
                        {remoteUsers.length > 0 ? (
                          <RemoteUser user={remoteUsers[0]} playVideo={true} playAudio={true} className={style.video_feed}>
                            <div className={style.remote_user_info}>
                              {params.interviewerName} ({t("interviewer")})
                            </div>
                          </RemoteUser>
                        ) : (
                          <div className={style.empty_video}>
                            <p>{t("waiting_for_interviewer")}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="videos-btn-container candidate-join-btn-container">
                    <Button className="videos-btn danger" onClick={endCall}>
                      <LeaveIcon className="videos-btn-icon" />
                      {t("leave")}
                    </Button>
                    <Button className="videos-btn secondary" onClick={() => setIsCameraOn(!isCameraOn)}>
                      <VideoCallIcon className="videos-btn-icon" isVideoMute={!isCameraOn} />
                      {t("cam")} {isCameraOn}
                    </Button>
                    <Button className="videos-btn secondary" onClick={() => setIsMicOn(!isMicOn)}>
                      <MicIcon className="videos-btn-icon" isMicMute={!isMicOn} />
                      {t("mic")} {isMicOn}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default CandidateJoin;
