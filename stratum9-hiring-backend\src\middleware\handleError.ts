import { Request, Response, NextFunction } from "express";
import * as <PERSON><PERSON> from "@sentry/node";

const HandleErrors =
  (func) =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    try {
      // eslint-disable-next-line no-console
      console.log("Inside HandleErrors", req.originalUrl);
      await func(req, res, next);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error("Inside HandleErrors Error", error);
      Sentry.captureException(error);
      res.status(400).send(error);
      next(error);
    }
  };

export default HandleErrors;
