import { Permission, Role, IUserData, IDepartment } from "@/interfaces/authInterfaces";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// interface IPlanFeature {
//   slug: string;
//   text: string;
//   value: number | string | boolean;
//   feature: string;
//   is_active: boolean;
//   description: string;
// }

export interface ICurrentPlan {
  orgSubscriptionId?: number;
  startDate?: string;
  expiryDate?: string;
  nextBillingDate?: string;
  status?: string;
  subscriptionPlanId?: number;
  subscriptionPlanName?: string;
  subscriptionPlanDescription?: string;
  pricingId?: number;
  price?: number;
  subscriptionPlanPaymentType?: string;
}

export interface AuthState {
  authData: IUserData | null;
  department: IDepartment | null;
  role: Role | null;
  permissions: Permission[];
  currentPlan: ICurrentPlan | null;
}

const initialState: AuthState = {
  authData: {
    id: -1,
    account_type: "",
    email: "",
    isVerified: false,
    sms_notification: false,
    allow_notification: false,
    is_deleted: false,
    image: "",
    orgId: -1,
    departmentId: -1,
    organizationName: "",
    organizationCode: "",
    createdTs: "",
    first_name: "",
    last_name: "",
  },
  department: null,
  role: null,
  permissions: [],
  currentPlan: null,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAuthData: (state, action) => {
      state.authData = action.payload;
    },
    setRole: (state, action) => {
      state.role = action.payload;
    },
    setDepartment: (state, action) => {
      state.department = action.payload;
    },
    setPermissions: (state, action) => {
      state.permissions = action.payload;
    },
    setCurrentPlan: (state, action) => {
      state.currentPlan = action.payload;
    },
    updateUserProfileData: (
      state,
      action: PayloadAction<{
        first_name?: string;
        last_name?: string;
        image?: string | null;
      }>
    ) => {
      if (state.authData) {
        const { first_name, last_name, image } = action.payload;

        // Update firstName and lastName separately
        if (first_name !== undefined) {
          state.authData.first_name = first_name;
        }

        if (last_name !== undefined) {
          state.authData.last_name = last_name;
        }

        // Update image if provided
        if (image !== undefined) {
          state.authData.image = image;
        }
      }
    },
  },
});

export const selectRole = (state: { auth: AuthState }) => state.auth.role;
export const selectDepartment = (state: { auth: AuthState }) => state.auth.department;
export const selectPermissions = (state: { auth: AuthState }) => state.auth.permissions;
export const selectProfileData = (state: { auth: AuthState }) => state.auth.authData;
export const selectCurrentPlan = (state: { auth: AuthState }) => state.auth.currentPlan;

export const { setAuthData, setRole, setDepartment, setPermissions, updateUserProfileData, setCurrentPlan } = authSlice.actions;

export default authSlice.reducer;
