{"v": "4.8.0", "ip": 0, "op": 90, "fr": 30, "w": 1080, "h": 1080, "nm": "R", "assets": [], "layers": [{"ind": 1, "nm": "L", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 10, "s": [100]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [-15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 21, "s": [2]}, {"t": 25, "s": [0]}]}, "p": {"a": 0, "k": [539.619, 539.702, 0]}, "a": {"a": 0, "k": [166.119, 148.202, 0]}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [600, 600, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 10, "s": [240, 240, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 15, "s": [232, 232, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 19, "s": [237, 237, 100]}, {"t": 23, "s": [235, 235, 100]}], "a": 1}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.585, 1.608], [0, 0], [1.727, -0.628], [0, 0], [0, 0]], "o": [[1.728, -0.629], [0, 0], [-0.585, -1.608], [0, 0], [0, 0], [0, 0]], "v": [[8.147, 14.739], [9.937, 11.389], [0.075, -15.707], [-3.471, -17.183], [-9.585, -14.957], [2.034, 16.965]], "c": true}, "a": 0}}, {"ty": "sh", "nm": "P", "ind": 1, "ks": {"k": {"i": [[0, 0], [-0.73, 0.266], [0, 0], [-2.038, -5.599], [0, 0], [6.246, -2.273], [0, 0], [0.238, 0.655]], "o": [[-0.217, -0.595], [0, 0], [6.246, -2.274], [0, 0], [2.038, 5.598], [0, 0], [-0.731, 0.266], [0, 0]], "v": [[-21.137, -18.577], [-20.42, -19.916], [-4.472, -25.72], [8.239, -20.567], [19.315, 9.866], [12.868, 21.923], [-3.079, 27.728], [-4.488, 27.163]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [278.867, 107.29]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.797, -0.29], [0, 0], [0, 0], [0, 0], [-0.238, -0.655], [0, 0], [0.665, -0.242], [0, 0], [0, 0], [0, 0], [-0.238, -0.655], [0, 0], [0.797, -0.29], [0, 0], [0.217, 0.596], [0, 0], [-0.598, 0.217], [0, 0], [-0.216, -0.595]], "o": [[0.217, 0.596], [0, 0], [0, 0], [0, 0], [0.665, -0.242], [0, 0], [0.238, 0.655], [0, 0], [0, 0], [0, 0], [0.798, -0.29], [0, 0], [0.217, 0.596], [0, 0], [-0.598, 0.218], [0, 0], [-0.217, -0.595], [0, 0], [0.798, -0.29], [0, 0]], "v": [[7.1, -21.715], [6.406, -20.315], [-10.207, -14.268], [-5.871, -2.357], [6.82, -6.977], [8.252, -6.352], [10.398, -0.456], [9.703, 0.943], [-2.988, 5.562], [1.39, 17.594], [18.002, 11.547], [19.412, 12.112], [21.58, 18.068], [20.864, 19.409], [-3.589, 28.308], [-4.933, 27.719], [-21.58, -18.021], [-20.931, -19.336], [3.522, -28.237], [4.932, -27.671]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [246.818, 118.56]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-0.217, -0.595], [0, 0], [0.731, -0.266], [0, 0], [0, 0], [0.731, -0.266], [0, 0], [0.239, 0.654], [0, 0], [0, 0], [0.216, 0.595], [0, 0], [-0.664, 0.242]], "o": [[0.731, -0.266], [0, 0], [0.216, 0.595], [0, 0], [0, 0], [0.239, 0.655], [0, 0], [-0.664, 0.242], [0, 0], [0, 0], [-0.664, 0.242], [0, 0], [-0.217, -0.596], [0, 0]], "v": [[7.931, -26.664], [9.341, -26.098], [11.508, -20.142], [10.792, -18.802], [3.084, -15.997], [17.216, 22.833], [16.501, 24.173], [9.59, 26.688], [8.18, 26.123], [-5.953, -12.708], [-13.661, -9.902], [-15.07, -10.468], [-17.238, -16.424], [-16.522, -17.764]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [211.246, 128.331]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.664, -0.242], [0, 0], [0.217, 0.596], [0, 0], [1.727, -0.629], [0, 0], [-0.563, -1.548], [0, 0], [-1.728, 0.628], [0, 0], [0.585, 1.608], [0, 0], [-0.731, 0.266], [0, 0], [-0.217, -0.595], [0, 0], [6.18, -2.249], [0, 0], [2.037, 5.598], [0, 0], [-6.247, 2.274], [0, 0], [-2.038, -5.598]], "o": [[0.217, 0.595], [0, 0], [-0.731, 0.267], [0, 0], [-0.564, -1.549], [0, 0], [-1.794, 0.653], [0, 0], [0.585, 1.608], [0, 0], [1.728, -0.629], [0, 0], [-0.217, -0.596], [0, 0], [0.665, -0.242], [0, 0], [2.038, 5.599], [0, 0], [-6.247, 2.273], [0, 0], [-2.038, -5.598], [0, 0], [6.179, -2.249], [0, 0]], "v": [[10.115, -13.663], [9.399, -12.323], [2.754, -9.905], [1.343, -10.471], [-0.39, -15.235], [-3.915, -16.65], [-7.371, -15.392], [-9.162, -12.043], [0.723, 15.115], [4.248, 16.53], [7.703, 15.272], [9.494, 11.922], [7.76, 7.158], [8.476, 5.818], [15.121, 3.399], [16.531, 3.965], [18.872, 10.397], [12.425, 22.456], [4.983, 25.165], [-7.773, 20.096], [-18.872, -10.397], [-12.358, -22.48], [-4.916, -25.189], [7.774, -20.095]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [185.686, 140.834]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.797, -0.29], [0, 0], [0, 0], [0, 0], [-0.238, -0.655], [0, 0], [0.664, -0.242], [0, 0], [0, 0], [0, 0], [-0.239, -0.655], [0, 0], [0.797, -0.29], [0, 0], [0.216, 0.596], [0, 0], [-0.598, 0.217], [0, 0], [-0.217, -0.595]], "o": [[0.217, 0.595], [0, 0], [0, 0], [0, 0], [0.664, -0.241], [0, 0], [0.239, 0.655], [0, 0], [0, 0], [0, 0], [0.797, -0.29], [0, 0], [0.217, 0.595], [0, 0], [-0.599, 0.218], [0, 0], [-0.217, -0.595], [0, 0], [0.797, -0.29], [0, 0]], "v": [[7.101, -21.715], [6.406, -20.315], [-10.206, -14.268], [-5.871, -2.357], [6.821, -6.977], [8.251, -6.352], [10.398, -0.456], [9.703, 0.943], [-2.989, 5.563], [1.391, 17.594], [18.003, 11.547], [19.412, 12.112], [21.581, 18.068], [20.864, 19.409], [-3.589, 28.308], [-4.932, 27.719], [-21.58, -18.021], [-20.93, -19.336], [3.523, -28.237], [4.932, -27.671]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [153.126, 152.661]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-0.732, 0.266], [0, 0], [-0.217, -0.596], [0, 0], [6.179, -2.249], [0, 0], [2.037, 5.598], [0, 0], [-0.731, 0.266], [0, 0], [-0.217, -0.595], [0, 0], [-1.728, 0.629], [0, 0], [0.563, 1.549]], "o": [[-0.217, -0.595], [0, 0], [0.665, -0.242], [0, 0], [2.038, 5.598], [0, 0], [-6.246, 2.273], [0, 0], [-0.217, -0.596], [0, 0], [0.665, -0.242], [0, 0], [0.563, 1.548], [0, 0], [1.728, -0.629], [0, 0]], "v": [[-9.403, -23.885], [-8.687, -25.225], [-1.776, -27.74], [-0.366, -27.174], [13.506, 10.942], [7.059, 23], [-0.382, 25.709], [-13.073, 20.616], [-15.328, 14.422], [-14.61, 13.082], [-7.967, 10.663], [-6.556, 11.229], [-4.908, 15.756], [-1.384, 17.17], [2.072, 15.913], [3.863, 12.563]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [125.267, 164.233]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.797, -0.29], [0, 0], [0, 0], [0, 0], [-0.238, -0.654], [0, 0], [0.665, -0.242], [0, 0], [0, 0], [0, 0], [-0.238, -0.655], [0, 0], [0.797, -0.291], [0, 0], [0.217, 0.595], [0, 0], [-0.598, 0.218], [0, 0], [-0.217, -0.595]], "o": [[0.217, 0.595], [0, 0], [0, 0], [0, 0], [0.665, -0.242], [0, 0], [0.238, 0.655], [0, 0], [0, 0], [0, 0], [0.797, -0.29], [0, 0], [0.217, 0.595], [0, 0], [-0.598, 0.217], [0, 0], [-0.217, -0.596], [0, 0], [0.797, -0.29], [0, 0]], "v": [[7.1, -21.714], [6.406, -20.315], [-10.206, -14.269], [-5.871, -2.357], [6.82, -6.977], [8.252, -6.352], [10.398, -0.455], [9.703, 0.944], [-2.988, 5.563], [1.39, 17.593], [18.003, 11.547], [19.412, 12.113], [21.58, 18.069], [20.864, 19.409], [-3.589, 28.309], [-4.933, 27.719], [-21.58, -18.02], [-20.931, -19.336], [3.523, -28.236], [4.933, -27.67]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [86.745, 176.822]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.563, 1.549], [0, 0], [1.727, -0.629], [0, 0], [0, 0]], "o": [[1.728, -0.629], [0, 0], [-0.564, -1.548], [0, 0], [0, 0], [0, 0]], "v": [[1.994, -1.245], [3.784, -4.595], [-0.291, -15.792], [-3.816, -17.206], [-10.395, -14.812], [-4.584, 1.149]], "c": true}, "a": 0}}, {"ty": "sh", "nm": "P", "ind": 1, "ks": {"k": {"i": [[0, 0], [2.735, -2.411], [0, 0], [0.73, -0.266], [0, 0], [0.328, 0.488], [0, 0], [0, 0], [0, 0], [0.665, -0.242], [0, 0], [0.217, 0.595], [0, 0], [-0.664, 0.242], [0, 0], [-2.038, -5.598]], "o": [[1.452, 3.99], [0, 0], [0.46, 0.642], [0, 0], [-0.665, 0.242], [0, 0], [0, 0], [0, 0], [0.217, 0.595], [0, 0], [-0.665, 0.241], [0, 0], [-0.217, -0.595], [0, 0], [6.246, -2.274], [0, 0]], "v": [[13.428, -6.216], [11.438, 3.41], [21.703, 17.345], [21.163, 18.756], [14.252, 21.271], [12.819, 20.848], [2.861, 7.341], [-1.724, 9.01], [3.739, 24.019], [3.022, 25.358], [-3.888, 27.874], [-5.299, 27.308], [-21.946, -18.431], [-21.229, -19.771], [-4.551, -25.841], [8.14, -20.748]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [53.156, 189.591]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [3.963, -1.443], [0, 0], [1.443, 3.963], [0, 0], [-3.963, 1.442], [0, 0], [-1.442, -3.963]], "o": [[1.442, 3.963], [0, 0], [-3.963, 1.443], [0, 0], [-1.442, -3.963], [0, 0], [3.963, -1.443], [0, 0]], "v": [[152.188, -33.081], [147.617, -23.277], [-128.043, 77.054], [-137.847, 72.483], [-152.188, 33.08], [-147.617, 23.277], [128.043, -77.055], [137.846, -72.483]], "c": true}, "a": 0}}, {"ty": "sh", "nm": "P", "ind": 1, "ks": {"k": {"i": [[5.153, -1.876], [0, 0], [-1.875, -5.152], [0, 0], [-5.151, 1.875], [0, 0], [1.875, 5.152], [0, 0]], "o": [[0, 0], [-5.152, 1.875], [0, 0], [1.875, 5.152], [0, 0], [5.151, -1.876], [0, 0], [-1.876, -5.152]], "v": [[127.258, -79.21], [-148.401, 21.121], [-154.344, 33.866], [-140.003, 73.268], [-127.259, 79.212], [148.402, -21.121], [154.344, -33.866], [140.003, -73.269]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [166.119, 148.066]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [4.954, -1.803], [0, 0], [1.803, 4.954], [0, 0], [-4.954, 1.803], [0, 0], [-1.803, -4.954]], "o": [[1.803, 4.954], [0, 0], [-4.954, 1.803], [0, 0], [-1.803, -4.954], [0, 0], [4.954, -1.803], [0, 0]], "v": [[159.751, -31.168], [154.037, -18.914], [-130.069, 84.492], [-142.323, 78.778], [-159.751, 30.895], [-154.037, 18.641], [130.069, -84.765], [142.323, -79.051]], "c": true}, "a": 0}}, {"ty": "sh", "nm": "P", "ind": 1, "ks": {"k": {"i": [[0, 0], [-20.11, 7.319], [-5.689, 19.043], [0, 0], [42.999, -15.636], [28.48, 32.481]], "o": [[16.598, 10.931], [20.111, -7.32], [0, 0], [-0.938, 43.211], [-42.977, 15.629], [0, 0]], "v": [[-35.801, 54.251], [22.281, 61.082], [62.385, 18.514], [107.161, 2.217], [36.185, 101.368], [-81.861, 71.016]], "c": true}, "a": 0}}, {"ty": "sh", "nm": "P", "ind": 2, "ks": {"k": {"i": [[50.779, -18.466], [31.889, 40.035], [0, 0], [-1.325, -1.425], [-13.428, -6.267], [-14.735, -0.759], [-14.475, 5.264], [-11.19, 10.402], [-6.266, 13.429], [-0.758, 14.735], [0, 1.959], [0, 0]], "o": [[-50.757, 18.458], [0, 0], [1.245, 1.484], [10.046, 10.807], [13.429, 6.266], [15.256, 0.783], [14.476, -5.264], [10.806, -10.045], [6.267, -13.428], [0.101, -1.965], [0, 0], [1.306, 51.194]], "v": [[41.182, 115.106], [-96.493, 76.341], [-88.161, 73.309], [-84.307, 77.675], [-48.931, 103.405], [-6.487, 113.991], [38.32, 107.239], [76.999, 83.63], [102.727, 48.254], [113.313, 5.811], [113.461, -0.076], [121.794, -3.109]], "c": true}, "a": 0}}, {"ty": "sh", "nm": "P", "ind": 3, "ks": {"k": {"i": [[0, 0], [20.046, -7.296], [5.727, -18.962], [0, 0], [-43.072, 15.663], [-28.467, -32.602]], "o": [[-16.576, -10.846], [-20.045, 7.296], [0, 0], [0.85, -43.295], [43.051, -15.655], [0, 0]], "v": [[35.692, -54.484], [-22.229, -61.209], [-62.277, -18.827], [-108.522, -1.995], [-37.539, -101.368], [80.655, -70.85]], "c": true}, "a": 0}}, {"ty": "sh", "nm": "P", "ind": 4, "ks": {"k": {"i": [[-50.851, 18.492], [-31.869, -40.157], [0, 0], [1.375, 1.48], [13.428, 6.266], [14.734, 0.758], [14.476, -5.265], [11.189, -10.401], [6.267, -13.428], [0.758, -14.735], [-0.004, -2.032], [0, 0]], "o": [[50.828, -18.484], [0, 0], [-1.289, -1.542], [-10.045, -10.806], [-13.429, -6.267], [-15.257, -0.784], [-14.476, 5.264], [-10.807, 10.045], [-6.266, 13.429], [-0.105, 2.039], [0, 0], [-1.399, -51.273]], "v": [[-42.535, -115.105], [95.277, -76.172], [86.947, -73.14], [82.953, -77.675], [47.577, -103.404], [5.134, -113.99], [-39.674, -107.238], [-78.352, -83.63], [-104.082, -48.254], [-114.667, -5.81], [-114.815, 0.296], [-123.145, 3.327]], "c": true}, "a": 0}}, {"ty": "sh", "nm": "P", "ind": 5, "ks": {"k": {"i": [[6.935, -2.524], [0, 0], [0.657, 0.121], [0.982, 10.39], [10.207, -2.181], [4.884, 9.224], [8.593, -5.922], [8.042, 6.653], [5.674, -8.76], [9.975, 3.069], [1.89, -10.265], [10.39, -0.981], [-2.182, -10.206], [9.224, -4.884], [-5.921, -8.594], [6.653, -8.043], [0.377, -0.577], [0, 0], [-2.524, -6.935], [0, 0], [-6.936, 2.524], [0, 0], [-0.538, -0.099], [-0.981, -10.389], [-10.207, 2.181], [-4.884, -9.224], [-8.594, 5.922], [-8.043, -6.653], [-5.673, 8.76], [-9.975, -3.069], [-1.89, 10.265], [-10.389, 0.981], [2.181, 10.207], [-9.223, 4.885], [5.922, 8.593], [-6.653, 8.043], [-0.324, 0.476], [0, 0], [2.525, 6.935], [0, 0]], "o": [[0, 0], [-0.62, -0.183], [-10.265, -1.891], [-0.982, -10.39], [-10.206, 2.182], [-4.884, -9.224], [-8.593, 5.921], [-8.044, -6.653], [-5.674, 8.76], [-9.976, -3.069], [-1.891, 10.265], [-10.39, 0.982], [2.181, 10.207], [-9.224, 4.884], [5.922, 8.593], [-0.454, 0.549], [0, 0], [-6.936, 2.525], [0, 0], [2.524, 6.935], [0, 0], [0.514, 0.142], [10.265, 1.89], [0.982, 10.39], [10.207, -2.181], [4.884, 9.224], [8.593, -5.922], [8.042, 6.653], [5.674, -8.76], [9.976, 3.069], [1.89, -10.266], [10.39, -0.981], [-2.182, -10.205], [9.224, -4.884], [-5.922, -8.594], [0.378, -0.457], [0, 0], [6.935, -2.524], [0, 0], [-2.525, -6.935]], "v": [[128.761, -88.359], [115.274, -83.45], [113.36, -83.909], [92.912, -106.238], [72.57, -121.163], [45.132, -133.966], [20.628, -139.969], [-9.619, -141.299], [-34.559, -137.468], [-63.012, -127.121], [-84.586, -114.037], [-106.915, -93.589], [-121.839, -73.247], [-134.643, -45.809], [-140.647, -21.304], [-141.976, 8.944], [-143.222, 10.635], [-155.345, 15.047], [-163.345, 32.203], [-145.917, 80.086], [-128.761, 88.086], [-116.291, 83.547], [-114.714, 83.91], [-94.266, 106.238], [-73.924, 121.163], [-46.486, 133.967], [-21.981, 139.97], [8.267, 141.299], [33.205, 137.469], [61.658, 127.122], [83.233, 114.038], [105.561, 93.589], [120.486, 73.247], [133.289, 45.809], [139.293, 21.305], [140.622, -8.943], [141.672, -10.344], [155.345, -15.32], [163.344, -32.476], [145.917, -80.359]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.827, 0.066, 0.066, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [166.119, 148.202]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "cl": "ai", "ip": 0, "op": 150, "st": -167.5, "ty": 4}], "markers": []}