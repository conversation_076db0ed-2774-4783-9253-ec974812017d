// eslint-disable-next-line import/no-extraneous-dependencies
import express, { Request, Response } from "express";
import http from "http";
import cors from "cors";
import helmet from "helmet";
import swaggerUi from "swagger-ui-express";
import swaggerJSDoc from "swagger-jsdoc";
import config from "./config/envConfig";

import swaggerDefinition from "./swagger/swagger";
import sentryInitialization from "./config/sentryConfig";
import { BASE_ROUTES, LIMIT, ROUTES } from "./utils/constants";
import dbConnection from "./db/dbConnection";

import accessManagementRoutes from "./features/accessManagement/routes";
import employeeManagementRoutes from "./features/employeeManagement/routes";
import { getSecretKeys } from "./config/awsConfig";
import authRoutes from "./features/auth/routes";
import jobRoutes from "./features/jobs/routes";
import finalAssessmentRoutes from "./features/finalAssessment/routes";
import resumeScreenRoutes from "./features/resumeScreen/routes";
import interviewRoutes from "./features/interview/routes";
import candidatesRoute from "./features/candidatesManagement/routes";
import agoraRecordingRoutes from "./features/agoraRecording/routes";
import subscriptionRoutes from "./features/subscriptions/routes";
import notificationRoutes from "./features/notification/routes";
import userProfileRoutes from "./features/userProfile/routes";

import { SocketIOManager } from "./utils/helper";
import commonRoutes from "./features/commonFeatures/routes";
import validateStripeWebhook from "./middleware/stripeWebhookValidator";
import HandleErrors from "./middleware/handleError";
import { handleStripeWebhook } from "./features/subscriptions/controller";
import activityLogsRoute from "./features/activityLogs/routes";

const app = express();
const envConfig = config();
const { port } = envConfig;
const httpServer = http.createServer(app);

dbConnection.getS9DataSource();
dbConnection.getS9InnerviewDataSource();
app.use(
  BASE_ROUTES.SUBSCRIPTION + ROUTES.WEBHOOKS.STRIPE,
  express.raw({ type: "application/json" }),
  validateStripeWebhook,
  HandleErrors(handleStripeWebhook)
);
sentryInitialization();
app.use(
  cors({
    origin: "*",
  })
);
app.use(express.json({ limit: LIMIT }));
app.use(express.urlencoded({ extended: true, limit: LIMIT }));
// Helmet helps you secure your Express apps by setting various HTTP headers.
app.use(helmet());

// **********Hiring Moudule APIs**********
app.use(BASE_ROUTES.ACCESS_MANAGEMENT, accessManagementRoutes);
app.use(BASE_ROUTES.EMPLOYEE_MANAGEMENT, employeeManagementRoutes);
app.use(BASE_ROUTES.AUTH, authRoutes);
app.use(BASE_ROUTES.INTERVIEW, interviewRoutes);
app.use(BASE_ROUTES.JOB_REQUIREMENT, jobRoutes);
app.use(BASE_ROUTES.FINAL_ASSESSMENT, finalAssessmentRoutes);
app.use(BASE_ROUTES.RESUME_SCREEN, resumeScreenRoutes);
app.use(BASE_ROUTES.CANDIDATES, candidatesRoute);
app.use(BASE_ROUTES.SUBSCRIPTION, subscriptionRoutes);
app.use(BASE_ROUTES.NOTIFICATIONS, notificationRoutes);
app.use(BASE_ROUTES.USER_PROFILE, userProfileRoutes);
app.use(BASE_ROUTES.COMMON, commonRoutes);
app.use(BASE_ROUTES.ACTIVITY_LOGS, activityLogsRoute);

// **********Agora Cloud Recording APIs**********
app.use(BASE_ROUTES.AGORA_RECORDING, agoraRecordingRoutes);

// app.get("/delete-cache", (req, res) => {
//   const cache = new Cache();
//   cache.del("skills_data");
//   res
//     .status(200)
//     .json({ success: true, message: "Cache deleted successfully" });
// });

app.get("/api/v1/health", (req, res) => {
  res.status(200).json({ success: true, message: "Server is up and running" });
});
// sync configuration
app.get("/api/v1/sync-config", async (_: Request, res: Response) => {
  await getSecretKeys(true);
  res.status(200).json({
    message: "Synced configuration",
  });
});

const swaggerSpec = swaggerJSDoc(swaggerDefinition);
app.use("/api/jobs", jobRoutes);

app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Initialize Socket.io server using the SocketIOManager class
new SocketIOManager(httpServer);

httpServer.listen(port, () => {
  console.log(`🚀 Server running at http://localhost:${port}`);
});
