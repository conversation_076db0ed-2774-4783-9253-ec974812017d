"use client";
import React, { useState } from "react";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import { useForm } from "react-hook-form";
import Link from "next/link";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import style from "@/styles/commonPage.module.scss";
import ApplicationsSourcesModal from "@/components/commonModals/ApplicationsSourcesModal";
const ConductFurtherRound = () => {
  const { control } = useForm();

  const [showApplicationsSourcesModal, setShowApplicationsSourcesModal] = useState(false);
  const onCancelApplicationsSourcesModal = async () => {
    setShowApplicationsSourcesModal(false);
  };
  return (
    <>
      <section className={`${style.resume_page} ${style.candidates_list_page}`}>
        <div className="container">
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  <BackArrowIcon />
                  Lined-Up Candidates For <span>Operation Admin</span>
                </h2>
                <div className="right-action">
                  <InputWrapper className="mb-0 w-100 search-input">
                    <div className="icon-align right">
                      <Textbox
                        className="form-control w-100"
                        control={control}
                        name="search"
                        type="text"
                        placeholder="Search using job id, job title etc."
                      >
                        <InputWrapper.Icon>
                          <SearchIcon />
                        </InputWrapper.Icon>
                      </Textbox>
                    </div>
                  </InputWrapper>
                </div>
              </div>
            </div>
          </div>
          <div className={style.candidates_list_section}>
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>Candidate Name</th>
                    <th>Qualified On</th>
                    <th>Lined Up For</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <Link href="/" className="primary underline">
                        Kathryn Hahn
                      </Link>
                    </td>
                    <td>Aug 18, 2024</td>
                    <td>2nd Round</td>
                    <td>
                      <div className="multi-action-links">
                        <Link href="/" className="primary underline">
                          Conduct Interview
                        </Link>
                        <Link href="/" className="primary underline">
                          Quick Summary
                        </Link>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Link href="/" className="primary underline">
                        Kathryn Hahn
                      </Link>
                    </td>
                    <td>Aug 18, 2024</td>
                    <td>2nd Round</td>
                    <td>
                      <div className="multi-action-links">
                        <Link href="/" className="primary underline">
                          Conduct Interview
                        </Link>
                        <Link href="/" className="primary underline">
                          Quick Summary
                        </Link>
                      </div>
                    </td>
                  </tr>{" "}
                  <tr>
                    <td>
                      <Link href="/" className="primary underline">
                        Kathryn Hahn
                      </Link>
                    </td>
                    <td>Aug 18, 2024</td>
                    <td>2nd Round</td>
                    <td>
                      <div className="multi-action-links">
                        <Link href="/" className="primary underline">
                          Conduct Interview
                        </Link>
                        <Link href="/" className="primary underline">
                          Quick Summary
                        </Link>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Link href="/" className="primary underline">
                        Kathryn Hahn
                      </Link>
                    </td>
                    <td>Aug 18, 2024</td>
                    <td>2nd Round</td>
                    <td>
                      <div className="multi-action-links">
                        <Link href="/" className="primary underline">
                          Conduct Interview
                        </Link>
                        <Link href="/" className="primary underline">
                          Quick Summary
                        </Link>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Link href="/" className="primary underline">
                        Kathryn Hahn
                      </Link>
                    </td>
                    <td>Aug 18, 2024</td>
                    <td>2nd Round</td>
                    <td>
                      <div className="multi-action-links">
                        <Link href="/" className="primary underline">
                          Conduct Interview
                        </Link>
                        <Link href="/" className="primary underline">
                          Quick Summary
                        </Link>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Link href="/" className="primary underline">
                        Kathryn Hahn
                      </Link>
                    </td>
                    <td>Aug 18, 2024</td>
                    <td>2nd Round</td>
                    <td>
                      <div className="multi-action-links">
                        <Link href="/" className="primary underline">
                          Conduct Interview
                        </Link>
                        <Link href="/" className="primary underline">
                          Quick Summary
                        </Link>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {showApplicationsSourcesModal && <ApplicationsSourcesModal onCancel={onCancelApplicationsSourcesModal} />}
    </>
  );
};

export default ConductFurtherRound;
