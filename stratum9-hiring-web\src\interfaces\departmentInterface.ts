/**
 * Department related interfaces
 */

import { DEPARTMENT_ALTER_MODE } from "@/components/views/accessManagement/EmployeeManagement";
import { FindDepartmentResponse } from "@/services/departmentService";

export interface Department {
  id: number;
  name: string;
  organizationId: number;
}

export interface DepartmentForm {
  name: string;
}

export interface DepartmentModalProps {
  onClickCancel: () => void;
  onSubmitSuccess: (message?: string) => void;
  disabled?: boolean;
  department?: FindDepartmentResponse | null;
  mode: (typeof DEPARTMENT_ALTER_MODE)[keyof typeof DEPARTMENT_ALTER_MODE];
}

export interface DepartmentFormData {
  name: string;
}
