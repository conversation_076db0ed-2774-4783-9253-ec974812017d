/**
 * Agora Recording Routes
 * Defines API endpoints for Agora Cloud Recording and Speech-to-Text
 */
import express from "express";
import { sanitizeBody } from "../../middleware/sanitize";
import HandleErrors from "../../middleware/handleError";
import {
  createAgoraToken,
  startMeetingRecording,
  stopMeetingRecording,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import {
  queryValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import {
  createTokenSchema,
  startRecordingSchema,
  stopRecordingSchema,
} from "./validation";

const router = express.Router();

/**
 * @swagger
 * /api/v1/agora-recordings/meetings/{meetingId}/start:
 *   post:
 *     summary: Start recording a meeting
 *     description: Starts cloud recording for a specific meeting
 *     parameters:
 *       - in: path
 *         name: meetingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the meeting to record
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               channelName:
 *                 type: string
 *                 description: Agora channel name for the meeting
 *               token:
 *                 type: string
 *                 description: Optional Agora token for secure channels
 *               uid:
 *                 type: string
 *                 description: Optional custom recording UID
 *     responses:
 *       200:
 *         description: Recording started successfully
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error while starting recording
 */
router.post(
  ROUTES.AGORA.START_MEETING_RECORDING,
  sanitizeBody(),
  schemaValidation(startRecordingSchema),
  HandleErrors(startMeetingRecording)
);

/**
 * @swagger
 * /api/v1/agora-recordings/meetings/stop:
 *   post:
 *     summary: Stop recording a meeting
 *     description: Stops an active cloud recording for a specific meeting
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - meetingId
 *               - resourceId
 *               - sid
 *               - channelName
 *               - uid
 *             properties:
 *               meetingId:
 *                 type: string
 *                 description: ID of the meeting to stop recording
 *               resourceId:
 *                 type: string
 *                 description: Resource ID acquired from Agora
 *               sid:
 *                 type: string
 *                 description: Session ID for the recording
 *               mode:
 *                 type: string
 *                 enum: [mix, individual]
 *                 default: mix
 *                 description: Recording mode
 *               channelName:
 *                 type: string
 *                 description: Channel name being recorded
 *               uid:
 *                 type: string
 *                 description: Unique ID for the recording service
 *     responses:
 *       200:
 *         description: Recording stopped successfully
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Server error while stopping recording
 */
router.post(
  ROUTES.AGORA.STOP_MEETING_RECORDING,
  sanitizeBody(),
  schemaValidation(stopRecordingSchema),
  HandleErrors(stopMeetingRecording)
);

/**
 * @swagger
 * /api/v1/agora-recordings/create-token:
 *   get:
 *     summary: Create Agora token
 *     description: Creates a token for Agora Cloud Recording service
 *     parameters:
 *       - in: query
 *         name: interviewId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the interview to create token for
 *       - in: query
 *         name: personType
 *         required: true
 *         schema:
 *           type: string
 *         description: Type of person (interviewer or candidate)
 *       - in: query
 *         name: channelName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the channel to create token for
 *       - in: query
 *         name: candidateEmail
 *         required: false
 *         schema:
 *           type: string
 *         description: Email of the candidate (only required for candidates)
 *     responses:
 *       200:
 *         description: Token created successfully
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error while creating token
 */
router.get(
  ROUTES.AGORA.CREATE_TOKEN,
  sanitizeBody(),
  queryValidation(createTokenSchema),
  HandleErrors(createAgoraToken)
);

export default router;
