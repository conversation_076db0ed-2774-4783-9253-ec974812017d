// src/features/candidatesManagement/routes.ts
import express from "express";
import auth from "../../middleware/auth";
import { ROUTES } from "../../utils/constants";
import {
  addApplicantAdditionalInfoController,
  getCandidateDetailsController,
  getCandidatesController,
  getTopCandidatesController,
  promoteDemoteCandidateController,
  archiveActiveApplication,
  updateJobApplicationStatusController,
  getCandidateInterviewHistoryController,
  getApplicationFinalSummaryController,
  generateFinalSummaryController,
  getAllHiredCandidate,
} from "./controller";
import HandleErrors from "../../middleware/handleError";
import {
  authorizedForAddAdditionalCandidateInfo,
  authorizedForArchiveRestoreCandidates,
  authorizedForManageTopCandidates,
  authorizedForCandidateDetailsAccess,
  authorizedForHireCandidateAccess,
  authorizedForManageCandidateProfileAccess,
  authorizedForHireCandidate,
  // authorizedForViewCandidateProfileSummary,
} from "../../middleware/isAuthorized";
import {
  queryValidation,
  schemaValidation,
  paramsValidation,
} from "../../middleware/validateSchema";
import {
  addApplicantAdditionalInfoValidation,
  getAllCandidatesValidation,
  getTopCandidatesValidation,
  promoteDemoteCandidateValidation,
  archiveActiveApplicationValidation,
  updateJobApplicationStatusValidation,
  jobApplicationIdValidation,
} from "./validation";

const candidatesRoute = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     CandidateApplication:
 *       type: object
 *       properties:
 *         candidateName:
 *           type: string
 *           description: Full name of the candidate
 *           example: "John Doe"
 *         applicationCreatedTs:
 *           type: string
 *           format: date-time
 *           description: Timestamp when application was created
 *           example: "2024-01-15T10:30:00Z"
 *         atsScore:
 *           type: number
 *           description: ATS (Applicant Tracking System) score
 *           example: 85
 *         applicationId:
 *           type: number
 *           description: Unique identifier for the application
 *           example: 123
 *         applicationRankStatus:
 *           type: string
 *           enum: ["Promoted", "Demoted", "No Changes"]
 *           description: Current ranking status of the application
 *           example: "Promoted"
 *         applicationUpdatedTs:
 *           type: string
 *           format: date-time
 *           description: Timestamp when application was last updated
 *           example: "2024-01-16T14:20:00Z"
 *         applicationSource:
 *           type: string
 *           description: Source platform where application originated
 *           example: "LinkedIn"
 *         candidateId:
 *           type: number
 *           description: Unique identifier for the candidate
 *           example: 456
 *         aiReason:
 *           type: string
 *           description: AI-generated reason for application status
 *           example: "Strong technical background with relevant experience"
 *         aiDecision:
 *           type: string
 *           enum: ["Approved", "Rejected"]
 *           description: AI decision on the application
 *           example: "Approved"
 *         applicationStatus:
 *           type: string
 *           enum: ["Approved", "Rejected", "On-Hold", "Hired", "Pending", "Final-Reject"]
 *           description: Current status of the application
 *           example: "Approved"
 *         hiringManagerReason:
 *           type: string
 *           description: Reason provided by hiring manager
 *           example: "Excellent communication skills demonstrated in screening"
 *         job_id:
 *           type: number
 *           description: ID of the job position
 *           example: 789
 *         isTopApplication:
 *           type: boolean
 *           description: Whether this is marked as a top application
 *           example: true
 *         isActive:
 *           type: boolean
 *           description: Whether the application is active or archived
 *           example: true
 *         hiring_manager_id:
 *           type: number
 *           description: ID of the assigned hiring manager
 *           example: 101
 *
 *     CandidateProfile:
 *       type: object
 *       properties:
 *         candidateName:
 *           type: string
 *           description: Full name of the candidate
 *           example: "Jane Smith"
 *         jobTitle:
 *           type: string
 *           description: Title of the job position applied for
 *           example: "Senior Software Engineer"
 *         status:
 *           type: string
 *           description: Current application status
 *           example: "Approved"
 *         resumeLink:
 *           type: string
 *           format: uri
 *           description: URL link to candidate's resume
 *           example: "https://example.com/resumes/jane-smith.pdf"
 *         hiringManagerId:
 *           type: number
 *           description: ID of the assigned hiring manager
 *           example: 101
 *         interviewerName:
 *           type: string
 *           description: Name of the assigned interviewer
 *           example: "Mike Johnson"
 *         interviewerImage:
 *           type: string
 *           format: uri
 *           description: Profile image URL of the interviewer
 *           example: "https://example.com/images/mike-johnson.jpg"
 *         department:
 *           type: string
 *           description: Department for the position
 *           example: "Engineering"
 *         imageUrl:
 *           type: string
 *           format: uri
 *           description: Profile image URL of the candidate
 *           example: "https://example.com/images/jane-smith.jpg"
 *         roundNumber:
 *           type: number
 *           description: Current interview round number
 *           example: 2
 *         jobApplicationId:
 *           type: number
 *           description: Unique identifier for the job application
 *           example: 123
 *
 *     InterviewHistory:
 *       type: object
 *       properties:
 *         roundNumber:
 *           type: number
 *           description: Interview round number
 *           example: 1
 *         interviewerId:
 *           type: number
 *           description: ID of the interviewer
 *           example: 101
 *         interviewerName:
 *           type: string
 *           description: Name of the interviewer
 *           example: "Sarah Wilson"
 *         hardSkillMarks:
 *           type: number
 *           description: Hard skills evaluation score
 *           example: 8.5
 *         skillScores:
 *           type: object
 *           description: JSON object containing skill-specific scores
 *           example: {"JavaScript": 9, "React": 8, "Node.js": 7}
 *         interviewSummary:
 *           type: object
 *           description: Summary of the interview
 *           properties:
 *             highlight:
 *               type: array
 *               items:
 *                 type: string
 *               description: Key highlights from the interview
 *               example: ["Strong problem-solving skills", "Good communication"]
 *         interviewerPerformanceAiAnalysis:
 *           type: string
 *           description: AI analysis of interviewer performance
 *           example: "Interviewer conducted thorough technical assessment"
 *
 *     FinalAssessment:
 *       type: object
 *       properties:
 *         overallSuccessProbability:
 *           type: number
 *           description: Overall success probability percentage (0-100)
 *           example: 85
 *         skillSummary:
 *           type: string
 *           description: AI-generated summary of candidate's skills
 *           example: "Strong technical foundation with excellent problem-solving abilities"
 *         developmentRecommendations:
 *           type: object
 *           description: Personalized development recommendations by category
 *           example: {"Technical": ["Advanced algorithms"], "Soft Skills": ["Leadership training"]}
 *
 *     SkillSpecificAssessment:
 *       type: object
 *       properties:
 *         careerBasedSkillsScore:
 *           type: number
 *           description: Overall career-based skills score (0-10)
 *           example: 8.2
 *         skillsScores:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               skill_name:
 *                 type: string
 *                 description: Name of the skill
 *                 example: "JavaScript"
 *               skill_marks:
 *                 type: number
 *                 description: Skill score (0-10)
 *                 example: 8.5
 *               strengths:
 *                 type: object
 *                 properties:
 *                   strengths:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of strengths in this skill
 *                     example: ["Advanced ES6 knowledge", "Async programming expertise"]
 *               potentials_gaps:
 *                 type: object
 *                 properties:
 *                   potentialGaps:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Areas for improvement
 *                     example: ["Testing frameworks", "Performance optimization"]
 *               probability_of_success_in_this_skill:
 *                 type: object
 *                 properties:
 *                   probabilityOfSuccessInSkill:
 *                     type: number
 *                     description: Success probability for this skill (0-100)
 *                     example: 85
 *
 *     ApiResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           description: Indicates if the request was successful
 *           example: true
 *         message:
 *           type: string
 *           description: Response message
 *           example: "candidates_fetched"
 *         code:
 *           type: number
 *           description: HTTP status code
 *           example: 200
 *         data:
 *           description: Response data (varies by endpoint)
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           description: Always false for error responses
 *           example: false
 *         message:
 *           type: string
 *           description: Error message
 *           example: "something_went_wrong"
 *         error:
 *           type: string
 *           description: Detailed error information
 *           example: "Database connection failed"
 *         code:
 *           type: number
 *           description: HTTP status code
 *           example: 500
 *
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *       description: JWT token for authentication
 *
 * tags:
 *   - name: Candidates Management
 *     description: API endpoints for managing candidate applications and profiles
 */

/**
 * @swagger
 * /api/candidates/get-candidates:
 *   get:
 *     summary: Get all candidates with their job applications
 *     description: Retrieves a paginated list of candidates with their job application details, supporting filtering by job ID, search string, and active status
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Page offset for pagination (0-based)
 *         example: 0
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Maximum number of candidates to return per page
 *         example: 10
 *       - in: query
 *         name: searchStr
 *         schema:
 *           type: string
 *         description: Search string to filter candidates by name
 *         example: "John Doe"
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by status - true for active candidates, false for archived
 *         example: true
 *       - in: query
 *         name: jobId
 *         schema:
 *           type: integer
 *         description: Optional job ID to filter candidates for specific position
 *         example: 123
 *     responses:
 *       200:
 *         description: Successfully retrieved candidates
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/CandidateApplication'
 *             example:
 *               success: true
 *               message: "candidates_fetched"
 *               code: 200
 *               data: [
 *                 {
 *                   candidateName: "John Doe",
 *                   applicationCreatedTs: "2024-01-15T10:30:00Z",
 *                   atsScore: 85,
 *                   applicationId: 123,
 *                   applicationRankStatus: "Promoted",
 *                   applicationUpdatedTs: "2024-01-16T14:20:00Z",
 *                   applicationSource: "LinkedIn",
 *                   candidateId: 456,
 *                   aiReason: "Strong technical background",
 *                   aiDecision: "Approved",
 *                   applicationStatus: "Approved",
 *                   hiringManagerReason: "Excellent communication skills",
 *                   job_id: 789,
 *                   isTopApplication: true,
 *                   isActive: true,
 *                   hiring_manager_id: 101
 *                 }
 *               ]
 *       400:
 *         description: Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "get_all_candidates_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_CANDIDATES,
  auth,
  queryValidation(getAllCandidatesValidation),
  HandleErrors(getCandidatesController)
);

/**
 * @swagger
 * /api/candidates/archive-active-application/{applicationId}:
 *   put:
 *     summary: Archive or restore a candidate application
 *     description: Archives or restores a candidate application by updating its active status. Requires special authorization for archive/restore operations.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: applicationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the application to archive/restore
 *         example: 123
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: boolean
 *                 description: Archive status - false to archive, true to restore
 *                 example: false
 *               reason:
 *                 type: string
 *                 description: Optional reason for archiving (required when archiving)
 *                 example: "Candidate withdrew application"
 *           examples:
 *             archive:
 *               summary: Archive application
 *               value:
 *                 status: false
 *                 reason: "Candidate withdrew application"
 *             restore:
 *               summary: Restore application
 *               value:
 *                 status: true
 *     responses:
 *       200:
 *         description: Application status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "updated_application_status_success"
 *               code: 200
 *       400:
 *         description: Invalid request body or parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       403:
 *         description: Forbidden - Insufficient permissions for archive/restore operations
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "You don't have permission to access this feature."
 *               code: 403
 *       404:
 *         description: Job application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "job_application_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "update_application_status_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.put(
  ROUTES.CANDIDATES.ARCHIVE_ACTIVE_APPLICATION,
  auth,
  authorizedForArchiveRestoreCandidates,
  schemaValidation(archiveActiveApplicationValidation),
  HandleErrors(archiveActiveApplication)
);

/**
 * @swagger
 * /api/candidates/top-candidates:
 *   get:
 *     summary: Get top-ranked candidates
 *     description: Retrieves a list of top-performing candidates based on AI scoring, ATS evaluation, and ranking algorithms. Results are pre-filtered and sorted by performance metrics.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: jobId
 *         schema:
 *           type: integer
 *         description: Optional job ID to filter top candidates for specific position
 *         example: 123
 *     responses:
 *       200:
 *         description: Successfully retrieved top candidates
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/CandidateApplication'
 *             example:
 *               success: true
 *               message: "top_candidates_retrieved"
 *               code: 200
 *               data: [
 *                 {
 *                   candidateName: "Alice Johnson",
 *                   applicationCreatedTs: "2024-01-15T10:30:00Z",
 *                   atsScore: 95,
 *                   applicationId: 456,
 *                   applicationRankStatus: "Promoted",
 *                   applicationUpdatedTs: "2024-01-16T14:20:00Z",
 *                   applicationSource: "LinkedIn",
 *                   candidateId: 789,
 *                   aiReason: "Exceptional technical skills and experience",
 *                   aiDecision: "Approved",
 *                   applicationStatus: "Approved",
 *                   hiringManagerReason: "Outstanding portfolio and references",
 *                   job_id: 123,
 *                   isTopApplication: true,
 *                   isActive: true,
 *                   hiring_manager_id: 101
 *                 }
 *               ]
 *       400:
 *         description: Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "get_top_candidates_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_TOP_CANDIDATES,
  auth,
  authorizedForManageTopCandidates,
  queryValidation(getTopCandidatesValidation),
  HandleErrors(getTopCandidatesController)
);

/**
 * @swagger
 * /api/candidates/update-candidate-rank-status:
 *   put:
 *     summary: Promote or demote a candidate
 *     description: Updates the ranking status of a candidate application by promoting or demoting them. This affects the candidate's visibility and priority in the hiring process.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - candidateId
 *               - applicationId
 *               - action
 *             properties:
 *               candidateId:
 *                 type: integer
 *                 description: Unique identifier of the candidate
 *                 example: 456
 *               applicationId:
 *                 type: integer
 *                 description: Unique identifier of the application
 *                 example: 123
 *               action:
 *                 type: string
 *                 enum: ["Promoted", "Demoted"]
 *                 description: Action to perform on the candidate ranking
 *                 example: "Promoted"
 *           examples:
 *             promote:
 *               summary: Promote candidate
 *               value:
 *                 candidateId: 456
 *                 applicationId: 123
 *                 action: "Promoted"
 *             demote:
 *               summary: Demote candidate
 *               value:
 *                 candidateId: 456
 *                 applicationId: 123
 *                 action: "Demoted"
 *     responses:
 *       200:
 *         description: Candidate ranking updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "Candidate ranking updated successfully"
 *               code: 200
 *       400:
 *         description: Invalid request body or parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_application_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "update_rank_status_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.put(
  ROUTES.CANDIDATES.PROMOTE_DEMOTE_CANDIDATE,
  auth,
  authorizedForManageTopCandidates,
  schemaValidation(promoteDemoteCandidateValidation),
  HandleErrors(promoteDemoteCandidateController)
);

/**
 * @swagger
 * /api/candidates/get-candidate-details:
 *   get:
 *     summary: Get detailed candidate profile information
 *     description: Retrieves comprehensive candidate profile details including personal information, job application status, assigned interviewer details, resume links, and current round information.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: candidateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the candidate
 *         example: 456
 *     responses:
 *       200:
 *         description: Successfully retrieved candidate details
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CandidateProfile'
 *             example:
 *               success: true
 *               message: "Candidate details retrieved successfully"
 *               code: 200
 *               data:
 *                 candidateName: "Jane Smith"
 *                 jobTitle: "Senior Software Engineer"
 *                 status: "Approved"
 *                 resumeLink: "https://example.com/resumes/jane-smith.pdf"
 *                 hiringManagerId: 101
 *                 interviewerName: "Mike Johnson"
 *                 interviewerImage: "https://example.com/images/mike-johnson.jpg"
 *                 department: "Engineering"
 *                 imageUrl: "https://example.com/images/jane-smith.jpg"
 *                 roundNumber: 2
 *                 jobApplicationId: 123
 *       400:
 *         description: Invalid or missing candidateId parameter
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate not found or not accessible by organization
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_not_found_for_org"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "fetch_candidate_details_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_CANDIDATE_DETAILS,
  auth,
  authorizedForCandidateDetailsAccess,
  queryValidation(jobApplicationIdValidation),
  HandleErrors(getCandidateDetailsController)
);

/**
 * @swagger
 * /api/candidates/add-applicant-additional-info:
 *   post:
 *     summary: Add additional information to candidate application
 *     description: Allows candidates or hiring managers to submit supplementary information, documents, or clarifications to an existing application. Commonly used for portfolio submissions, additional certifications, or responses to specific questions.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - applicationId
 *               - description
 *               - images
 *             properties:
 *               applicationId:
 *                 type: string
 *                 description: Unique identifier of the application to update
 *                 example: "123"
 *               description:
 *                 type: string
 *                 description: Description or text content of the additional information
 *                 example: "Portfolio showcasing recent projects and achievements"
 *               images:
 *                 type: string
 *                 description: Image URLs, file references, or document links
 *                 example: "https://example.com/portfolio.pdf,https://example.com/certificate.jpg"
 *           examples:
 *             portfolio:
 *               summary: Portfolio submission
 *               value:
 *                 applicationId: "123"
 *                 description: "Portfolio showcasing recent projects and achievements"
 *                 images: "https://example.com/portfolio.pdf"
 *             certification:
 *               summary: Additional certification
 *               value:
 *                 applicationId: "456"
 *                 description: "AWS Solutions Architect certification"
 *                 images: "https://example.com/aws-cert.jpg"
 *     responses:
 *       200:
 *         description: Additional information added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "Additional information added successfully"
 *               code: 200
 *       400:
 *         description: Invalid request body or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "job_application_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "something_went_wrong"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.post(
  ROUTES.CANDIDATES.ADD_APPLICANT_ADDITIONAL_INFO,
  auth,
  authorizedForAddAdditionalCandidateInfo,
  schemaValidation(addApplicantAdditionalInfoValidation),
  HandleErrors(addApplicantAdditionalInfoController)
);

/**
 * @swagger
 * /api/candidates/update-job-application-status/{jobApplicationId}:
 *   put:
 *     summary: Update job application status (Hire/Reject)
 *     description: Updates the final status of a job application to either "Hired" or "Final-Reject". This action typically triggers workflow notifications and updates the candidate's status across the system.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobApplicationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the job application to update
 *         example: 123
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: ["Hired", "Final-Reject", "Approved", "Rejected", "On-Hold", "Pending"]
 *                 description: New status for the job application
 *                 example: "Hired"
 *           examples:
 *             hire:
 *               summary: Hire candidate
 *               value:
 *                 status: "Hired"
 *             reject:
 *               summary: Reject candidate
 *               value:
 *                 status: "Final-Reject"
 *             approve:
 *               summary: Approve candidate
 *               value:
 *                 status: "Approved"
 *     responses:
 *       200:
 *         description: Job application status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "Job application status updated successfully"
 *               code: 200
 *       400:
 *         description: Invalid request body or parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Job application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_application_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Failed to update job application status"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.put(
  ROUTES.CANDIDATES.UPDATE_JOB_APPLICATION_STATUS,
  auth,
  authorizedForHireCandidate,
  paramsValidation(jobApplicationIdValidation),
  schemaValidation(updateJobApplicationStatusValidation),
  HandleErrors(updateJobApplicationStatusController)
);

/**
 * @swagger
 * /api/candidates/get-candidate-interview-history:
 *   get:
 *     summary: Get candidate interview history
 *     description: Retrieves comprehensive interview history for a specific candidate including interviewer information, skill scores, hard skill marks, interview summaries, and AI-powered performance analysis across all rounds.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: candidateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the candidate
 *         example: 456
 *     responses:
 *       200:
 *         description: Successfully retrieved interview history
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/InterviewHistory'
 *             example:
 *               success: true
 *               message: "Interview history retrieved successfully"
 *               code: 200
 *               data: [
 *                 {
 *                   roundNumber: 1,
 *                   interviewerId: 101,
 *                   interviewerName: "Sarah Wilson",
 *                   hardSkillMarks: 8.5,
 *                   skillScores: {
 *                     "JavaScript": 9,
 *                     "React": 8,
 *                     "Node.js": 7
 *                   },
 *                   interviewSummary: {
 *                     highlight: [
 *                       "Strong problem-solving skills",
 *                       "Good communication",
 *                       "Excellent technical knowledge"
 *                     ]
 *                   },
 *                   interviewerPerformanceAiAnalysis: "Interviewer conducted thorough technical assessment with good coverage of required skills"
 *                 }
 *               ]
 *       400:
 *         description: Invalid or missing candidateId parameter
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate not found or no interview history available
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "get_interview_history_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_CANDIDATE_INTERVIEW_HISTORY,
  auth,
  paramsValidation(jobApplicationIdValidation),
  HandleErrors(getCandidateInterviewHistoryController)
);

/**
 * @swagger
 * /api/candidates/application-final-summary/{candidateId}:
 *   get:
 *     summary: Get application final assessment summary
 *     description: Retrieves comprehensive final assessment analysis generated after all interview rounds are completed. Includes AI-powered insights, success probability calculations, skill summaries, and personalized development recommendations.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: candidateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the candidate
 *         example: 456
 *     responses:
 *       200:
 *         description: Successfully retrieved final assessment summary
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/FinalAssessment'
 *             example:
 *               success: true
 *               message: "Final assessment retrieved successfully"
 *               code: 200
 *               data:
 *                 overallSuccessProbability: 85
 *                 skillSummary: "Strong technical foundation with excellent problem-solving abilities. Demonstrates proficiency in modern web technologies and shows good understanding of software architecture principles."
 *                 developmentRecommendations:
 *                   Technical: ["Advanced algorithms and data structures", "System design patterns"]
 *                   Soft Skills: ["Leadership training", "Project management"]
 *                   Career: ["Senior developer track", "Technical mentoring opportunities"]
 *       400:
 *         description: Invalid candidateId parameter
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate not found or final assessment not available
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "something_went_wrong"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.APPLICATION_FINAL_SUMMARY,
  auth,
  paramsValidation(jobApplicationIdValidation),
  HandleErrors(getApplicationFinalSummaryController)
);

/**
 * @swagger
 * /api/candidates/application-skill-score-data/{candidateId}:
 *   get:
 *     summary: Get detailed skill-specific assessment data
 *     description: Retrieves comprehensive skill evaluation data aggregated from all completed interview rounds. The data is flattened and optimized for frontend consumption, providing detailed insights into each skill area evaluated.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: candidateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the candidate
 *         example: 456
 *     responses:
 *       200:
 *         description: Successfully retrieved skill assessment data
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/SkillSpecificAssessment'
 *             example:
 *               success: true
 *               message: "Skill assessment data retrieved successfully"
 *               code: 200
 *               data:
 *                 careerBasedSkillsScore: 8.2
 *                 skillsScores: [
 *                   {
 *                     skill_name: "JavaScript",
 *                     skill_marks: 8.5,
 *                     strengths: {
 *                       strengths: [
 *                         "Advanced ES6 knowledge",
 *                         "Async programming expertise",
 *                         "Strong debugging skills"
 *                       ]
 *                     },
 *                     potentials_gaps: {
 *                       potentialGaps: [
 *                         "Testing frameworks",
 *                         "Performance optimization"
 *                       ]
 *                     },
 *                     probability_of_success_in_this_skill: {
 *                       probabilityOfSuccessInSkill: 85
 *                     }
 *                   },
 *                   {
 *                     skill_name: "React",
 *                     skill_marks: 8.0,
 *                     strengths: {
 *                       strengths: [
 *                         "Component architecture",
 *                         "State management",
 *                         "Hooks proficiency"
 *                       ]
 *                     },
 *                     potentials_gaps: {
 *                       potentialGaps: [
 *                         "Advanced patterns",
 *                         "Performance optimization"
 *                       ]
 *                     },
 *                     probability_of_success_in_this_skill: {
 *                       probabilityOfSuccessInSkill: 80
 *                     }
 *                   }
 *                 ]
 *       400:
 *         description: Invalid candidateId parameter
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate not found or no interview data available
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "get_skill_specific_assessment_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
// candidatesRoute.get(
//   ROUTES.CANDIDATES.APPLICATION_SKILL_SCORE_DATA,
//   auth,
//   paramsValidation(getSkillSpecificAssessmentValidation),
//   HandleErrors(getApplicationSKillScoreDataController)
// );

/**
 * @swagger
 * /api/candidates/generate-final-summary:
 *   get:
 *     summary: Generate final summary for candidate application
 *     description: Triggers the generation of a comprehensive final summary for a candidate based on their interview performance, skill assessments, and overall evaluation data. The summary includes AI-powered insights and recommendations for hiring decisions.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: candidateId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique identifier of the candidate
 *         example: "456"
 *       - in: query
 *         name: jobApplicationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique identifier of the job application
 *         example: "123"
 *     responses:
 *       200:
 *         description: Final summary generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         candidateId:
 *                           type: string
 *                           description: ID of the candidate
 *                           example: "456"
 *                         jobApplicationId:
 *                           type: string
 *                           description: ID of the job application
 *                           example: "123"
 *                         orgId:
 *                           type: number
 *                           description: Organization ID
 *                           example: 1
 *                         generatedAt:
 *                           type: string
 *                           format: date-time
 *                           description: Timestamp when summary was generated
 *                           example: "2024-01-15T10:30:00Z"
 *             example:
 *               success: true
 *               message: "final_summary_generated_successfully"
 *               code: 200
 *               data:
 *                 candidateId: "456"
 *                 jobApplicationId: "123"
 *                 orgId: 1
 *                 generatedAt: "2024-01-15T10:30:00Z"
 *       400:
 *         description: Invalid or missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidateId and jobApplicationId are required"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "generate_final_summary_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GENERATE_FINAL_SUMMARY,
  auth,
  authorizedForManageCandidateProfileAccess,
  queryValidation(jobApplicationIdValidation),
  HandleErrors(generateFinalSummaryController)
);

candidatesRoute.get(
  ROUTES.CANDIDATES.GET_ALL_HIRED_CANDIDATE,
  auth,
  authorizedForHireCandidateAccess,
  HandleErrors(getAllHiredCandidate)
);

export default candidatesRoute;
