// eslint-disable-next-line import/no-extraneous-dependencies
import { RtcTokenBuilder, RtcRole } from "agora-token";
import { getSecretKeys } from "../config/awsConfig";
/**
 * Agora Token Utilities
 * Functions for creating and managing Agora tokens for video calls
 */
// import config from '../config/envConfig'; we will use in future

// Environment config
// const envConfig = config(); we will use in future

// Token expiration time in seconds (effectively never expires)
// Using a large value (10 years in seconds) for effectively permanent tokens
// const TOKEN_EXPIRATION_IN_SECONDS = 315360000;  we will use in future

interface AgoraTokenData {
  interviewId: string;
  channelName: string;
  uid: number | string;
  token: string;
}

/**
 * Generate an Agora RTC token for video calling
 *
 * @param channelName The name of the channel to join
 * @param uid User ID (can be number or string)
 * @param role User role (publisher or subscriber)
 * @param tokenExpiryInSeconds Token validity period in seconds
 * @param saveToJson Whether to save the token data to a JSON file
 * @returns The generated token data
 */
const generateAgoraToken = async (
  interviewId: string,
  channelName: string,
  uid: number | string
): Promise<AgoraTokenData> => {
  try {
    // Get Agora credentials from environment
    const keys = await getSecretKeys();
    const appId = keys.agora_appId;
    const appCertificate = keys.agora_appCertificate;

    // Validate credentials
    if (!appId || !appCertificate) {
      console.log("Agora App ID or App Certificate not configured");
      return {
        interviewId,
        channelName,
        uid,
        token: "",
      };
    }

    // Generate the token
    const token = RtcTokenBuilder.buildTokenWithUid(
      appId,
      appCertificate,
      channelName,
      uid,
      RtcRole.PUBLISHER,
      315360000,
      315360000
    );

    // Compile token data
    const tokenData: AgoraTokenData = {
      interviewId,
      channelName,
      uid,
      token,
    };

    return tokenData;
  } catch (error) {
    console.log("Error generating Agora token:", error);
    return {
      interviewId,
      channelName,
      uid,
      token: "",
    };
  }
};

export default generateAgoraToken;
