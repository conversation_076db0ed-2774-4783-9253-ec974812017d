import { EMAIL_REGEX, PASSWORD_REGEX } from "@/constants/commonConstants";
import * as yup from "yup";

export const loginValidation = (translation: (key: string) => string) =>
  yup
    .object()
    .concat(forgotPasswordValidation(translation))
    .shape({
      password: yup.string().required(translation("pass_req")),
    });

export const resetPasswordValidation = (translation: (key: string) => string) =>
  yup.object().shape({
    new_password: yup.string().required(translation("pass_req")).matches(PASSWORD_REGEX, translation("pass_val")),
    confirm_password: yup
      .string()
      .required(translation("confirm_pass_req"))
      .oneOf([yup.ref("new_password")], translation("pass_not_match")),
  });

export const verifyOTPValidation = (translation: (key: string) => string) =>
  yup.object().shape({
    otp: yup
      .number()
      .typeError(translation("valid_otp"))
      .test("len", translation("valid_otp"), (val) => val?.toString().length === 4)
      .required(translation("otp_req")),
  });

export const forgotPasswordValidation = (translation: (key: string) => string) => EmailValidation(translation);

export const EmailValidation = (translation: (key: string) => string) =>
  yup.object().shape({
    email: yup
      .string()
      .trim()
      .required(translation("email_req_m"))
      .email(translation("email_val_msg"))
      .matches(EMAIL_REGEX, translation("email_val_msg")),
  });
