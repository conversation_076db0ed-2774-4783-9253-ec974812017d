import React from "react";

function QuestionnaireIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 25 24" fill="none">
      <g clipPath="url(#clip0_9953_3370)">
        <path
          d="M19.4716 4.95688C19.3586 4.95688 19.2478 4.91091 19.1679 4.831C19.088 4.75109 19.042 4.64025 19.042 4.52727C19.042 4.41428 19.088 4.30344 19.1679 4.22353C19.2478 4.14367 19.3586 4.09766 19.4716 4.09766C19.5846 4.09766 19.6954 4.14362 19.7753 4.22353C19.8552 4.30344 19.9012 4.41428 19.9012 4.52727C19.9012 4.64068 19.8552 4.75109 19.7753 4.831C19.6954 4.91091 19.5846 4.95688 19.4716 4.95688Z"
          fill="#436EB6"
          stroke="#436EB6"
          strokeWidth="0.275"
        />
        <path
          d="M11.9997 22.9999C9.71858 23 7.48943 22.293 5.63205 20.9721L2.79336 21.7328C2.64506 21.7725 2.48692 21.7301 2.37836 21.6216C2.26984 21.5131 2.22744 21.3548 2.26718 21.2066L3.0278 18.3679C1.55862 16.3019 0.848909 13.7756 1.0269 11.23C1.21167 8.58796 2.34601 6.10031 4.221 4.22528C7.97424 0.472079 13.8656 -0.0695298 18.2295 2.93752C18.4248 3.07216 18.4741 3.33964 18.3395 3.53502C18.2049 3.7305 17.9373 3.77969 17.7419 3.64505C13.7195 0.873206 8.28871 1.37276 4.82864 4.83283C1.30533 8.35614 0.880743 14.0278 3.84105 18.0257C3.91898 18.1309 3.94467 18.266 3.91078 18.3925L3.28974 20.7103L5.60744 20.0892C5.73391 20.0554 5.86902 20.081 5.97428 20.1589C9.97223 23.1192 15.6439 22.6947 19.1672 19.1713C22.6273 15.7112 23.1268 10.2803 20.3548 6.2578C20.2202 6.06242 20.2694 5.7949 20.4648 5.66026C20.6601 5.52566 20.9277 5.5749 21.0623 5.77024C24.0696 10.1341 23.5281 16.0257 19.7748 19.7789C17.8997 21.654 15.4121 22.7883 12.7701 22.9731C12.513 22.991 12.2559 22.9999 11.9997 22.9999Z"
          fill="#436EB6"
          stroke="#436EB6"
          strokeWidth="0.275"
        />
        <path
          d="M11.9259 15.3692C11.1775 15.3692 10.5715 14.7632 10.5715 14.0184C10.5715 12.6016 11.3649 11.3221 12.6421 10.6794C13.2065 10.3953 13.5524 9.803 13.5233 9.17036C13.4883 8.40883 12.7629 7.68348 12.0014 7.64842C11.5584 7.62832 11.138 7.78456 10.8186 8.08954C10.6282 8.27144 10.4898 8.48732 10.4071 8.73112C10.2196 9.28489 9.70782 9.66075 9.13361 9.66643V9.66647C9.1291 9.66651 9.12459 9.66651 9.12008 9.66651C8.68879 9.66651 8.28002 9.45944 8.02436 9.11068C7.76565 8.7578 7.69472 8.31823 7.82974 7.9046C8.04738 7.23789 8.43485 6.62539 8.95017 6.13319C9.809 5.31298 10.9364 4.89166 12.1257 4.94609C13.1679 4.99399 14.1698 5.44822 14.9467 6.22508C15.7235 7.00194 16.1777 8.00375 16.2256 9.04607C16.3037 10.7431 15.3745 12.3328 13.8582 13.0959C13.4995 13.2764 13.2766 13.6299 13.2766 14.0184C13.2767 14.7632 12.6708 15.3692 11.9259 15.3692ZM11.9229 6.7874C11.9622 6.7874 12.0015 6.7883 12.041 6.79015C13.2539 6.84591 14.3258 7.91783 14.3816 9.13083C14.4263 10.1016 13.8951 11.0107 13.0283 11.4469C12.0428 11.9428 11.4307 12.9282 11.4307 14.0183C11.4307 14.2894 11.6512 14.5099 11.9223 14.5099C12.1969 14.5099 12.4175 14.2894 12.4175 14.0183C12.4175 13.3032 12.8215 12.6556 13.4719 12.3283C14.6859 11.7173 15.4299 10.4444 15.3674 9.08551C15.3293 8.25769 14.9641 7.45759 14.3391 6.83264C13.7141 6.20764 12.914 5.84247 12.0863 5.80437C11.1341 5.76115 10.231 6.098 9.54363 6.75453C9.12467 7.15467 8.82283 7.63132 8.6466 8.17126C8.59849 8.31853 8.62431 8.47576 8.71732 8.60267C8.81278 8.73288 8.95923 8.80729 9.11977 8.80729C9.12154 8.80729 9.12295 8.80639 9.12506 8.80725C9.33458 8.80519 9.52279 8.6638 9.59333 8.45549C9.71904 8.0843 9.93754 7.74294 10.2251 7.46824C10.6875 7.02669 11.2865 6.7874 11.9229 6.7874Z"
          fill="#436EB6"
          stroke="#436EB6"
          strokeWidth="0.275"
        />
        <path
          d="M11.9249 19.0607C11.179 19.0607 10.5723 18.4539 10.5723 17.7081C10.5723 16.9622 11.179 16.3555 11.9249 16.3555C12.6707 16.3555 13.2775 16.9622 13.2775 17.7081C13.2775 18.4539 12.6707 19.0607 11.9249 19.0607ZM11.9249 17.2147C11.6528 17.2147 11.4315 17.436 11.4315 17.7081C11.4315 17.9801 11.6528 18.2015 11.9249 18.2015C12.197 18.2015 12.4183 17.9801 12.4183 17.7081C12.4183 17.436 12.197 17.2147 11.9249 17.2147Z"
          fill="#436EB6"
          stroke="#436EB6"
          strokeWidth="0.275"
        />
      </g>
      <defs>
        <clipPath id="clip0_9953_3370">
          <rect width="24" height="24" fill="white" transform="translate(0.529297)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default QuestionnaireIcon;
