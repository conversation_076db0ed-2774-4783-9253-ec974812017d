import * as yup from "yup";

export const scheduleInterviewValidation = (translation: (key: string) => string) =>
  yup.object({
    eventTitle: yup
      .string()
      .required(translation("title_req"))
      .trim()
      .min(5, translation("title_min_5_chars"))
      .max(100, translation("title_max_100_chars")),
    jobTitle: yup.number().required(translation("job_title_req")),
    interviewType: yup.string().required(translation("interview_type_req")),
    jobId: yup.string().required(translation("job_id_req")),
    date: yup
      .string()
      .required(translation("date_req"))
      .test("is-not-in-past", translation("cannot_schedule_interview_past"), function (dateStr) {
        if (!dateStr) return true;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const inputDate = new Date(dateStr);
        inputDate.setHours(0, 0, 0, 0);
        return inputDate >= today;
      })
      .test("not-more-than-month-in-advance", translation("cannot_schedule_more_than_one_month_in_advance"), function (dateStr) {
        if (!dateStr) return true;
        const oneMonthFromNow = new Date();
        oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);
        oneMonthFromNow.setHours(0, 0, 0, 0);
        const inputDate = new Date(dateStr);
        inputDate.setHours(0, 0, 0, 0);
        return inputDate <= oneMonthFromNow;
      }),

    startTime: yup
      .string()
      .required(translation("start_time_req"))
      .matches(/^([01]\d|2[0-3]):([0-5]\d)$/, translation("invalid_time_format"))
      .test("not-in-past-today", translation("cannot_schedule_interview_in_past"), function (startTime) {
        if (!startTime) return true;

        const { date } = this.parent;
        if (!date) return true;

        const selectedDate = new Date(date);
        const today = new Date();

        const [selectedHour, selectedMinute] = startTime.split(":").map(Number);

        // Set time on selectedDate to match startTime
        selectedDate.setHours(selectedHour, selectedMinute, 0, 0);

        // If the selected date is today, check time
        if (today.toDateString() === selectedDate.toDateString() && selectedDate.getTime() < today.getTime()) {
          return false; // Invalid: selected time is in the past
        }

        return true;
      }),

    description: yup.string().nullable().optional().max(2000, translation("description_max_2000_chars")),
    endTime: yup
      .string()
      .required(translation("end_time_req"))
      .matches(/^([01]\d|2[0-3]):([0-5]\d)$/, translation("invalid_time_format"))
      .test("is-greater-than-start-time", translation("end_time_must_be_after_start_time"), function (endTime) {
        const { startTime } = this.parent;
        if (!startTime || !endTime) return true;

        // Compare times
        return endTime > startTime;
      })
      .test("is-at-least-30-min", translation("interview_must_be_at_least_10_min"), function (endTime) {
        const { startTime } = this.parent;
        if (!startTime || !endTime) return true;

        // Parse hours and minutes
        const [startHour, startMin] = startTime.split(":").map(Number);
        const [endHour, endMin] = endTime.split(":").map(Number);

        // Calculate total minutes
        const startTotalMins = startHour * 60 + startMin;
        const endTotalMins = endHour * 60 + endMin;
        const diffMins = endTotalMins - startTotalMins;

        // Ensure at least 30 minutes difference
        return diffMins >= 10;
      })
      .test("max-duration", translation("interview_must_not_exceed_2_hours"), function (endTime) {
        const { startTime } = this.parent;
        if (!startTime || !endTime) return true;

        // Parse hours and minutes
        const [startHour, startMin] = startTime.split(":").map(Number);
        const [endHour, endMin] = endTime.split(":").map(Number);

        // Calculate total minutes
        const startTotalMins = startHour * 60 + startMin;
        const endTotalMins = endHour * 60 + endMin;
        const diffMins = endTotalMins - startTotalMins;

        // Ensure interview is no longer than 2 hours (120 minutes)
        return diffMins <= 120;
      }),
    interviewer: yup.number().required(translation("interviewer_req")),
    candidate: yup
      .number()
      .test("job-title-required", translation("please_select_job_title_first"), function () {
        const { jobTitle } = this.parent;
        return !!jobTitle && jobTitle > 0;
      })
      .required(translation("candidate_req")),
  });

export const addUpdateQuestionValidation = (translation: (key: string) => string) =>
  yup.object().shape({
    question: yup.string().required(translation("question_req")).min(10, translation("question_min_10_chars")),
  });

export const addAnswerValidation = (translation: (key: string) => string) =>
  yup
    .object()
    .shape({
      behavioralInfo: yup.string().optional().max(1500, translation("behavioral_info_max_1500_chars")),
    })
    .test("dynamic-answer-validation", "", function (values) {
      console.log("values========", values);
      // Get all field names from the form values
      const fieldNames = Object.keys(values || {});

      // Create a new schema object to add dynamic validations
      let dynamicSchema = yup.object().shape({});

      // Add validation for each answer field
      fieldNames.forEach((fieldName) => {
        if (fieldName.startsWith("answer-")) {
          // Add validation for this specific answer field
          dynamicSchema = dynamicSchema.shape({
            [fieldName]: yup.string().optional().max(2000, translation("answer_max_2000_chars")),
          });
        }
      });

      try {
        dynamicSchema.validateSync(values, { abortEarly: false });
        return true;
      } catch (error) {
        console.log("error", error);
        return false;
      }
    });
