import http from "http";
import { Server, Socket } from "socket.io";
import {
  createClient,
  ListenLiveClient,
  LiveTranscriptionEvents,
} from "@deepgram/sdk";
import * as jwt from "jsonwebtoken";
import { Repository } from "typeorm";
import pdf from "pdf-parse";
import dbConnection from "../db/dbConnection";
import {
  ATTEMPT,
  PDF_PARSING_MAX_ATTEMPTS,
  PASSWORD_REGEX,
  REDIS_KEYS,
  SOCKET_ROUTES,
} from "./constants";
import { getSecretKeys } from "../config/awsConfig";
import Cache from "../db/cache";
import InterviewServices from "../features/interview/services";
import ActivityLogModel from "../schema/s9-innerview/activity_logs";

export const generateRandomPassword = () => {
  const lower = "abcdefghijklmnopqrstuvwxyz";
  const upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const digits = "0123456789";
  const symbols = "!@#$%^&*()-_=+[]{}|;:,.<>?";
  const all = lower + upper + digits + symbols;

  const getRandom = (chars) => chars[Math.floor(Math.random() * chars.length)];

  // Start with one from each required group
  const password = [
    getRandom(lower),
    getRandom(upper),
    getRandom(digits),
    getRandom(symbols),
  ];

  // Fill the rest up to a random length between 8–16
  const targetLength = Math.floor(Math.random() * 9) + 8;
  while (password.length < targetLength) {
    password.push(getRandom(all));
  }

  // Shuffle and return
  const final = password.sort(() => 0.5 - Math.random()).join("");

  return PASSWORD_REGEX.test(final) ? final : generateRandomPassword();
};

export async function getRepositories<T extends Record<string, any>>(
  modelMap: T
): Promise<{ [K in keyof T]: Repository<InstanceType<T[K]>> }> {
  const entries = await Promise.all(
    Object.entries(modelMap).map(async ([key, model]) => [
      key,
      await dbConnection.getS9InnerViewDatabaseRepository(model),
    ])
  );
  return Object.fromEntries(entries) as {
    [K in keyof T]: Repository<InstanceType<T[K]>>;
  };
}

// Retry Mechanism for PDF parsing
export const parsePdfWithRetries = async (
  buffer: Buffer,
  maxAttempts = PDF_PARSING_MAX_ATTEMPTS,
  attempt = ATTEMPT
): Promise<{ text: string } | null> => {
  try {
    const pdfParsed = await pdf(buffer);
    if (pdfParsed.text.trim() === "") {
      // Succesret
      return null;
    }
    return pdfParsed;
  } catch (error) {
    console.log("Error parsing PDF =========>", error);
  }
  if (attempt + 1 < maxAttempts) {
    return parsePdfWithRetries(buffer, maxAttempts, attempt + 1); // Retry
  }

  // All attempts failed
  return null;
};

export const setUpDeepgram = async (cache: Cache, interviewId: number) => {
  const keys = await getSecretKeys();
  let keepAlive: ReturnType<typeof setInterval> | null = null;
  let connection: ListenLiveClient | null = null;

  // Cleanup function
  const cleanup = () => {
    console.log("Cleaning up Deepgram connection...");
    if (keepAlive) {
      clearInterval(keepAlive);
      keepAlive = null;
    }
    if (connection) {
      try {
        connection.off(LiveTranscriptionEvents.Open, () => {});
        connection.off(LiveTranscriptionEvents.Close, () => {});
        connection.off(LiveTranscriptionEvents.Error, () => cleanup());
        connection.off(LiveTranscriptionEvents.Transcript, () => {});
        connection.requestClose();
      } catch (e) {
        console.error("Error during cleanup:", e);
      } finally {
        connection = null;
      }
    }
  };

  try {
    // STEP 1: Create a Deepgram client using the API key
    const deepgram = createClient(keys.deepgram_secret_key);

    // STEP 2: Create a live transcription connection
    connection = deepgram.listen.live({
      model: "nova-3",
      language: "en-US",
      smart_format: true,
      interim_results: true,
      punctuate: true,
      diarize: true,
      alive: true,
    });

    // Setup keep alive
    keepAlive = setInterval(() => {
      if (connection?.getReadyState() === 1) {
        // Check if connection is open
        connection?.keepAlive();
        console.log("Deepgram connection keep alive");
      }
    }, 8000);

    // STEP 3: Set up event handlers
    connection.on(LiveTranscriptionEvents.Open, () => {
      console.log("Deepgram connection opened");
    });

    connection.on(LiveTranscriptionEvents.Close, () => {
      console.log("Deepgram connection closed");
      cleanup();
    });

    connection.on(LiveTranscriptionEvents.Error, () => cleanup());

    connection.on(LiveTranscriptionEvents.Transcript, async (data) => {
      const { transcript } = data.channel.alternatives[0];
      console.log("Transcript received:", transcript);

      console.log("is_final======", data.is_final);

      if (transcript && data.is_final) {
        try {
          // Get current transcript
          const currentFullTranscript =
            (await cache.get(
              `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`
            )) || "";

          // Append new transcript with spacing/punctuation
          const updatedTranscript = currentFullTranscript
            ? `${currentFullTranscript} ${transcript}`
            : transcript;

          console.log("updatedTranscript", updatedTranscript);

          // Save transcript to Redis if interviewId is available
          if (interviewId) {
            console.log(
              `Saving transcript for interview in deepgram: ${interviewId}`
            );
            await cache.set(
              `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`,
              updatedTranscript
            );
          }
        } catch (e) {
          console.error("Error processing transcript:", e);
        }
      }
    });

    connection.on(LiveTranscriptionEvents.Metadata, (data) => {
      console.log("Metadata:", data);
    });

    // Return cleanup function along with connection
    return {
      connection,
      cleanup,
    };
  } catch (error) {
    console.error("Failed to set up Deepgram:", error);
    cleanup();
    return {
      connection: null,
      cleanup: () => {},
    };
  }
};

/**
 * Socket.io Server Manager Class
 * Handles socket.io server initialization and event handling
 */
export class SocketIOManager {
  private io: Server;

  private candidateIo: Server;

  private cache: Cache;

  private lastProcessedTime: number;

  private deepgramConnection: ListenLiveClient;

  private interviewerId: string;

  private candidateId: string;

  private deepgramCleanup: () => void;

  /**
   * Initialize Socket.io Server Manager
   * @param httpServer - HTTP Server to attach Socket.io to
   * @param cache - Cache instance for storing transcripts
   */
  constructor(
    httpServer: http.Server<
      typeof http.IncomingMessage,
      typeof http.ServerResponse
    >
  ) {
    this.cache = new Cache();
    this.lastProcessedTime = 0;
    this.deepgramConnection = null;
    this.interviewerId = null;
    this.candidateId = null;

    // Initialize Socket.io server
    this.io = new Server(httpServer, {
      path: SOCKET_ROUTES.CONDUCT_INTERVIEW,
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    });

    this.candidateIo = new Server(httpServer, {
      path: SOCKET_ROUTES.CANDIDATE_CONDUCT_INTERVIEW,
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    });

    this.setupEventHandlers();
  }

  /**
   * Setup Socket.io event handlers
   */
  private async setupEventHandlers(): Promise<void> {
    this.io.on("connection", async (socket: Socket) => {
      console.log("New user connected : interviewer", socket.connected);

      const interviewId = +socket.handshake.query.interviewId;
      if (interviewId) {
        const { connection, cleanup } = await setUpDeepgram(
          this.cache,
          interviewId
        );
        this.deepgramConnection = connection;
        this.deepgramCleanup = cleanup;
      }

      // Handle disconnect event
      socket.on("disconnect", (reason) => {
        this.handleDisconnect(socket, reason);
      });

      // Handle message event
      socket.on("message", async (payload, callback) => {
        console.log("payload interviewer", payload);
        this.handleMessage(socket, payload, callback);
      });
    });

    this.candidateIo.on("connection", async (socket: Socket) => {
      console.log("New user connected : candidate", socket.connected);

      // Handle disconnect event
      socket.on("disconnect", async (reason) => {
        const candidateKey = REDIS_KEYS.INTERVIEW_CANDIDATE_KEY.replace(
          "{interviewId}",
          String(socket.handshake.query.interviewId)
        ).replace("{candidateId}", String(this.candidateId));
        await this.cache.del(`${candidateKey}`);

        console.log("candidateKey deleted", await this.cache.get(candidateKey));

        console.log("Disconnected : candidate");
        console.log("Reason: candidate", reason);
        this.candidateId = null;
      });

      // Handle message event
      socket.on("message", async (payload: { candidateId: string }) => {
        const { candidateId } = payload;
        this.candidateId = candidateId;

        console.log("payload candidate", payload);
        if (socket.connected && payload) {
          console.log("candidateId", candidateId);
          const candidateKey = REDIS_KEYS.INTERVIEW_CANDIDATE_KEY.replace(
            "{interviewId}",
            String(socket.handshake.query.interviewId)
          ).replace("{candidateId}", String(candidateId));
          await this.cache.set(`${candidateKey}`, candidateId, 30);
        }
      });
    });
  }

  /**
   * Handle socket disconnect event
   * @param socket - Socket instance
   * @param connection - Deepgram connection
   * @param reason - Disconnect reason
   * @param details - Disconnect details
   */
  private async handleDisconnect(
    socket: Socket,
    reason: string
  ): Promise<void> {
    console.log("Disconnected... interviewer");
    console.log("Reason: interviewer", reason);

    this.deepgramCleanup();

    const interviewId = +socket.handshake.query.interviewId;
    const interviewerKey = REDIS_KEYS.INTERVIEW_INTERVIEWER_KEY.replace(
      "{interviewId}",
      String(interviewId)
    ).replace("{interviewerId}", String(this.interviewerId));
    await this.cache.del(`${interviewerKey}`);

    console.log("interviewerKey======", interviewerKey);

    console.log("interviewerKey deleted", await this.cache.get(interviewerKey));

    const transcripts = await this.cache.get(
      `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`
    );

    if (interviewId) {
      // Save transcript with interviewId as key if available
      console.log(
        `Saving transcript for interview on disconnect: ${interviewId}`
      );
      this.cache.set(
        `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`,
        transcripts || ""
      );
    } else {
      // Fallback to socket.id if interviewId is not available
      console.log("No interviewId found, using socket.id as fallback");
      this.cache.set(socket.id, transcripts || "");
    }

    this.interviewerId = null;

    this.deepgramConnection?.requestClose();
    this.deepgramConnection?.removeAllListeners();
  }

  /**
   * Handle socket message event
   * @param socket - Socket instance
   * @param connection - Deepgram connection
   * @param payload - Message payload
   * @param callback - Message callback
   */
  private async handleMessage(
    socket: Socket,
    payload: any,
    callback: Function
  ): Promise<null> {
    try {
      let userId = null;
      try {
        console.log("Inside socket middleware");
        const keys = await getSecretKeys();
        const jwtToken = socket.handshake.headers.authorization;
        const token = jwtToken?.split(" ")[1];

        if (!jwtToken) {
          return callback({
            success: false,
            message: "token_req",
            code: 401,
          });
        }

        const verify = jwt.verify(token, keys.token_key) as jwt.JwtPayload;
        userId = verify?.id;

        if (!verify) {
          return callback({
            success: false,
            message: "invalid_token",
            code: 401,
          });
        }
      } catch (error) {
        if (error.name === "TokenExpiredError") {
          return callback({
            success: false,
            message: "token_expired",
            code: 401,
          });
        }
        console.log("socket middleware error:", error);
        return callback({
          success: false,
          message: "invalid_token",
          code: 401,
        });
      }
      console.log("user id=========>>>>>", userId);

      // Method to process time-based events
      const processTimeBasedEvents = async (currentTimeInSeconds: number) => {
        // Check if 5 minutes (300 seconds) have passed since last processing
        const fiveMinutesInSeconds = 300;
        const currentTimeBlock = Math.floor(
          currentTimeInSeconds / fiveMinutesInSeconds
        );
        const lastTimeBlock = Math.floor(
          this.lastProcessedTime / fiveMinutesInSeconds
        );

        console.log("currentTimeBlock", currentTimeBlock);
        console.log("lastTimeBlock", lastTimeBlock);

        // If we've moved to a new 5-minute block
        if (currentTimeBlock > lastTimeBlock) {
          const interviewId = +socket.handshake.query.interviewId;
          const question = await InterviewServices.generateFollowUpQuestions({
            jobSkillId: payload.jobSkillId,
            type: payload.type,
            interviewId,
            jobId: payload.jobId,
          });
          console.log(
            `5-minute interval reached at ${currentTimeInSeconds} seconds`
          );
          // Update the last processed time
          this.lastProcessedTime = currentTimeInSeconds;
          console.log("question===>>>", question);
          return callback({
            success: true,
            data: JSON.parse(question).questionText,
            code: 200,
          });
        }

        // Update the last processed time
        this.lastProcessedTime = currentTimeInSeconds;
        return null;
      };
      console.log("payload interviewer", payload);

      if (socket.connected) {
        const { interviewId } = socket.handshake.query;
        this.interviewerId = String(userId);
        const interviewerKey = REDIS_KEYS.INTERVIEW_INTERVIEWER_KEY.replace(
          "{interviewId}",
          String(interviewId)
        ).replace("{interviewerId}", String(userId));
        await this.cache.set(`${interviewerKey}`, String(userId), 60);

        if (
          payload?.time &&
          payload?.blob &&
          this.deepgramConnection?.getReadyState() === 1
        ) {
          console.log("socket connected");
          processTimeBasedEvents(payload.time);
          console.log("payload", payload);
          this.deepgramConnection?.send(payload.blob);
          console.log("Sent audio data to Deepgram");
        }
        if (payload?.blob && this.deepgramConnection?.getReadyState() >= 2) {
          console.log("Deepgram connection lost, reconnecting...");
          this.deepgramConnection?.requestClose();
          this.deepgramConnection?.removeAllListeners();
          const { connection, cleanup } = await setUpDeepgram(
            this.cache,
            +interviewId
          );
          this.deepgramConnection = connection;
          this.deepgramCleanup = cleanup;
        }
      }
      return null;
    } catch (error) {
      console.log("socket connection error:", error);
      return null;
    }
  }
}

/**
 * Add an entry to the activity_logs table
 * @param params - Activity log parameters
 */
export async function addActivityLog(params: {
  orgId: number;
  logType: string;
  userId: number;
  entityId: number;
  entityType?: string;
  oldValue?: string;
  newValue?: string;
  comments: string;
}) {
  try {
    const s9InnerviewDataSource = await dbConnection.getS9InnerviewDataSource();
    const activityLogRepo =
      s9InnerviewDataSource.getRepository(ActivityLogModel);
    const log = activityLogRepo.create({
      orgId: params.orgId ?? null,
      logType: params.logType,
      userId: params.userId ?? null,
      entityId: params.entityId ?? null,
      entityType: params.entityType ?? null,
      oldValue: params.oldValue ?? null,
      newValue: params.newValue ?? null,
      comments: params.comments ?? null,
    });
    await activityLogRepo.save(log);
    return true;
  } catch (error) {
    console.error("Error adding activity log:", error);
    return false;
  }
}
export const stripHtmlTags = (html: string) => {
  // 1. Decode HTML entities like &lt; &gt; etc.
  const htmlDecoded = html
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&nbsp;/g, " ");

  // 2. Remove HTML tags
  const plainText = htmlDecoded.replace(/<[^>]*>/g, "");

  // 3. Trim and clean up multiple newlines
  return plainText.replace(/\n\s*\n/g, "\n").trim();
};
