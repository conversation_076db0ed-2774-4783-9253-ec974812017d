import React, { useState, useEffect } from "react";
import UploadDocumentIcon from "../svgComponents/UploadDocumentIcon";
import { useTranslations } from "next-intl";

interface UploadBoxProps {
  UploadBoxClassName?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  inputRef?: React.RefObject<HTMLInputElement | null>;
  isLoading?: boolean;
  uploadingMessages?: string[]; // Optional array of dynamic messages
  messageInterval?: number; // Optional interval for message rotation (default: 2000ms)
  disabled?: boolean;
}

const UploadBox = ({ UploadBoxClassName, onChange, inputRef, isLoading, uploadingMessages, messageInterval = 2000 }: UploadBoxProps) => {
  const t = useTranslations();
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  // Dynamic message rotation effect
  useEffect(() => {
    if (!isLoading || !uploadingMessages || uploadingMessages.length <= 1) {
      return;
    }

    const interval = setInterval(() => {
      setCurrentMessageIndex((prevIndex) => (prevIndex + 1) % uploadingMessages.length);
    }, messageInterval);

    return () => clearInterval(interval);
  }, [isLoading, uploadingMessages, messageInterval]);

  // Reset message index when loading starts
  useEffect(() => {
    if (isLoading) {
      setCurrentMessageIndex(0);
    }
  }, [isLoading]);

  // Determine what message to show during loading
  const getLoadingMessage = () => {
    if (uploadingMessages && uploadingMessages.length > 0) {
      return uploadingMessages[currentMessageIndex];
    }
    return t("uploading");
  };

  return (
    <div className={`upload-card ${UploadBoxClassName}`}>
      <input type="file" accept=".pdf" onChange={onChange} disabled={isLoading} ref={inputRef} />
      <div className="upload-box-inner">
        <UploadDocumentIcon />
        {!isLoading ? (
          <p>
            {t("upload_doc")}
            <br />
            {t("max_file_size")}
          </p>
        ) : (
          <p className="uploading-message">{getLoadingMessage()}</p>
        )}
      </div>
    </div>
  );
};

export default UploadBox;
