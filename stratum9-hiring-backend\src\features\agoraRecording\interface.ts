/**
 * Agora Recording feature interfaces
 */

/**
 * Agora Token Data
 */
export interface AgoraTokenData {
  error?: string; // Error message if any
  message?: string;
  status?: boolean;
  interviewId: number;
  channelName: string;
  uid: number;
  token: string;
}

/**
 * Resource Acquisition Request
 */
export interface AcquireResourceRequest {
  cname: string; // Channel name
  uid: string; // Recording UID (unique identifier for recording)
  clientRequest: any;
}

/**
 * Resource Acquisition Response
 */
export interface AcquireResourceResponse {
  resourceId: string;
}

/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * Recording Mode Type
 */
export enum RecordingMode {
  MIX = "mix", // Composite mode (all participants in one file)
  INDIVIDUAL = "individual", // Individual mode (separate files per participant)
}

/**
 * Stream Types for Recording
 */
export enum StreamTypes {
  AUDIO_ONLY = 0,
  VIDEO_ONLY = 1,
  AUDIO_AND_VIDEO = 2,
}

/**
 * Channel Type
 */
export enum ChannelType {
  COMMUNICATION = 0,
  LIVE_BROADCAST = 1,
}

/**
 * Storage Vendor Type
 */
export enum StorageVendor {
  AWS_S3 = 1,
}

/**
 * Recording Configuration
 */
export interface RecordingConfig {
  streamTypes: StreamTypes;
  channelType: ChannelType;
  extensionParams?: {
    enableNTPtimestamp?: boolean; // Enable NTP timestamp for synchronization
  };
  transcodingConfig?: {
    width?: number;
    height?: number;
    fps?: number;
    bitrate?: number;
    mixedVideoLayout?: number;
  };
  maxIdleTime: number;
  subscribeVideoUids?: string[];
  subscribeAudioUids?: string[];
  subscribeUidGroup?: number;
  format: string[];
}

/**
 * S3 Storage Configuration
 */
export interface S3StorageConfig {
  vendor: StorageVendor;
  region: number;
  bucket: string;
  accessKey: string;
  secretKey: string;
  fileNamePrefix?: string[];
}

/**
 * Start Recording Request
 */
export interface StartRecordingRequest {
  cname: string;
  uid: string;
  clientRequest: {
    token?: string; // Optional token for secure channels
    recordingConfig: RecordingConfig;
    recordingFileConfig: {
      avFileType: string[];
    };
    storageConfig: S3StorageConfig;
  };
}

/**
 * Recording File Information
 */
export interface RecordingFile {
  fileName: string;
  trackType: string;
  uid: string;
  mixedAllUser?: boolean;
  isPlayable: boolean;
  sliceStartTime?: string;
}

/**
 * Stop Recording Request
 */
export interface StopRecordingRequest {
  cname: string;
  uid: string;
  clientRequest: { async_stop: boolean };
}
