import * as Sentry from "@sentry/node";
import dbConnection from "../dbConnection";

import { ICreateNotification } from "../utils/commonInterfaces";
import NotificationsModal from "../schema/notifications";

class NotificationServices {

  static createNotification = async (
    orgId: number,
    userId: number,
    data: ICreateNotification
  ) => {
    const { type, title, description, relatedId, additionalInfo } = data;
    try {
      const dataSource = await dbConnection.getDataSource();
      const notificationsRepo = dataSource.getRepository(NotificationsModal);
      const notification = new NotificationsModal();

      notification.organizationId = orgId;
      notification.userId = userId;
      notification.type = type;
      notification.title = title;
      notification.description = description;
      notification.relatedId = relatedId;
      notification.additionalInfo = additionalInfo;
      notification.isWatched = false;

      await notificationsRepo.save(notification);

      return {
        success: true,
        message: "Notification created successfully",
      };
    } catch (error) {
      console.error("createNotification error ===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: "Failed to create notification",
      };
    }
  };

}

export default NotificationServices;
