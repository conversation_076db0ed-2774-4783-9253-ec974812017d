"use client";
import React, { useEffect, useState } from "react";
import Button from "@/components/formElements/Button";
import style from "../../../styles/conductInterview.module.scss";
import ArrowDownIcon from "@/components/svgComponents/ArrowDownIcon";
import { getFinalAssessmentByCandidate, submitAssessment } from "@/services/assessmentService";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import Loader from "@/components/loader/Loader";
import { IAssessmentData, Question } from "@/interfaces/candidateFinalAssessment";
import { useTranslations } from "next-intl";
import { IAssessmentSubmission } from "@/interfaces/finalAssessment";
import { commonConstants } from "@/constants/commonConstants";
import AssessmentInstructionsModal from "@/components/commonModals/AssessmentInstructionsModal";
import "./candidateAssessmentStyles.css";

const CandidateAssessment = () => {
  const t = useTranslations();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [assessmentData, setAssessmentData] = useState<IAssessmentData | null>(null);
  const [currentGroupIndex, setCurrentGroupIndex] = useState(0);
  const [expandedQuestions, setExpandedQuestions] = useState<{ [key: number]: boolean }>({});
  const [finalAssessmentId, setFinalAssessmentId] = useState<number | null>(null);
  const [candidateAnswers, setCandidateAnswers] = useState<{ [key: number]: string }>({});
  const [finalAssessmentToken, setFinalAssessmentToken] = useState<string>("");
  const [assessmentSubmitted, setAssessmentSubmitted] = useState(false);
  const [showInstructionsModal, setShowInstructionsModal] = useState(true);
  const [emailVerified, setEmailVerified] = useState(false);
  const [candidateEmail, setCandidateEmail] = useState<string>("");

  useEffect(() => {
    // Get the token from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get(commonConstants.token);

    if (token) {
      setFinalAssessmentToken(token);
    } else {
      toastMessageError(t("something_went_wrong"));
    }
  }, []);

  // Fetch assessment questions when finalAssessmentId is available and email is verified
  useEffect(() => {
    const fetchAssessmentQuestions = async () => {
      if (!finalAssessmentId) {
        return;
      }

      // Only fetch assessment questions if email is verified
      if (!emailVerified) {
        return;
      }

      try {
        setLoading(true);
        const response = await getFinalAssessmentByCandidate(finalAssessmentId);

        if (response.data && response.data.success) {
          setAssessmentData(response.data.data);

          // Initialize all questions as expanded
          const questions = response.data.data.questionGroups?.[0]?.questions || [];
          if (questions.length > 0) {
            const initialExpandState = Object.fromEntries(questions.map((question: Question) => [question.id, true]));

            setExpandedQuestions(initialExpandState);
          }
        } else {
          toastMessageError(t(response.data?.message || "failed_to_fetch_assessment_questions"));
        }
      } catch (error) {
        console.error("Error fetching assessment questions:", error);
        toastMessageError(t("an_error_occurred_while_fetching_assessment_questions"));
      } finally {
        setLoading(false);
      }
    };

    if (finalAssessmentId && emailVerified) {
      fetchAssessmentQuestions();
    }
  }, [finalAssessmentId, emailVerified, t]);

  // Toggle question expansion
  const handleToggleQuestion = (questionId: number) => {
    setExpandedQuestions((prev) => ({
      ...prev,
      [questionId]: !prev[questionId],
    }));
  };

  // Handle navigation to next question group
  const handleNextGroup = () => {
    if (assessmentData && currentGroupIndex < assessmentData.questionGroups.length - 1) {
      const nextIndex = currentGroupIndex + 1;
      setCurrentGroupIndex(nextIndex);

      // Initialize expanded state for questions in the next group
      const nextGroupQuestions = assessmentData.questionGroups[nextIndex].questions;
      const initialExpandState = nextGroupQuestions.reduce(
        (acc, question) => {
          acc[question.id] = true;
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      setExpandedQuestions(initialExpandState);
    }
  };

  // Handle navigation to previous question group
  const handlePreviousGroup = () => {
    if (assessmentData && currentGroupIndex > 0) {
      const prevIndex = currentGroupIndex - 1;
      setCurrentGroupIndex(prevIndex);

      // Initialize expanded state for questions in the previous group
      const prevGroupQuestions = assessmentData.questionGroups[prevIndex].questions;
      const initialExpandState = prevGroupQuestions.reduce(
        (acc, question) => {
          acc[question.id] = true;
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      setExpandedQuestions(initialExpandState);
    }
  };

  // Handle answer selection
  const handleAnswerSelect = (questionId: number, optionId: string) => {
    setCandidateAnswers((prev) => ({
      ...prev,
      [questionId]: optionId,
    }));
  };

  // Handle email verification success
  const handleEmailVerificationSuccess = (email: string, finalAssessmentId: number) => {
    setCandidateEmail(email);
    setEmailVerified(true);
    setFinalAssessmentId(finalAssessmentId);
    setShowInstructionsModal(false);
  };

  // Check if all questions have been answered
  // const allQuestionsAnswered = () => {
  //   if (!assessmentData) return false;

  //   let allQuestions: Question[] = [];
  //   assessmentData.questionGroups.forEach((group) => {
  //     allQuestions = [...allQuestions, ...group.questions];
  //   });

  //   return allQuestions.every((question) => candidateAnswers[question.id]);
  // };

  // Handle assessment submission
  const handleSubmitAssessment = async () => {
    // Check if all questions are answered
    // if (!allQuestionsAnswered()) {
    //   toastMessageError(t("please_answer_all_questions_before_submitting"));
    //   return;
    // }

    if (!assessmentData || !finalAssessmentId) {
      toastMessageError(t("assessment_data_is_missing"));
      return;
    }

    try {
      setSubmitting(true);

      // Collect all answers
      const answers = Object.entries(candidateAnswers).map(([questionId, selectedAnswer]) => ({
        questionId: +questionId,
        answer: selectedAnswer,
      }));

      const submissionData: IAssessmentSubmission = {
        finalAssessmentId: finalAssessmentId, // Using encrypted ID from URL
        candidateEmail: candidateEmail,
        answers,
      };

      // Submit answers using the new API
      const response = await submitAssessment(submissionData);
      if (response.data?.success === true) {
        toastMessageSuccess(t(response.data.message || "assessment_submitted_successfully"));
        setAssessmentSubmitted(true);
      } else if (response.data?.success === false) {
        toastMessageError(t(response.data.message || "failed_to_submit_assessment"));
      } else {
        toastMessageError(t("an_error_occurred_while_submitting_assessment"));
      }
    } catch (error) {
      console.error("Error submitting assessment:", error);
      toastMessageError(t("an_error_occurred_while_submitting_assessment"));
    } finally {
      setSubmitting(false);
    }
  };

  // Get current question group
  const currentGroup = assessmentData?.questionGroups[currentGroupIndex];

  // Format group type for display
  const formatGroupType = (type: string) => {
    return type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  // Determine if this is the last group
  const isLastGroup = assessmentData ? currentGroupIndex === assessmentData.questionGroups.length - 1 : false;

  // Group questions by skillId
  const getQuestionsGroupedBySkill = () => {
    if (!currentGroup) return [];

    // Group questions by skillId
    const groupedQuestions: { [key: number]: { skillTitle: string; questions: Question[] } } = {};

    currentGroup.questions.forEach((question) => {
      if (!groupedQuestions[question.skillId]) {
        groupedQuestions[question.skillId] = {
          skillTitle: question.skillTitle,
          questions: [],
        };
      }

      groupedQuestions[question.skillId].questions.push(question);
    });

    // Convert to array for rendering
    return Object.values(groupedQuestions);
  };

  const skillGroups = getQuestionsGroupedBySkill();

  // Always show instructions modal if not verified, regardless of other states
  if (showInstructionsModal && finalAssessmentToken) {
    return (
      <div className={style.conductInterviewWrapper}>
        <AssessmentInstructionsModal finalAssessmentToken={finalAssessmentToken} onVerificationSuccess={handleEmailVerificationSuccess} />
      </div>
    );
  }

  // Render loading state
  if (loading) {
    return (
      <div className={style.conductInterviewWrapper}>
        <div className="container">
          <div className="text-center py-5">
            <Loader />
            <h1 className="mt-3">{t("loading_assessment_questions")}</h1>
          </div>
        </div>
      </div>
    );
  }

  // Render error state if no assessment data
  if (!loading && !assessmentData) {
    return (
      <div className={style.conductInterviewWrapper}>
        <div className="container">
          <div className="text-center py-5">{t("no_assessment_data_found")}</div>
        </div>
      </div>
    );
  }

  // Render thank you page if assessment is submitted
  if (assessmentSubmitted) {
    return (
      <div className={style.conductInterviewWrapper}>
        <div className="container">
          <div className="text-center py-5">
            <h2 className="mb-4">{t("thank_you")}</h2>
            <p className="mb-3">{t("your_assessment_has_been_submitted_successfully")}</p>
            <p>{t("we_will_review_your_answers_and_get_back_to_you_soon")}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={style.conductInterviewWrapper}>
      <div className="container">
        <div className="text-center mt-4 mb-4">
          <h2>
            {t("candidate")} <span className="text-primary">{t("assessment")}</span>
          </h2>
        </div>

        <div className="inner-section">
          <div className="section-heading d-flex justify-content-between mb-4">
            <h2 className="m-0">
              {currentGroup && (
                <>
                  {t("group")} {currentGroupIndex + 1} of {assessmentData?.questionGroups.length}: <span>{formatGroupType(currentGroup.type)}</span>
                </>
              )}
            </h2>
          </div>

          <div className="row">
            <div className="col-md-12">
              {skillGroups.map((skillGroup, skillGroupIndex) => (
                <div key={`skill-${skillGroupIndex}`} className="skill-group mb-4">
                  <h3 className="skill-title mb-3" style={{ color: "#2c7be5", borderBottom: "1px solid #e3e6f0", paddingBottom: "10px" }}>
                    {t("skill")}: {skillGroup.skillTitle}
                  </h3>

                  {skillGroup.questions.map((question, index) => (
                    <div key={question.id} className="interview-question-card with-border" onClick={() => handleToggleQuestion(question.id)}>
                      <p className="tittle">
                        {t("question")} {index + 1} <ArrowDownIcon className={!expandedQuestions[question.id] ? "rotate" : ""} />
                      </p>
                      <h5>{question.question}</h5>
                      {expandedQuestions[question.id] && (
                        <div className="question-body" onClick={(e) => e.stopPropagation()}>
                          {question.options.options.map((option) => {
                            const isSelected = candidateAnswers[question.id] === option.id;

                            return (
                              <label
                                key={option.id}
                                className={`answer-strap ${isSelected ? "selected-answer" : ""}`}
                                htmlFor={`question_${question.id}_option_${option.id}`}
                                style={{ display: "block", cursor: "pointer" }}
                              >
                                <div className="radio-wrapper" style={{ pointerEvents: "none" }}>
                                  <input
                                    className="radio-input form-check-input"
                                    type="radio"
                                    name={`question_${question.id}`}
                                    id={`question_${question.id}_option_${option.id}`}
                                    value={option.id}
                                    checked={isSelected}
                                    onChange={() => handleAnswerSelect(question.id, option.id)}
                                    style={{ pointerEvents: "auto" }}
                                  />
                                  <span className="radio-label" style={{ pointerEvents: "none" }}>
                                    {option.text}
                                  </span>
                                </div>
                              </label>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="button-align" style={{ justifyContent: "space-between" }}>
          {/* Show Previous button if not on the first group */}
          <div>
            {currentGroupIndex > 0 && (
              <Button className="secondary-btn rounded-md" onClick={handlePreviousGroup} disabled={submitting}>
                <span style={{ marginRight: "8px" }}>&#8592;</span> {t("previous_skill_assessment")}
              </Button>
            )}
          </div>

          {/* Show Next button if not on the last group */}
          <div>
            {!isLastGroup ? (
              <Button className="primary-btn rounded-md" onClick={handleNextGroup} disabled={submitting}>
                {t("next_skill_assessment")} <span style={{ marginLeft: "8px" }}>&#8594;</span>
              </Button>
            ) : (
              <Button className="primary-btn rounded-md" onClick={handleSubmitAssessment} disabled={submitting}>
                <div className="d-flex align-items-center justify-content-center">
                  {submitting && <Loader />}
                  <span className={submitting ? "ms-2" : ""}>{submitting ? t("submitting") : t("submit_assessment")}</span>
                </div>
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CandidateAssessment;
