import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { QUESTION_TYPES, QuestionType } from "@/constants/commonConstants";
import { IGetInterviewSkillQuestionsResponse, IInterviewQuestionResponse, IInterviewStaticInformation } from "@/interfaces/interviewInterfaces";

export interface IQuestionAnswer {
  questionId: number;
  answer: string;
}

export interface IUpdateQuestionAnswerPayload {
  interviewId: number;
  questionType: QuestionType;
  questionAnswers: IQuestionAnswer[];
  stratumScore: number;
  category?: string;
  interviewerName?: string;
}

export interface IInterviewState {
  interviews: {
    [interviewId: number]: Omit<IGetInterviewSkillQuestionsResponse, "interviewStaticInformation">;
  };
  interviewStaticInformation: IInterviewStaticInformation;
}

const initialState: IInterviewState = {
  interviews: {},
  interviewStaticInformation: {
    oneToOneInterviewInstructions: [],
    videoCallInterviewInstructions: [],
    stratumDescription: {},
  },
};

export const interviewSlice = createSlice({
  name: "interview",
  initialState,
  reducers: {
    setInterviewQuestions: (state, action: PayloadAction<{ interviewId: number } & IGetInterviewSkillQuestionsResponse>) => {
      const { interviewId, ...interviewData } = action.payload;

      if (!state.interviews[interviewId]) {
        state.interviews[interviewId] = {
          roleSpecificQuestions: {},
          cultureSpecificQuestions: {},
          careerBasedQuestions: {
            questions: [],
            score: 0,
          },
        };
      }

      // Handle role-specific questions
      if (interviewData.roleSpecificQuestions !== undefined) {
        state.interviews[interviewId].roleSpecificQuestions = interviewData.roleSpecificQuestions;
      }

      // Handle culture-specific questions
      if (interviewData.cultureSpecificQuestions !== undefined) {
        state.interviews[interviewId].cultureSpecificQuestions = interviewData.cultureSpecificQuestions;
      }

      // Handle career-based questions
      if (interviewData.careerBasedQuestions !== undefined) {
        state.interviews[interviewId].careerBasedQuestions = interviewData.careerBasedQuestions;
      }
    },

    setInterviewStaticInformation: (state, action: PayloadAction<IInterviewStaticInformation>) => {
      state.interviewStaticInformation = action.payload;
    },

    updateQuestionAnswer: (state, action: PayloadAction<IUpdateQuestionAnswerPayload>) => {
      const { interviewId, questionType, category, questionAnswers, stratumScore, interviewerName } = action.payload;

      // Initialize interview data if it doesn't exist
      if (!state.interviews[interviewId]) {
        state.interviews[interviewId] = {
          roleSpecificQuestions: {},
          cultureSpecificQuestions: {},
          careerBasedQuestions: {
            questions: [],
            score: 0,
            isLocked: false,
          },
        };
      }

      const interview = state.interviews[interviewId];
      const answerMap = new Map(questionAnswers.map((qa) => [qa.questionId, qa.answer]));

      switch (questionType) {
        case QUESTION_TYPES.CAREER_BASED:
          // Update answers
          interview.careerBasedQuestions.questions = interview.careerBasedQuestions.questions.map((question) => {
            const answer = answerMap.get(question.id);
            if (answer !== undefined) {
              return { ...question, answer };
            }
            return question;
          });

          // Update score
          interview.careerBasedQuestions.score = stratumScore;
          break;

        case QUESTION_TYPES.ROLE_SPECIFIC:
          if (category) {
            // Initialize category if it doesn't exist
            if (!interview.roleSpecificQuestions[category]) {
              interview.roleSpecificQuestions[category] = {
                questions: [],
                score: 0,
              };
            }

            // Update answers
            interview.roleSpecificQuestions[category].questions = interview.roleSpecificQuestions[category].questions.map((question) => {
              const answer = answerMap.get(question.id);
              if (answer !== undefined) {
                return { ...question, answer };
              }
              return question;
            });

            // Update score and interviewer name
            interview.roleSpecificQuestions[category].score = stratumScore;
            interview.roleSpecificQuestions[category].interviewerName = interviewerName;
          }
          break;

        case QUESTION_TYPES.CULTURE_SPECIFIC:
          if (category) {
            // Initialize category if it doesn't exist
            if (!interview.cultureSpecificQuestions[category]) {
              interview.cultureSpecificQuestions[category] = {
                questions: [],
                score: 0,
              };
            }

            // Update answers
            interview.cultureSpecificQuestions[category].questions = interview.cultureSpecificQuestions[category].questions.map((question) => {
              const answer = answerMap.get(question.id);
              if (answer !== undefined) {
                return { ...question, answer };
              }
              return question;
            });

            // Update score and interviewer name
            interview.cultureSpecificQuestions[category].score = stratumScore;
            interview.cultureSpecificQuestions[category].interviewerName = interviewerName;
          }
          break;
      }
    },

    addFollowupQuestion: (state, action: PayloadAction<{ interviewId: number } & IInterviewQuestionResponse>) => {
      const { interviewId, skillId, jobSkillId, id, jobApplicationId, questionType, question, answer, skillTitle, createdTs, updatedTs } =
        action.payload;

      switch (questionType) {
        case QUESTION_TYPES.CAREER_BASED:
          // add new question
          state.interviews[interviewId].careerBasedQuestions.questions.push({
            id,
            jobApplicationId,
            questionType,
            question,
            answer,
            skillTitle,
            jobSkillId: 0,
            skillId: 0,
            interviewId,
            createdTs,
            updatedTs,
          });
          break;
        case QUESTION_TYPES.ROLE_SPECIFIC:
          // add new question
          state.interviews[interviewId].roleSpecificQuestions[skillTitle].questions.push({
            id,
            jobApplicationId,
            questionType,
            question,
            answer,
            skillTitle,
            jobSkillId,
            skillId,
            createdTs,
            updatedTs,
            interviewId,
          });
          break;
        case QUESTION_TYPES.CULTURE_SPECIFIC:
          // add new question
          state.interviews[interviewId].cultureSpecificQuestions[skillTitle].questions.push({
            id,
            jobApplicationId,
            questionType,
            question,
            answer,
            skillTitle,
            jobSkillId,
            skillId,
            interviewId,
            createdTs,
            updatedTs,
          });
          break;
      }
    },

    clearInterview: (state, action: PayloadAction<{ interviewId: number }>) => {
      const { interviewId } = action.payload;
      if (state.interviews[interviewId]) {
        delete state.interviews[interviewId];
      } else {
        state.interviews = {};
      }
    },
  },
});

export const { setInterviewQuestions, updateQuestionAnswer, clearInterview, setInterviewStaticInformation, addFollowupQuestion } =
  interviewSlice.actions;

export default interviewSlice.reducer;
