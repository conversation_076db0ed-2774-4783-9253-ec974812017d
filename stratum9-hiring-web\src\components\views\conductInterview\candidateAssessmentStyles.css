.radio-input.form-check-input {
  border: 1px solid #757575;
  background-color: #fff;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  vertical-align: middle;
  position: relative;
}

.radio-input.form-check-input:checked {
  border: 2px solid #f0a500; /* Golden border */
  background-color: #fff;
}

.radio-input.form-check-input:checked::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #f0a500; /* Golden fill */
}

.form-check-input:focus {
  border-color: #757575;
  box-shadow: none;
  outline: none;
}

/* Remove any styling on the answer-strap when selected */
.answer-strap.selected-answer {
  border: 1px solid transparent;
  background-color: transparent;
}
