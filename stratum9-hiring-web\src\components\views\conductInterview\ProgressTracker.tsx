import React from "react";
import { useTranslations } from "next-intl";
import <PERSON><PERSON> from "lottie-react";

import { formatTime } from "@/utils/helper";
import { QUESTION_TYPES } from "@/constants/commonConstants";
import recordingProsses from "../../../../public/assets/images/recording-prosses.json";

const ProgressTracker: React.FC<{
  isRecording: boolean;
  elapsedTime: number;
  currentSkill: string;
  switchMainSkill: (skill: string) => void;
  onRecordingChange?: () => void;
}> = ({ isRecording, elapsedTime, currentSkill, switchMainSkill, onRecordingChange }) => {
  // Initialize progress state
  const t = useTranslations();

  console.log("current==skill", currentSkill);

  // Calculate overall progress (average of the three sections)
  let overallProgress = 0;
  switch (currentSkill) {
    case QUESTION_TYPES.CAREER_BASED:
      overallProgress = 33.33;
      break;
    case QUESTION_TYPES.ROLE_SPECIFIC:
      overallProgress = 66.66;
      break;
    default:
      overallProgress = 100;
      break;
  }

  return (
    <div className="progress-container">
      <div className="d-flex justify-content-between align-items-center">
        <p className="time">{formatTime(elapsedTime)}</p>

        <p onClick={onRecordingChange} className="status">
          {" "}
          {isRecording ? (
            <Lottie animationData={recordingProsses} loop={true} className="status-lottie" />
          ) : (
            <>
              <span className="record-icon" /> {t("record_interview")}{" "}
            </>
          )}{" "}
        </p>
      </div>
      <div className="progress-tracker">
        <div className="bar-container">
          <div className="bar">
            <div className="progress" style={{ width: `${overallProgress}%` }} />
            <div className="marker active" style={{ left: "0%", opacity: 0 }} />
            <div className="marker active" style={{ left: "33.3333%" }} />
            <div className="marker active" style={{ left: "66.6667%" }} />
            <div className="marker" style={{ left: "100%", opacity: 0 }} />
          </div>
          <div className="labels">
            <div className="label cursor-pointer" style={{ left: "0%" }} onClick={() => switchMainSkill(QUESTION_TYPES.CAREER_BASED)}>
              {t("career_based") + t("interview_")}
            </div>
            <div className="label cursor-pointer" style={{ left: "33.3333%" }} onClick={() => switchMainSkill(QUESTION_TYPES.ROLE_SPECIFIC)}>
              {t("role_based") + t("interview_")}
            </div>
            <div className="label cursor-pointer" style={{ left: "66.6667%" }} onClick={() => switchMainSkill(QUESTION_TYPES.CULTURE_SPECIFIC)}>
              {t("culture_based") + t("interview_")}
            </div>
            <div className="label cursor-pointer" style={{ left: "100%" }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
