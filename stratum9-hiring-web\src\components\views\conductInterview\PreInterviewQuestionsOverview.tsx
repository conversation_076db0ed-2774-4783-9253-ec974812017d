"use client";
import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslations } from "next-intl";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import { useDispatch, useSelector } from "react-redux";
import { useHasPermission } from "@/utils/permission";
import { useRouter, useSearchParams } from "next/navigation";

import Button from "@/components/formElements/Button";
import DownloadResumeIcon from "@/components/svgComponents/DownloadResumeIcon";
import { IAddInterviewSkillQuestion, IUpdateInterviewSkillQuestion } from "@/interfaces/interviewInterfaces";
import {
  addInterviewSkillQuestion,
  conductInterviewStaticInformation,
  getInterviewSkillQuestions,
  updateInterviewSkillQuestion,
} from "@/services/interviewServices";
import { decryptInfo, encryptInfo, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import AddUpdateQuestionModal from "@/components/commonModals/AddUpdateQuestionModal";
import { PerformanceCardSkeleton } from "./skeletons/PerformanceCardSkeleton";
import ConductingInterviewsModal from "@/components/commonModals/ConductingInterviewsModal";
import { IInterviewState, setInterviewQuestions, setInterviewStaticInformation } from "@/redux/slices/interviewSlice";
import ROUTES from "@/constants/routes";
import styles from "../../../styles/accessManagement.module.scss";
import { IParsedInfo } from "@/interfaces/commonInterfaces";
import { INTERVIEW_SCHEDULE_ROUND_TYPE, PERMISSION } from "@/constants/commonConstants";
import { AuthState } from "@/redux/slices/authSlice";
import NoPreInterviewQuestions from "@/components/svgComponents/NoPreInterviewQuestions";
import EditSkillIcon from "@/components/svgComponents/EditSkillIcon";
import AiIcon from "@/components/svgComponents/AiIcon";

export const ModalMode = {
  ADD: "add",
  UPDATE: "update",
};

export const SkillType = {
  ROLE_SPECIFIC: "role_specific",
  CULTURE_SPECIFIC: "culture_specific",
  CAREER_BASED: "career_based",
};

const PreInterviewQuestionsOverview = () => {
  const router = useRouter();
  const interviewQuestionsData = useSelector((state: { interview: IInterviewState }) => state.interview);
  const interviewStaticInformation = interviewQuestionsData.interviewStaticInformation;
  const authData = useSelector((state: { auth: AuthState }) => state.auth);

  const t = useTranslations();

  const dispatch = useDispatch();
  const params = useSearchParams();

  const info = params?.get("info");

  const initialFetchDone = useRef(false);

  const [parsedInfo, setParsedInfo] = useState<IParsedInfo | null>(null);

  console.log("info========>>>>>>>>>>>>>>PreInterviewQuestionsOverview", info);
  console.log("parsedInfo========>>>>>>>>>>>>>>PreInterviewQuestionsOverview", parsedInfo);

  useEffect(() => {
    toast.dismiss();
    try {
      if (info) {
        const decryptedInfo = decryptInfo(info);
        if (decryptedInfo) {
          const parsed = JSON.parse(decryptedInfo);
          setParsedInfo(parsed);
        } else {
          moveToCalendar();
        }
      }
    } catch (error) {
      console.log("error", error);
      moveToCalendar();
    }
  }, [info]);

  const moveToCalendar = () => {
    toastMessageError(t("invalid_or_malformed_url_parameters"));
    router.replace(ROUTES.INTERVIEW.CALENDAR);
  };

  console.log("parsedInfo", parsedInfo);

  // Only proceed with the rest of the component if parsedInfo is available
  const interviewId = parsedInfo?.interviewId;
  const jobApplicationId = parsedInfo?.jobApplicationId;
  const isEnded = parsedInfo?.isEnded;
  const interviewType = parsedInfo?.interviewType || "";
  const resumeLink = parsedInfo?.resumeLink;
  const interviewDate = parsedInfo?.date;
  const jobId = parsedInfo?.jobId;
  const channelName = parsedInfo?.channelName;
  const candidateId = parsedInfo?.candidateId;
  const candidateName = parsedInfo?.candidateName;
  const interviewerName = parsedInfo?.interviewerName;
  const interviewerId = parsedInfo?.interviewerId;

  const isAuthorized = useMemo(() => {
    if (authData?.authData?.id === interviewerId) return true;
    return false;
  }, [authData?.authData?.id, interviewerId]);

  console.log("isAuthorized", isAuthorized);

  console.log(interviewId, jobApplicationId);

  const careerBasedQuestions = useMemo(
    () => (interviewId && interviewQuestionsData?.interviews?.[interviewId]?.careerBasedQuestions) || { questions: [], score: 0 },
    [interviewQuestionsData, interviewId]
  );
  const roleSpecificQuestions = useMemo(
    () => (interviewId && interviewQuestionsData?.interviews?.[interviewId]?.roleSpecificQuestions) || {},
    [interviewQuestionsData, interviewId]
  );
  const cultureSpecificQuestions = useMemo(
    () => (interviewId && interviewQuestionsData?.interviews?.[interviewId]?.cultureSpecificQuestions) || {},
    [interviewQuestionsData, interviewId]
  );

  const [showAddUpdateQuestionModal, setShowAddUpdateQuestionModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [showConductingInterviewsModal, setShowConductingInterviewsModal] = useState(false);

  const [cultureSpecificSkillTitle, setCultureSpecificSkillTitle] = useState<string>("");
  const [roleSpecificSkillTitle, setRoleSpecificSkillTitle] = useState<string>("");

  const hasManagePreInterviewQuestionsPermission = useHasPermission(PERMISSION.MANAGE_PRE_INTERVIEW_QUESTIONS);

  const [currentQuestion, setCurrentQuestion] = useState<IAddInterviewSkillQuestion | IUpdateInterviewSkillQuestion | null>(null);
  const [modalMode, setModalMode] = useState<(typeof ModalMode)[keyof typeof ModalMode]>(ModalMode.ADD);

  const roleSkills = useMemo(() => Object.keys(roleSpecificQuestions), [roleSpecificQuestions]);
  const cultureSkills = useMemo(() => Object.keys(cultureSpecificQuestions), [cultureSpecificQuestions]);

  const isDataAlreadyExist = useMemo(() => {
    return roleSkills.length > 0 && cultureSkills.length > 0;
  }, [roleSkills.length, cultureSkills.length]);

  const isExpired = useMemo(() => {
    if (!interviewDate) return false;
    const currentDate = new Date();
    const date = new Date(interviewDate);
    return date < currentDate;
  }, [interviewDate]);

  console.log("isExpired", isExpired);

  console.log("loading=======", loading);

  console.log("isDataAlreadyExist=======", isDataAlreadyExist);

  console.log("roleSkills=======", roleSkills);

  console.log("cultureSkills=======", cultureSkills);

  // Set default skill titles when Redux data is available
  useEffect(() => {
    if (isDataAlreadyExist) {
      // Only set if not already set
      if (!roleSpecificSkillTitle) {
        const roleSpecificSkill = roleSkills[0];
        setRoleSpecificSkillTitle(roleSpecificSkill);
      }

      if (!cultureSpecificSkillTitle) {
        const cultureSpecificSkill = cultureSkills[0];
        setCultureSpecificSkillTitle(cultureSpecificSkill);
      }
    }
  }, [cultureSkills, cultureSpecificSkillTitle, isDataAlreadyExist, roleSkills, roleSpecificSkillTitle]);

  console.log("initialFetchDone======", initialFetchDone.current);

  useEffect(() => {
    if (!isDataAlreadyExist && !initialFetchDone.current && parsedInfo) {
      getInterviewQuestions(true);
      if (
        !interviewStaticInformation ||
        !interviewStaticInformation.oneToOneInterviewInstructions.length ||
        !interviewStaticInformation.videoCallInterviewInstructions.length ||
        !interviewStaticInformation.stratumDescription
      ) {
        getInterviewStaticInformation();
      }
      initialFetchDone.current = true;
    }
  }, [isDataAlreadyExist, parsedInfo]);

  const onHandleContinue = (type: string) => {
    const info = encryptInfo(
      JSON.stringify({
        interviewId,
        jobApplicationId,
        interviewType,
        resumeLink,
        isEnded,
        jobId,
        channelName,
        candidateId,
        candidateName,
        interviewerName,
        isAuthorized,
      })
    );

    const encoded = encodeURIComponent(info);

    router.push(
      `${type === INTERVIEW_SCHEDULE_ROUND_TYPE[0].value ? ROUTES.INTERVIEW.INTERVIEW_QUESTION : ROUTES.INTERVIEW.INTERVIEW}?info=${encoded}`
    );
  };

  const getInterviewStaticInformation = async () => {
    try {
      const response = await conductInterviewStaticInformation();

      if (response?.data?.success) {
        dispatch(setInterviewStaticInformation(response?.data?.data));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
    }
  };

  const getInterviewQuestions = async (isInitialFetch = false) => {
    try {
      setLoader(true);
      if (!interviewId || !jobApplicationId) return;
      const response = await getInterviewSkillQuestions({ jobApplicationId, interviewId });

      if (response?.data?.success) {
        const cultureSpecificSkill = Object.keys(response?.data?.data?.cultureSpecificQuestions);
        const roleSpecificSkill = Object.keys(response?.data?.data?.roleSpecificQuestions);

        if (isInitialFetch) {
          setCultureSpecificSkillTitle(cultureSpecificSkill[0]);
          setRoleSpecificSkillTitle(roleSpecificSkill[0]);
        }

        const careerBasedQuestions = response?.data?.data?.careerBasedQuestions;
        const cultureSpecificQuestions = response?.data?.data?.cultureSpecificQuestions;
        const roleSpecificQuestions = response?.data?.data?.roleSpecificQuestions;

        dispatch(setInterviewQuestions({ careerBasedQuestions, cultureSpecificQuestions, roleSpecificQuestions, interviewId }));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setTimeout(() => {
        setLoader(false);
      }, 1000); // Simulate a delay for better UX
    }
  };

  // Download questions as a text file
  const downloadQuestions = () => {
    // Create a JSON object with all question types
    const questionsData = {
      careerBasedQuestions,
      roleSpecificQuestions,
      cultureSpecificQuestions,
    };

    // Convert to JSON string with pretty formatting
    const jsonString = JSON.stringify(questionsData, null, 2);

    // Create a Blob with JSON content
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.href = url;
    link.download = `interview-questions-${interviewId}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toastMessageSuccess(t("questions_downloaded_successfully"));
  };

  const addNewInterviewQuestion = async (data: IAddInterviewSkillQuestion) => {
    try {
      setLoading(true);
      const response = await addInterviewSkillQuestion(data);

      if (response?.data?.success) {
        toastMessageSuccess(t(response?.data?.message));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const updateInterviewQuestion = async (data: IUpdateInterviewSkillQuestion) => {
    try {
      setLoading(true);
      const response = await updateInterviewSkillQuestion(data);

      if (response?.data?.success) {
        toastMessageSuccess(t(response?.data?.message));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleAddQuestion = (skillType: string, jobSkillId?: number) => {
    // Set up data for creating a new question
    if (!parsedInfo) return;
    const { jobApplicationId, interviewId } = parsedInfo;
    const newQuestionData: IAddInterviewSkillQuestion = {
      jobApplicationId,
      interviewId,
      question: "",
      skillType,
      jobSkillId,
    };

    setCurrentQuestion(newQuestionData);
    setModalMode(ModalMode.ADD);
    setShowAddUpdateQuestionModal(true);
  };

  const handleEditQuestion = (question: { id: number; question: string }) => {
    if (!parsedInfo) return;
    const { interviewId } = parsedInfo;
    // Set up data for editing an existing question
    const questionData: IUpdateInterviewSkillQuestion = {
      interviewQuestionId: question.id,
      question: question.question,
      interviewId,
    };

    setCurrentQuestion(questionData);
    setModalMode(ModalMode.UPDATE);
    setShowAddUpdateQuestionModal(true);
  };

  const handleQuestionSubmit = async (data: IAddInterviewSkillQuestion | IUpdateInterviewSkillQuestion) => {
    if (modalMode === ModalMode.UPDATE) {
      // Update existing question
      await updateInterviewQuestion(data as IUpdateInterviewSkillQuestion);
    } else {
      // Add new question
      await addNewInterviewQuestion(data as IAddInterviewSkillQuestion);
    }

    // Close modal and refresh questions
    setShowAddUpdateQuestionModal(false);
    await getInterviewQuestions();
  };

  console.log("roleSkills.length, cultureSkills.length======", roleSkills.length, cultureSkills.length);
  console.log("!roleSkills.length || !cultureSkills.length======", !roleSkills.length || !cultureSkills.length);

  return (
    <>
      <section className={styles.conduct_interview}>
        <div className="container">
          <div className="row">
            <div className="col-md-12">
              <div className="common-page-header">
                <div className="common-page-head-section">
                  <div className="main-heading">
                    <h2>
                      {t("pre_interview")} <span>{t("questions_overview")}</span>
                    </h2>
                    <Button className="clear-btn text-btn primary p-0 m-0  " onClick={downloadQuestions}>
                      <DownloadResumeIcon className="me-2" />
                      {t("download_questions")}
                    </Button>
                  </div>
                  <p className="description">{t("pre_interview_description")}</p>
                </div>
              </div>

              {roleSkills.length > 0 || cultureSkills.length > 0 || loader ? (
                <>
                  <div className="inner-section">
                    {/* Career-Based Skills */}
                    <div className="section-heading">
                      <h2>
                        <AiIcon />
                        {t("for")} <span>{t("career_based_skills")}</span>
                      </h2>
                    </div>
                    <div className="row g-4">
                      {loader && !isDataAlreadyExist ? (
                        <PerformanceCardSkeleton count={4} />
                      ) : (
                        careerBasedQuestions?.questions?.map((question, index) => (
                          <div className="col-md-4" key={question.id}>
                            <div className="overview-skill-card" data-text={index < 9 ? `0${index + 1}` : index + 1}>
                              <p className="overview-txt">{question.question}</p>
                              {!isEnded && !isExpired && hasManagePreInterviewQuestionsPermission ? (
                                <Button className="clear-btn p-0 m-0" onClick={() => handleEditQuestion(question)}>
                                  <EditSkillIcon /> {t("edit_question")}
                                </Button>
                              ) : null}
                            </div>
                          </div>
                        ))
                      )}

                      {!isEnded && !isExpired && careerBasedQuestions?.questions?.length < 8 && hasManagePreInterviewQuestionsPermission ? (
                        <div className="col-md-12">
                          <Button
                            className="clear-btn text-btn secondary p-0 m-0"
                            onClick={() => handleAddQuestion(SkillType.CAREER_BASED, careerBasedQuestions?.questions?.[0]?.jobSkillId)}
                          >
                            {" "}
                            {t("add_new_question")}
                          </Button>
                        </div>
                      ) : null}
                    </div>
                  </div>

                  {/* section saparater start*/}
                  <div className="dotted-border my-5" />
                  {/* section saparater end*/}

                  <div className="section-heading">
                    <h2>
                      <AiIcon />
                      {t("for")} <span>{t("role_specific_performance_based_skills")}</span>
                    </h2>
                  </div>
                  <div className="row g-4">
                    {/* skeletons for role specific skills */}
                    {loader && !isDataAlreadyExist ? (
                      <>
                        <ul className="role-list px-3">
                          <Skeleton height={70} width={145} borderRadius={16} />
                          <Skeleton height={70} width={174} borderRadius={16} />
                          <Skeleton height={70} width={168} borderRadius={16} />
                          <Skeleton height={70} width={147} borderRadius={16} />
                          <Skeleton height={70} width={171} borderRadius={16} />
                          <Skeleton height={70} width={144} borderRadius={16} />
                          <Skeleton height={70} width={128} borderRadius={16} />
                          <Skeleton height={70} width={150} borderRadius={16} />
                          <Skeleton height={70} width={86} borderRadius={16} />
                          <Skeleton height={70} width={166} borderRadius={16} />
                        </ul>
                        <PerformanceCardSkeleton count={3} />
                      </>
                    ) : (
                      <>
                        <div className="col-md-12">
                          <ul className="role-list secondary mb-4" aria-label="Job roles list">
                            {Object.keys(roleSpecificQuestions).map((skill, index) => (
                              <li
                                key={skill}
                                className={`role-item ${roleSpecificSkillTitle === skill ? "active" : ""}`}
                                tabIndex={index}
                                onClick={() => setRoleSpecificSkillTitle(skill)}
                              >
                                {skill}
                              </li>
                            ))}
                          </ul>
                        </div>
                        {roleSpecificQuestions[roleSpecificSkillTitle]?.questions?.map((question, index) => (
                          <div className="col-md-4" key={question.id}>
                            <div className="overview-skill-card" data-text={index < 9 ? `0${index + 1}` : index + 1}>
                              <p className="overview-txt">{question.question}</p>
                              {!isEnded && !isExpired && hasManagePreInterviewQuestionsPermission ? (
                                <Button className="clear-btn p-0 m-0" onClick={() => handleEditQuestion(question)}>
                                  <EditSkillIcon /> {t("edit_question")}
                                </Button>
                              ) : null}
                            </div>
                          </div>
                        ))}
                      </>
                    )}
                    {!isEnded &&
                    !isExpired &&
                    roleSpecificQuestions[roleSpecificSkillTitle]?.questions?.length < 6 &&
                    hasManagePreInterviewQuestionsPermission ? (
                      <div className="col-md-12">
                        <Button
                          className="clear-btn text-btn secondary p-0 m-0"
                          onClick={() => {
                            handleAddQuestion(SkillType.ROLE_SPECIFIC, roleSpecificQuestions?.[roleSpecificSkillTitle]?.questions?.[0]?.jobSkillId);
                          }}
                        >
                          {" "}
                          {t("add_new_question")}
                        </Button>
                      </div>
                    ) : null}
                  </div>

                  {/* section saparater start*/}
                  <div className="dotted-border my-5" />
                  {/* section saparater end*/}

                  <div className="section-heading">
                    <h2>
                      <AiIcon />
                      {t("for")} <span>{t("culture_specific_performance_based_skills")}</span>
                    </h2>
                  </div>
                  <div className="row g-4">
                    {/* skeletons for role specific skills */}
                    {loader && !isDataAlreadyExist ? (
                      <>
                        <ul className="role-list px-3">
                          <Skeleton height={70} width={168} borderRadius={16} />
                          <Skeleton height={70} width={114} borderRadius={16} />
                          <Skeleton height={70} width={180} borderRadius={16} />
                          <Skeleton height={70} width={169} borderRadius={16} />
                          <Skeleton height={70} width={132} borderRadius={16} />
                        </ul>
                        <PerformanceCardSkeleton count={3} />
                      </>
                    ) : (
                      <>
                        <div className="col-md-12">
                          <ul className="role-list secondary mb-4" aria-label="Job roles list">
                            {Object.keys(cultureSpecificQuestions).map((skill, index) => (
                              <li
                                key={skill}
                                className={`role-item ${cultureSpecificSkillTitle === skill ? "active" : ""}`}
                                tabIndex={index}
                                onClick={() => setCultureSpecificSkillTitle(skill)}
                              >
                                {skill}
                              </li>
                            ))}
                          </ul>
                        </div>
                        {cultureSpecificQuestions[cultureSpecificSkillTitle]?.questions?.map((question, index) => (
                          <div className="col-md-4" key={question.id}>
                            <div className="overview-skill-card" data-text={index < 9 ? `0${index + 1}` : index + 1}>
                              <p className="overview-txt">{question.question}</p>
                              {!isEnded && !isExpired && hasManagePreInterviewQuestionsPermission ? (
                                <Button className="clear-btn p-0 m-0" onClick={() => handleEditQuestion(question)}>
                                  <EditSkillIcon /> {t("edit_question")}
                                </Button>
                              ) : null}
                            </div>
                          </div>
                        ))}
                      </>
                    )}

                    {!isEnded &&
                    !isExpired &&
                    cultureSpecificQuestions[cultureSpecificSkillTitle]?.questions?.length < 6 &&
                    hasManagePreInterviewQuestionsPermission ? (
                      <div className="col-md-12">
                        <Button
                          className="clear-btn text-btn secondary p-0 m-0"
                          onClick={() =>
                            handleAddQuestion(
                              SkillType.CULTURE_SPECIFIC,
                              cultureSpecificQuestions?.[cultureSpecificSkillTitle]?.questions?.[0]?.jobSkillId
                            )
                          }
                        >
                          {t("add_new_question")}
                        </Button>
                      </div>
                    ) : null}
                  </div>

                  <div className="button-align mt-5 pb-5">
                    {!isEnded && !isExpired && isAuthorized ? (
                      <Button onClick={() => setShowConductingInterviewsModal(true)} className="primary-btn rounded-md">
                        {t("proceed_to_interview")}
                      </Button>
                    ) : null}
                    <Button onClick={() => router.back()} className="dark-outline-btn rounded-md">
                      {t("cancel")}
                    </Button>
                  </div>
                </>
              ) : (
                <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "300px" }}>
                  <NoPreInterviewQuestions />
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {showAddUpdateQuestionModal && (
        <AddUpdateQuestionModal
          onClickCancel={() => setShowAddUpdateQuestionModal(false)}
          mode={modalMode}
          initialData={currentQuestion || undefined}
          onSubmit={handleQuestionSubmit}
          isLoading={loading}
        />
      )}
      {showConductingInterviewsModal && (
        <ConductingInterviewsModal
          onClickCancel={() => setShowConductingInterviewsModal(false)}
          interviewStaticInformation={interviewStaticInformation}
          type={interviewType}
          onClickContinue={() => {
            setShowConductingInterviewsModal(false);
            onHandleContinue(interviewType);
          }}
        />
      )}
    </>
  );
};

export default PreInterviewQuestionsOverview;
