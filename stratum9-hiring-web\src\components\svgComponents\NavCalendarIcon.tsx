const NavCalendarIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 27" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        style={{ width: "24px", height: "22px" }}
        d="M23.2117 11.406H1.1439C0.631332 11.406 0.215332 10.99 0.215332 10.4774C0.215332 9.96483 0.631332 9.54883 1.1439 9.54883H23.2117C23.7243 9.54883 24.1403 9.96483 24.1403 10.4774C24.1403 10.99 23.7243 11.406 23.2117 11.406Z"
        // fill="#333333"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.6835 16.24C17.171 16.24 16.75 15.824 16.75 15.3114C16.75 14.7988 17.1598 14.3828 17.6724 14.3828H17.6835C18.1961 14.3828 18.6121 14.7988 18.6121 15.3114C18.6121 15.824 18.1961 16.24 17.6835 16.24Z"
        // fill="#333333"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.1899 16.24C11.6773 16.24 11.2563 15.824 11.2563 15.3114C11.2563 14.7988 11.6662 14.3828 12.1787 14.3828H12.1899C12.7024 14.3828 13.1184 14.7988 13.1184 15.3114C13.1184 15.824 12.7024 16.24 12.1899 16.24Z"
        // fill="#333333"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.68427 16.24C6.1717 16.24 5.74951 15.824 5.74951 15.3114C5.74951 14.7988 6.16056 14.3828 6.67313 14.3828H6.68427C7.19685 14.3828 7.61285 14.7988 7.61285 15.3114C7.61285 15.824 7.19685 16.24 6.68427 16.24Z"
        // fill="#333333"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.6835 21.0525C17.171 21.0525 16.75 20.6365 16.75 20.1239C16.75 19.6113 17.1598 19.1953 17.6724 19.1953H17.6835C18.1961 19.1953 18.6121 19.6113 18.6121 20.1239C18.6121 20.6365 18.1961 21.0525 17.6835 21.0525Z"
        // fill="#333333"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.1899 21.0525C11.6773 21.0525 11.2563 20.6365 11.2563 20.1239C11.2563 19.6113 11.6662 19.1953 12.1787 19.1953H12.1899C12.7024 19.1953 13.1184 19.6113 13.1184 20.1239C13.1184 20.6365 12.7024 21.0525 12.1899 21.0525Z"
        // fill="#333333"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.68427 21.0525C6.1717 21.0525 5.74951 20.6365 5.74951 20.1239C5.74951 19.6113 6.16056 19.1953 6.67313 19.1953H6.68427C7.19685 19.1953 7.61285 19.6113 7.61285 20.1239C7.61285 20.6365 7.19685 21.0525 6.68427 21.0525Z"
        // fill="#333333"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.1786 6.31257C16.666 6.31257 16.25 5.89657 16.25 5.384V1.30943C16.25 0.796859 16.666 0.380859 17.1786 0.380859C17.6911 0.380859 18.1071 0.796859 18.1071 1.30943V5.384C18.1071 5.89657 17.6911 6.31257 17.1786 6.31257Z"
        // fill="#333333"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.17711 6.31257C6.66454 6.31257 6.24854 5.89657 6.24854 5.384V1.30943C6.24854 0.796859 6.66454 0.380859 7.17711 0.380859C7.68968 0.380859 8.10568 0.796859 8.10568 1.30943V5.384C8.10568 5.89657 7.68968 6.31257 7.17711 6.31257Z"
        // fill="#333333"
      />
      <mask id="mask0_15282_6458" style={{ maskType: "luminance" }} maskUnits="userSpaceOnUse" x="0" y="2" width="25" height="25">
        <path fillRule="evenodd" clipRule="evenodd" d="M0.101074 2.33594H24.2439V26.9999H0.101074V2.33594Z" fill="white" />
      </mask>
      <g>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.93671 4.19289C3.72633 4.19289 1.95833 5.90518 1.95833 9.01404V20.2176C1.95833 23.3945 3.72633 25.1427 6.93671 25.1427H17.4085C20.6189 25.1427 22.3869 23.4267 22.3869 20.3117V9.01404C22.3919 7.48499 21.9808 6.29642 21.1649 5.47928C20.3255 4.63737 19.0317 4.19289 17.4197 4.19289H6.93671ZM17.4084 27H6.9366C2.72088 27 0.101074 24.4013 0.101074 20.2177V9.01422C0.101074 4.89384 2.72088 2.33594 6.9366 2.33594H17.4196C19.5355 2.33594 21.2849 2.96984 22.4796 4.16708C23.6397 5.33213 24.2501 7.00727 24.2439 9.0167V20.3118C24.2439 24.4372 21.6241 27 17.4084 27Z"
          // fill="#333333"
        />
      </g>
    </svg>
  );
};

export default NavCalendarIcon;
