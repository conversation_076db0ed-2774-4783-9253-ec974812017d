// src/features/candidatesManagement/routes.ts
import express from "express";
import auth from "../../middleware/auth";
import { ROUTES } from "../../utils/constants";
import HandleErrors from "../../middleware/handleError";
import { queryValidation } from "../../middleware/validateSchema";
import getActivityLogsValidation from "./validation";
import { authorizedForViewAuditLogs } from "../../middleware/isAuthorized";
import getLogsController from "./controller";

const activityLogsRoute = express.Router();

activityLogsRoute.get(
  ROUTES.ACTIVITY_LOGS.GET_LOGS,
  auth,
  authorizedForViewAuditLogs,
  queryValidation(getActivityLogsValidation),
  HandleErrors(getLogsController)
);

export default activityLogsRoute;
