import Joi from "joi";

// Department API validations
export const addDepartmentValidation = Joi.object({
  name: Joi.string().required().trim(),
  organizationId: Joi.number().optional(),
});

export const updateDepartmentValidation = Joi.object({
  name: Joi.string().required().trim(),
}).min(1);

// Employee API validations
export const addEmployeeValidation = Joi.object({
  employees: Joi.array()
    .items(
      Joi.object({
        firstName: Joi.string().required().trim(),
        lastName: Joi.string().required().trim(),
        email: Joi.string().email().required(),
        departmentId: Joi.number().required().positive(),
        roleId: Joi.number().required().positive(),
      })
    )
    .required(),
});

export const updateEmployeeRoleValidation = Joi.object({
  roleId: Joi.number().required().positive(),
});

export const updateEmployeeInterviewOrderValidation = Joi.object({
  newInterviewOrder: Joi.number().required().positive().messages({
    "number.base": "interview_order_must_be_number",
    "number.positive": "interview_order_must_be_positive",
    "any.required": "interview_order_required",
  }),
  departmentId: Joi.number().positive().required().messages({
    "number.base": "department_id_must_be_number",
    "number.positive": "department_id_must_be_positive",
    "any.required": "department_id_required",
  }),
});

export const departmentIdParamValidation = Joi.object({
  departmentId: Joi.number().positive().required().messages({
    "number.base": "department_id_must_be_number",
    "number.positive": "department_id_must_be_positive",
    "any.required": "department_id_required",
  }),
});
export const getdepartmentEmployeeQueryValidation = Joi.object({
  departmentId: Joi.number().positive().required().messages({
    "number.base": "department_id_must_be_number",
    "number.positive": "department_id_must_be_positive",
    "any.required": "department_id_required",
  }),
  search: Joi.string().allow("").optional(),
  page: Joi.number().optional(),
  limit: Joi.number().default(10),
  offset: Joi.number().min(0).optional(),
}).xor("page", "offset");

export const getDepartmentQueryValidation = Joi.object({
  search: Joi.string().allow("").optional(),
  page: Joi.number().default(1),
  limit: Joi.number().default(10),
});

export const getEmployeeByOrgIdQueryValidation = Joi.object({
  orgId: Joi.number().required(),
});

export const employeeIdParamValidation = Joi.object({
  employeeId: Joi.number().positive().required().messages({
    "number.base": "employee_id_must_be_number",
    "number.positive": "employee_id_must_be_positive",
    "any.required": "employee_id_required",
  }),
});
export const updateEmployeeStatusValidation = Joi.object({
  status: Joi.boolean().required(),
}).min(1);
