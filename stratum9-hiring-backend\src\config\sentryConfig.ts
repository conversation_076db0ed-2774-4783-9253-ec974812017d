import * as Sentry from "@sentry/node";

const sentryInitialization = async () => {
  try {
    // const sentryKeys = await getSecretKeys();

    Sentry.init({
      //   dsn: sentryKeys.sentry_dsn_key,
      dsn: "https://<EMAIL>/4505594209042432",

      tracesSampleRate: 1.0,
    });
  } catch (error) {
    console.error("Error initializing Sentry:", error);

    // sentryErrorLogging("sentry_secrate_key_error", error);
  }
};

export const captureSentryError = (error: Error, context?: string) => {
  console.log(`Error: ${context}`, error);

  Sentry.withScope((scope) => {
    if (context) {
      scope.setTag("context", context);
    }
    Sentry.captureException(error);
  });
};
export default sentryInitialization;
