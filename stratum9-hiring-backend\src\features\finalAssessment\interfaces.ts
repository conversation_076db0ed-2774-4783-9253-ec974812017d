import { QuestionType } from "../../utils/constants";

/**
 * Interface for creating a final assessment
 */
export interface ICreateFinalAssessment {
  jobId: number;
  jobApplicationId: number;
  overallSuccessProbability: number;
  skillSummary: object;
  developmentRecommendations: object;
}

/**
 * Interface for updating a final assessment
 */
export interface IUpdateFinalAssessment {
  isAssessmentSubmitted?: boolean;
  isAssessmentShared?: boolean;
  overallSuccessProbability?: number;
  skillSummary?: object;
  developmentRecommendations?: object;
}

/**
 * Interface for creating assessment questions
 */
export interface ICreateFinalAssessmentQuestion {
  finalAssessmentId: number;
  skillId?: number;
  question: string;
  questionType: QuestionType;
  options?: object;
  correctAnswer?: string;
}

/**
 * Interface for updating assessment questions
 */
export interface IUpdateFinalAssessmentQuestion {
  applicantAnswer?: string;
  correctAnswer?: string;
}

/**
 * Interface for final assessment response
 */
export interface IFinalAssessmentResponse {
  id: number;
  jobId: number;
  jobApplicationId: number;
  isAssessmentSubmitted: boolean;
  isAssessmentShared: boolean;
  overallSuccessProbability: number;
  skillSummary: object;
  developmentRecommendations: object;
  createdTs: Date;
  updatedTs: Date;
}

/**
 * Interface for final assessment question response
 */
export interface IFinalAssessmentQuestionResponse {
  id: number;
  finalAssessmentId: number;
  skillId?: number;
  question: string;
  questionType: QuestionType;
  options?: object;
  correctAnswer?: string;
  applicantAnswer?: string;
  createdTs: Date;
  updatedTs: Date;
}
