/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import React, { useState, useEffect, useCallback } from "react";
import styles from "../../../styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import EditIcon from "@/components/svgComponents/EditIcon";
import DeleteIcon from "@/components/svgComponents/DeleteIcon";
import Image from "next/image";
import userRolesImg from "../../../../public/assets/images/user-role-img.png";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import { useForm } from "react-hook-form";
import UserRoleModal from "@/components/commonModals/UserRoleModal";
import EditPermissionsModal from "@/components/commonModals/EditPermissionsModal";
import { findRoleList, FindRoleResponse, getRolePermissions, UpdateRolePermissionsResponse } from "@/services/roleService";
import { IRolePermission } from "@/interfaces/roleInterface";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { useTranslations } from "next-intl";
import Skeleton from "react-loading-skeleton";
import { DEFAULT_LIMIT } from "@/constants/commonConstants";
import InfiniteScroll from "react-infinite-scroll-component";
import TableSkeleton from "../skeletons/TableSkeleton";
import { debounce } from "lodash";
import { PERMISSION } from "@/constants/commonConstants";
import { useHasPermission } from "@/utils/permission";

// Interface for role permission data
export const ROLE_ALTER_MODE = {
  ADD: "add",
  EDIT: "edit",
  DELETE: "delete",
};

export const VIEW_MODE = {
  ROLES: "roles",
  PERMISSIONS: "permissions",
};

const RolesPermissions = () => {
  const t = useTranslations();
  const { control } = useForm();

  // Permission checks
  const hasCreateNewRolePermission = useHasPermission(PERMISSION.CREATE_NEW_ROLE);
  const hasManageUserPermissionsPermission = useHasPermission(PERMISSION.MANAGE_USER_PERMISSIONS);

  // Determine default view based on permissions
  const getDefaultView = () => {
    if (hasCreateNewRolePermission && !hasManageUserPermissionsPermission) {
      return VIEW_MODE.ROLES;
    } else if (!hasCreateNewRolePermission && hasManageUserPermissionsPermission) {
      return VIEW_MODE.PERMISSIONS;
    } else {
      // Both permissions or no permissions - default to ROLES
      return VIEW_MODE.ROLES;
    }
  };
  const [roleModalConfig, setRoleModalConfig] = useState<{
    show: boolean;

    mode: (typeof ROLE_ALTER_MODE)[keyof typeof ROLE_ALTER_MODE];
    role: FindRoleResponse | null;
  }>({ show: false, mode: ROLE_ALTER_MODE.ADD, role: null });
  const [showEditPermissionsModal, setShowEditPermissionsModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<{ id: number; name: string } | null>(null);
  const [roles, setRoles] = useState<FindRoleResponse[]>([]);
  const [rolePermissions, setRolePermissions] = useState<IRolePermission[]>([]);
  const [isLoadingRoles, setIsLoadingRoles] = useState(true);
  const [isLoadingRolesPermissions, setIsLoadingRolesPermissions] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [hasMorePermission, setHasMorePermission] = useState(true);
  const [activeView, setActiveView] = useState<(typeof VIEW_MODE)[keyof typeof VIEW_MODE]>(getDefaultView());

  const [searchTerm, setSearchTerm] = useState<string>("");
  const [offsets, setOffsets] = useState<{ roles: number; permissions: number }>({ roles: 0, permissions: 0 });

  // Define fetch functions with useCallback to prevent unnecessary re-renders
  const fetchRoles = async (currentOffset = offsets.roles, reset = false) => {
    try {
      setIsLoadingRoles(true);
      const response = await findRoleList(currentOffset, DEFAULT_LIMIT);
      if (response?.data?.success && Array.isArray(response.data.data)) {
        const rolesFetched = response.data.data;
        setRoles((prevRoles) => (reset ? rolesFetched : [...prevRoles, ...rolesFetched]));

        if (rolesFetched.length < DEFAULT_LIMIT) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }

        setOffsets((prev) => ({ ...prev, roles: currentOffset + rolesFetched.length }));
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
      setHasMore(false);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  const fetchRolePermissions = async (currentOffset = offsets.permissions, search: string, reset = false) => {
    try {
      setIsLoadingRolesPermissions(true);
      const response = await getRolePermissions(currentOffset, DEFAULT_LIMIT, search);
      if (response.data?.success) {
        const rolePermissionsFetched = response.data.data;

        setRolePermissions((prevRoles) => (reset ? rolePermissionsFetched : [...prevRoles, ...rolePermissionsFetched]));

        if (rolePermissionsFetched.length < DEFAULT_LIMIT) {
          setHasMorePermission(false);
        } else {
          setHasMorePermission(true);
        }

        setOffsets((prev) => ({ ...prev, permissions: currentOffset + rolePermissionsFetched.length }));
      } else {
        toastMessageError(response?.data?.message ? response.data.message : t("failed_load_roles"));
      }
    } catch (error) {
      console.error(error);
      setHasMorePermission(false);
      toastMessageError(t("unexpected_error"));
    } finally {
      setIsLoadingRolesPermissions(false);
    }
  };

  useEffect(() => {
    setActiveView(getDefaultView);
  }, [hasCreateNewRolePermission, hasManageUserPermissionsPermission]);

  // Fetch data when component mounts or view changes
  useEffect(() => {
    if (activeView === VIEW_MODE.ROLES) {
      if (roles.length === 0) {
        fetchRoles(0, true);
      }
    } else {
      if (rolePermissions.length === 0) {
        fetchRolePermissions(0, "", true);
      }
    }
    // Do NOT reset offsets when switching views
  }, [activeView]);

  const openRoleModal = useCallback(
    (mode: (typeof ROLE_ALTER_MODE)[keyof typeof ROLE_ALTER_MODE], role: { id: number; name: string; isDefaultRole: boolean } | null = null) => {
      setRoleModalConfig({
        show: true,
        mode,
        role,
      });
    },
    []
  );

  const closeRoleModal = () => {
    setRoleModalConfig({
      show: false,
      mode: ROLE_ALTER_MODE.ADD,
      role: null,
    });
  };

  const handleRoleSuccess = (message?: string, responseData?: FindRoleResponse) => {
    if (!responseData) {
      return;
    }

    // Extract the role data from the response
    const roleData = responseData;

    // Update the roles state based on the operation type
    if (roleModalConfig.mode === ROLE_ALTER_MODE.ADD && roleData) {
      // Add the new role to the beginning of the list
      setRoles((prevRoles) => [
        {
          id: roleData.id,
          name: roleData.name,
          isDefaultRole: false,
        },
        ...prevRoles,
      ]);
    } else if (roleModalConfig.mode === ROLE_ALTER_MODE.EDIT && roleData) {
      // Update the existing role in the list
      setRoles((prevRoles) => prevRoles.map((role) => (role.id === roleData.id ? { ...role, name: roleData.name } : role)));
    } else if (roleModalConfig.mode === ROLE_ALTER_MODE.DELETE && roleData) {
      // Remove the deleted role from the list
      setRoles((prevRoles) => prevRoles.filter((role) => role.id !== roleData.id));
    }

    if (message) {
      toastMessageSuccess(message);
    }
  };

  const handleEditClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {
    openRoleModal(ROLE_ALTER_MODE.EDIT, role);
  };

  const handleDeleteClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {
    openRoleModal(ROLE_ALTER_MODE.DELETE, role);
  };

  const handlePermissionsClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {
    setSelectedRole(role);
    setShowEditPermissionsModal(true);
  };

  const handlePermissionsSuccess = (message?: string, updatedRole?: UpdateRolePermissionsResponse) => {
    if (updatedRole) {
      // Update the role in the rolePermissions array without making an API call
      setRolePermissions((prevRoles) =>
        prevRoles.map((role) =>
          role.id === updatedRole.id
            ? {
                ...role,
                name: updatedRole.name,
                permission_count: updatedRole.permission_count,
                updated_ts: updatedRole.updated_ts,
                isDefaultRole: !!updatedRole.isDefaultRole,
              }
            : role
        )
      );
    } else {
      // If no updated role data is provided, fetch from API as before
      if (activeView === VIEW_MODE.ROLES) {
        fetchRoles(offsets.roles);
      } else {
        fetchRolePermissions(offsets.permissions, searchTerm);
      }
    }

    if (message) {
      toastMessageSuccess(message);
    }
  };

  const handleSearchInputChange = (event: string) => {
    const searchString = event.trim();
    setSearchTerm(searchString);
    setRolePermissions([]); // Clear previous results on new search
    setHasMorePermission(true);
    setOffsets((prev) => ({ ...prev, permissions: 0 }));
    fetchRolePermissions(0, searchString, true);
  };

  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 1000);

  return (
    <>
      <section className={styles.access_management}>
        <div className="container">
          <div className="common-page-header pb-0">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  {t("access_management", { defaultValue: "Access" })} <span>{t("management", { defaultValue: "Management" })}</span>
                </h2>
              </div>
            </div>
          </div>
          <div className="row">
            <div className={activeView === VIEW_MODE.ROLES ? "col-md-7" : "col-md-12"}>
              <div className="common-page-head-section">
                <div className="main-heading">
                  <h2>
                    {t("access_management", { defaultValue: "Access" })} <span>{t("management", { defaultValue: "Management" })}</span>
                  </h2>
                </div>
              </div>
              <div className="button-align justify-content-between mt-5 mb-5">
                <div className="button-align">
                  {hasCreateNewRolePermission && (
                    <Button
                      className={`${activeView === VIEW_MODE.ROLES ? "primary-btn hover-none" : "dark-outline-btn"} rounded-md button-sm minWidth`}
                      onClick={() => setActiveView(VIEW_MODE.ROLES)}
                    >
                      {t("user_roles", { defaultValue: "User Roles" })}
                    </Button>
                  )}
                  {hasManageUserPermissionsPermission && (
                    <Button
                      className={`${activeView === VIEW_MODE.PERMISSIONS ? "primary-btn hover-none" : "dark-outline-btn"} rounded-md button-sm`}
                      onClick={() => setActiveView(VIEW_MODE.PERMISSIONS)}
                    >
                      {t("user_permissions", { defaultValue: "User Permissions" })}
                    </Button>
                  )}
                </div>

                {activeView === VIEW_MODE.PERMISSIONS && (
                  <div className={styles.role_right}>
                    <InputWrapper className="mb-0 w-100">
                      <div className="icon-align right">
                        <Textbox
                          className="form-control w-100"
                          control={control}
                          name="search"
                          type="text"
                          placeholder={t("search_user_role", { defaultValue: "Search using user role" })}
                          onChange={(e) => debouncedHandleSearchInputChange(e.target.value)}
                        >
                          <InputWrapper.Icon>
                            <SearchIcon />
                          </InputWrapper.Icon>
                        </Textbox>
                      </div>
                    </InputWrapper>
                  </div>
                )}
              </div>

              {activeView === VIEW_MODE.ROLES && hasCreateNewRolePermission ? (
                // Roles View
                <div className="common-card margin-add">
                  <div className="card-header">
                    <h3>{t("user_role")}</h3>
                    <Button className="dark-outline-btn rounded-md button-sm" onClick={() => openRoleModal(ROLE_ALTER_MODE.ADD)}>
                      {t("add_new_role")}
                    </Button>
                  </div>
                  <div className="card-body">
                    {
                      <InfiniteScroll
                        dataLength={roles.length}
                        next={() => fetchRoles(offsets.roles)}
                        hasMore={hasMore}
                        height={window.innerHeight - 500}
                        className="pe-4"
                        loader={
                          isLoadingRoles && (
                            <ul className={`mt-3 ${styles.user_roles}`}>
                              <Skeleton height={20} width="100%" count={10} borderRadius={4} style={{ margin: "10px 0" }} />
                            </ul>
                          )
                        }
                        endMessage={
                          !isLoadingRoles && roles.length ? (
                            <div className="text-center py-4">
                              <p>{t("no_more_roles_to_fetch")}</p>
                            </div>
                          ) : null
                        }
                      >
                        <ul className={styles.user_roles}>
                          {roles.length
                            ? [...roles]
                                .sort((a, b) => a.name.localeCompare(b.name))
                                .map((role) => (
                                  <li key={role.id} className={role.isDefaultRole ? styles.disabled_role : ""}>
                                    <p>{role.name}</p>
                                    <div className="button-align">
                                      <Button
                                        className={`clear-btn p-0 ${role.isDefaultRole ? "disabled" : ""}`}
                                        onClick={() => !role.isDefaultRole && handleEditClick(role)}
                                        disabled={role.isDefaultRole}
                                      >
                                        <EditIcon />
                                      </Button>
                                      <Button
                                        className={`clear-btn p-0 ${role.isDefaultRole ? "disabled" : ""}`}
                                        onClick={() => !role.isDefaultRole && handleDeleteClick(role)}
                                        disabled={role.isDefaultRole}
                                      >
                                        <DeleteIcon />
                                      </Button>
                                    </div>
                                  </li>
                                ))
                            : !isLoadingRoles && (
                                <p className="text-center p-5">
                                  <strong>{t("no_roles_found")}</strong>
                                </p>
                              )}
                        </ul>
                      </InfiniteScroll>
                    }
                  </div>
                </div>
              ) : activeView === VIEW_MODE.PERMISSIONS && hasManageUserPermissionsPermission ? (
                // Roles View End
                // Permissions View
                <div className="table-responsive mt-5">
                  <InfiniteScroll
                    dataLength={rolePermissions.length}
                    next={() => fetchRolePermissions(offsets.permissions, searchTerm)}
                    hasMore={hasMorePermission}
                    height={window.innerHeight - 300}
                    loader={
                      isLoadingRolesPermissions && (
                        <table className="table w-100">
                          <TableSkeleton rows={10} cols={4} colWidths="120,80,100,24,24" />
                        </table>
                      )
                    }
                    endMessage={
                      !isLoadingRolesPermissions && rolePermissions.length ? (
                        <table className="table w-100">
                          <tbody>
                            <tr>
                              <td colSpan={5} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                                {t("no_more_roles_to_fetch")}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      ) : null
                    }
                  >
                    <table className="table overflow-auto mb-0">
                      <thead>
                        <tr>
                          <th style={{ width: "25%" }}>{t("user_role")}</th>
                          <th style={{ width: "25%" }} className="text-center">
                            {t("permission_counts")}
                          </th>
                          <th style={{ width: "25%" }} className="text-center">
                            {t("last_modified")}
                          </th>
                          <th style={{ width: "25%" }} className="text-center">
                            {t("actions")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {rolePermissions.length
                          ? [...rolePermissions]
                              .sort((a, b) => a.name.localeCompare(b.name))
                              .map((role) => (
                                <tr key={role.id} className={role.isDefaultRole ? "text-muted opacity-75 disabled-row" : ""}>
                                  <td style={{ width: "25%" }}>{role.name}</td>
                                  <td style={{ width: "25%" }} className="text-center">
                                    {role.permission_count}
                                  </td>
                                  <td style={{ width: "25%" }} className="text-center">
                                    {new Date(role.updated_ts).toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "numeric" })}
                                  </td>
                                  <td style={{ width: "25%" }} className="text-center">
                                    <Button
                                      className={`clear-btn p-0 text-decoration-underline m-auto ${role.isDefaultRole ? "text-muted" : "color-primary"}`}
                                      onClick={() =>
                                        !role.isDefaultRole &&
                                        handlePermissionsClick({ id: role.id, name: role.name, isDefaultRole: role.isDefaultRole })
                                      }
                                      disabled={role.isDefaultRole}
                                      style={role.isDefaultRole ? { cursor: "not-allowed" } : {}}
                                    >
                                      {t("edit")}
                                    </Button>
                                  </td>
                                </tr>
                              ))
                          : !isLoadingRolesPermissions && (
                              <tr>
                                <td colSpan={5}>
                                  <p className="text-center">
                                    <strong>{t("no_roles_found")}</strong>
                                  </p>
                                </td>
                              </tr>
                            )}
                      </tbody>
                    </table>
                  </InfiniteScroll>
                </div>
              ) : // Permissions View End
              null}
            </div>

            {activeView === VIEW_MODE.ROLES && hasCreateNewRolePermission && (
              <div className="col-md-5">
                <Image src={userRolesImg} alt={t("user_roles_alt")} className={styles.user_roles_img} />
              </div>
            )}
          </div>
        </div>
      </section>

      {roleModalConfig.show && (
        <UserRoleModal
          onClickCancel={closeRoleModal}
          onSubmitSuccess={handleRoleSuccess}
          role={roleModalConfig.role}
          mode={roleModalConfig.mode}
          disabled={isLoadingRoles}
        />
      )}

      {showEditPermissionsModal && selectedRole && (
        <EditPermissionsModal
          onClickCancel={() => setShowEditPermissionsModal(false)}
          onSubmitSuccess={handlePermissionsSuccess}
          role={selectedRole}
          disabled={isLoadingRolesPermissions}
        />
      )}
    </>
  );
};

export default RolesPermissions;
