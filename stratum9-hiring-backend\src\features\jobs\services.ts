import * as Sentry from "@sentry/node";
import { Brackets, Like } from "typeorm";
import <PERSON><PERSON><PERSON> from "openai";
import puppeteer from "puppeteer";
import axios from "axios";
import fs from "fs";
import path from "path";
import os from "os";
import Cache from "../../db/cache";
import dbConnection from "../../db/dbConnection";
import { ResponseObject } from "../../interface/commonInterface";
import {
  API_RESPONSE_MSG,
  REDIS_EXPIRY,
  GPT_MODEL,
  SKILLS_CACHE_KEY,
  DEFAULT_LIMIT,
  BENEFIT_SLUGS,
  ACTIVITY_LOG_TYPE,
  ENTITY_TYPE,
} from "../../utils/constants";
import SubscriptionServices from "../subscriptions/services";
import {
  IExtractedSkills,
  IGptResponse,
  IJobGenerationRequest,
} from "./interface";
import { JobsModel } from "../../schema/s9-innerview/jobs";
import SkillsModel from "../../schema/s9-innerview/skills";
import JobSkillsModel from "../../schema/s9-innerview/job_skills";
import OrganizationModel from "../../schema/s9/organization";
import { gettingPreSignedUrl } from "../../utils/fileUpload";
import JobApplicationsModel, {
  Status,
} from "../../schema/s9-innerview/job_applications";
import {
  EXTRACT_FORM_FIELDS_FROM_PDF_PROMPT,
  JOB_DESCRIPTION_FORMATTER_PROMPT,
} from "../../prompts";
import NotificationServices from "../notification/services";
import { NotificationType } from "../../schema/s9-innerview/notifications";
import Employee from "../../schema/s9-innerview/employees";
import InterviewModel from "../../schema/s9-innerview/interview";
import { getSecretKeys } from "../../config/awsConfig";
import * as helper from "../../utils/helper";

/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
// Define SkillType enum locally to match the one in job_skills.ts
enum SkillType {
  ROLE_SPECIFIC = "role_specific",
  CULTURE_SPECIFIC = "culture_specific",
  CAREER_BASED = "career_based",
}

/**
 * Constants for OpenAI API and caching
 */
let OPENAI_API_KEY: string;

(async () => {
  const keys = await getSecretKeys();
  OPENAI_API_KEY = keys.openai_api_key;
})();

/**
 * Job Services class for handling job-related operations
 * Implements services for job skills generation and analysis
 */
export class JobServices {
  private static openAiClient: OpenAI | null = null;

  /**
   * Generate job skills from GPT using the job information
   * @param request Job generation request with job details and skills
   * @returns Response with extracted skills categorized by type
   */
  static generateJobSkills = async (
    request: IJobGenerationRequest
  ): Promise<ResponseObject> => {
    try {
      // Combine job details into a comprehensive job description
      const jobDescription = JobServices.buildJobDescription(request);

      // Initialize OpenAI client if not already initialized
      if (!JobServices.openAiClient) {
        try {
          if (!OPENAI_API_KEY) {
            Sentry.captureMessage(
              "OpenAI API key not configured in environment variables"
            );
            // Continue without client, will use fallback
          } else {
            JobServices.openAiClient = new OpenAI({ apiKey: OPENAI_API_KEY });
          }
        } catch (error) {
          Sentry.captureException(error);
          // Continue without client, will use fallback
        }
      }

      // Get skills data from database (with Redis caching)
      let dbSkills = [];
      try {
        const skillsResult = await JobServices.getSkillsData();
        // Check if result is an array (successful path) or an error response object
        if (Array.isArray(skillsResult)) {
          dbSkills = skillsResult;
        }
      } catch (dbError) {
        Sentry.captureException(dbError);
        // Continue with empty dbSkills, will use static skills list
      }

      // Prepare prompt for GPT with database skills or fallback to static skills
      const prompt = JobServices.prepareGptPrompt(jobDescription, dbSkills);

      try {
        // Call GPT API to get categorized skills
        const gptResponse = await JobServices.callGptApi(prompt);

        // Process the GPT response
        const extractedSkills = JobServices.simulateGptResponse(gptResponse);

        return {
          success: true,
          message: API_RESPONSE_MSG.skills_generated,
          data: extractedSkills,
        };
      } catch (apiError) {
        Sentry.captureException(apiError);

        // Use fallback if API call fails
        const fallbackSkills = JobServices.simulateGptResponse(null);

        return {
          success: true,
          message: API_RESPONSE_MSG.skills_generated,
          data: fallbackSkills,
        };
      }
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.skills_generation_failed,
        error: error.message,
      };
    }
  };

  // Add this to JobServices class in services.ts
  static getAllJobTitles = async (): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9DataSource();
      const jobRepo = dataSource.getRepository(JobsModel);

      const jobs = await jobRepo.find({
        select: ["id", "title", "createdTs"],
        order: { createdTs: "DESC" },
      });

      return {
        success: true,
        message: API_RESPONSE_MSG.job_fetch_success,
        data: jobs,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
        error: error.message,
      };
    }
  };

  /**
   * Retrieves dashboard statistics for the given organization and user.
   *
   * @param organizationId - The ID of the organization to filter jobs.
   * @param userId - The ID of the user (hiring manager) to filter jobs and applications.
   * @returns A promise resolving to an object containing total jobs, active jobs,
   * and applications on hold, along with a success message.
   */
  static getDashboardCounts = async (
    organizationId: number,
    userId: number
  ): Promise<
    ResponseObject & {
      totalJobs?: number;
      activeJobs?: number;
      onHoldApplications?: number;
      upcomingInterviews?: number;
      scheduledInterviews?: number;
    }
  > => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      const jobRepo = dataSource.getRepository(JobsModel);
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);
      const interviewRepo = dataSource.getRepository(InterviewModel);
      const currentTime = new Date();

      const [
        totalJobs,
        activeJobs,
        onHoldApplications,
        upcomingInterviews,
        scheduledInterviews,
      ] = await Promise.all([
        jobRepo.count({
          where: {
            orgId: organizationId,
            userId,
          },
        }),

        jobRepo.count({
          where: {
            orgId: organizationId,
            userId,
            isActive: true,
          },
        }),

        jobApplicationRepo
          .createQueryBuilder("application")
          .innerJoin("application.job", "job")
          .andWhere("application.status = :status", { status: Status.ON_HOLD })
          .andWhere("job.orgId = :orgId", { orgId: organizationId })
          .getCount(),

        interviewRepo
          .createQueryBuilder("interview")
          .innerJoin("interview.job", "job")
          .andWhere("job.orgId = :orgId", { orgId: organizationId })
          .andWhere("interview.startTime > :currentTime", { currentTime })
          .andWhere("interview.isEnded = :isEnded", { isEnded: false })
          .getCount(),

        interviewRepo
          .createQueryBuilder("interview")
          .innerJoin("interview.job", "job")
          .andWhere("job.orgId = :orgId", { orgId: organizationId })
          .getCount(),
      ]);

      return {
        success: true,
        message: API_RESPONSE_MSG.dashboard_counts_fetch_success,
        totalJobs,
        activeJobs,
        onHoldApplications,
        upcomingInterviews,
        scheduledInterviews,
      };
    } catch (err) {
      console.error("Error in getDashboardCounts:", err);
      return {
        success: false,
        message: API_RESPONSE_MSG.dashboard_counts_fetch_failed,
        error: err.message,
      };
    }
  };

  /**
   * Updates the active/inactive status of a job.
   *
   * @param jobId - The ID of the job to update.
   * @param status - A boolean indicating the new active state of the job.
   * @returns A promise resolving to an object with success status and message.
   */
  static updateJob = async (
    jobId: number,
    orgId: number,
    status: boolean
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobRepo = dataSource.getRepository(JobsModel);
      const employeeRepo = dataSource.getRepository(Employee);

      const job = await jobRepo.findOneBy({ id: jobId, orgId });

      if (!job) {
        return {
          success: false,
          message: API_RESPONSE_MSG.job_not_found,
        };
      }

      job.isActive = status;

      await jobRepo.save(job);
      const employee = await employeeRepo.findOneBy({
        organizationId: job.orgId,
        isAdmin: true,
      });
      NotificationServices.createNotification(orgId, employee.userId, {
        type: NotificationType.JOB_POST_ARCHIVED,
        title: NotificationType.JOB_POST_ARCHIVED,
        description: `Job post ${job?.title} has been archived. Can be restored`,
        relatedId: job.id,
      });
      return {
        success: true,
        message: `Job with id ${jobId} ${status ? "activated" : "deactivated"} successfully`,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed_to_update_job,
        error: error.message,
      };
    }
  };

  /**
   * Retrieves a paginated list of job metadata with optional filters.
   *
   * @param offset - The number of items to skip (for pagination).
   * @param searchStr - A search string to filter jobs by title.
   * @param isActive - Optional filter for active/inactive jobs.
   * @param limit - The maximum number of jobs to retrieve. Defaults to 10.
   * @returns A promise resolving to a list of formatted job metadata.
   */
  static getAllJobsMeta = async (
    orgId: number,
    offset: number,
    searchStr: string,
    isActive?: boolean,
    limit: number = DEFAULT_LIMIT
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobRepo = dataSource.getRepository(JobsModel);

      // ✅ Use QueryBuilder and JOIN with jobApplications
      const jobsQuery = jobRepo
        .createQueryBuilder("job")
        .leftJoin("job_applications", "jobApp", "job.id = jobApp.job_id") // 🔄 Join with applications
        .select([
          "job.id AS id",
          "job.job_id AS jobId",
          "job.title AS title",
          "job.createdTs as postedDate",
          "job.updatedTs as updatedDate",
          "job.isActive as isActive",
          "job.jobId as jobId",
          "job.finalJobDescriptionHtml AS finalJobDescriptionHtml",
          "COUNT(jobApp.id) AS applicationCount", // ✅ Count applications
        ])
        .orderBy("job.createdTs", "DESC")
        .groupBy(
          "job.id, job.job_id, job.title, job.createdTs, job.isActive, job.finalJobDescriptionHtml"
        )
        .where("job.orgId = :orgId", { orgId })
        .andWhere(
          new Brackets((subQb) => {
            // Escape SQL wildcard characters to treat them as literal characters
            const escapedSearchStr = searchStr.trim().replace(/[%_]/g, "\\$&");
            subQb
              .where("LOWER(job.title) LIKE LOWER(:searchStr)", {
                searchStr: `%${escapedSearchStr}%`,
              })
              .orWhere("job.jobId LIKE LOWER(:searchStr)", {
                searchStr: `%${escapedSearchStr}%`,
              });
          })
        );

      // ✅ Add active/inactive filter
      if (typeof isActive === "boolean") {
        jobsQuery.andWhere("job.isActive = :isActive", { isActive });
      } else {
        jobsQuery.andWhere("job.isActive = :isActive", { isActive: true });
      }

      const jobs = await jobsQuery.offset(offset).limit(limit).getRawMany();
      console.log("Jobs fetched successfully:", jobs.length);
      return {
        success: true,
        message: "Job metadata fetched successfully",
        data: jobs,
      };
    } catch (error: any) {
      console.log("Error in getAllJobsMeta:", error);

      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed_to_fetch_jobs_meta,
        error: error.message,
      };
    }
  };

  /**
   * Build a comprehensive job description from the request fields
   */
  private static buildJobDescription = (
    request: IJobGenerationRequest
  ): string => `
      Job Title: ${request.title}\n
      Employment Type: ${request.employment_type}\n
      Salary Range: ${request.salary_range}\n
      Salary Cycle: ${request.salary_cycle}\n
      Location: ${request.city}, ${request.state} (${request.location_type})\n
      Overview: ${request.role_overview}\n
      Experience Level: ${request.experience_level}\n
      Responsibilities: ${request.responsibilities}\n
      Education Requirements: ${request.educations_requirement}\n
      Specific Skills/Software Knowledge: ${request.skills_and_software_expertise}\n
      Certifications: ${request.certifications}\n
      Experience Required: ${request.experience_required}\n
      Ideal Candidate Traits: ${request.ideal_candidate_traits}\n
      About the Company: ${request.about_company}\n
      Perks and Benefits: ${request.perks_benefits}\n
      ${request.additional_info ? `Additional Information: ${request.additional_info}\n\n` : ""}

      ${request.show_compliance ? `Compliance Statement: ${request.compliance_statement}\n\n` : ""}

      Tone and Style: ${request.tone_style}
      Hiring Type: ${request.hiring_type}
    `;

  /**
   * Prepare a prompt for GPT to extract relevant skills
   * @param jobDescription The job description text
   * @param skills Static skills list for fallback
   * @param dbSkills Skills data retrieved from database
   */
  private static prepareGptPrompt = (
    jobDescription: string,
    dbSkills = []
  ): string => {
    // Use database skills if available, otherwise use static skills
    const skillsData = dbSkills && dbSkills.length > 0 ? dbSkills : [];

    return `For the following job description:
  ${jobDescription}
      From the list of skills provided below, please identify and categorize as follows:


      1. Top 5 career-based skills come from JD not from given 45 skills


      2. Top 10 role-specific performance-based skills (skills directly related to success in this specific role)


      3. Top 5 culture-specific performance-based skills (skills related to cultural fit and teamwork)


      Skills list:
${JSON.stringify(skillsData, null, 2)}


      IMPORTANT: Your response must be a valid parseable JSON object with the following structure:
      {
        "careerSkills": [
          { "name": "Skill Name", "description": "Skill Description" }
        ],
        "roleSpecificSkills": [
          { "id": "Skill ID from skill list", "name": "Skill Name", "description": "Skill Description" }
        ],
        "cultureSpecificSkills": [
          { "id": "Skill ID from skill list", "name": "Skill Name", "description": "Skill Description" }
        ]
      }

      Do not include any additional text, explanations, or markdown formatting outside of the JSON object.
    `;
  };

  /**
   * Call to GPT API to get the extracted skills
   * @param prompt The prepared prompt with job description and skills
   * @returns An IGptResponse object with categorized skills
   */
  private static callGptApi = async (prompt: string): Promise<IGptResponse> => {
    // Check if client is initialized
    if (!JobServices.openAiClient) {
      Sentry.captureMessage(
        "Attempted to call GPT API without initialized client"
      );
      return JobServices.simulateGptResponse(null);
    }

    try {
      // Call the OpenAI API with proper error handling
      const response = await JobServices.openAiClient.chat.completions.create({
        model: GPT_MODEL,
        messages: [
          {
            role: "system",
            content:
              "You are a helpful assistant that analyzes job descriptions and extracts relevant skills.",
          },
          { role: "user", content: prompt },
        ],
      });
      // Check if response has expected structure
      if (!response?.choices?.[0]?.message?.content) {
        return JobServices.simulateGptResponse(null);
      }

      const { content } = response.choices[0].message;
      let jsonText = content;

      // Try to extract JSON from code blocks if present
      const jsonMatch = content.match(/```(?:json)?\s*({[\s\S]*?})\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        [, jsonText] = jsonMatch;
      } else {
        // If no code blocks, try to find JSON object directly
        const objectMatch = content.match(/({\s*"\w+"\s*:\s*\[)[\s\S]*?}\s*$/);
        if (objectMatch && objectMatch.index !== undefined) {
          jsonText = content.substring(objectMatch.index);
        }
      }

      try {
        // Attempt to parse the extracted JSON
        return JSON.parse(jsonText);
      } catch (jsonError) {
        // Try to clean the text more aggressively
        const firstBrace = jsonText.indexOf("{");
        const lastBrace = jsonText.lastIndexOf("}");

        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
          jsonText = jsonText.substring(firstBrace, lastBrace + 1);

          try {
            return JSON.parse(jsonText);
          } catch (innerError) {
            Sentry.captureException(innerError);
            return JobServices.simulateGptResponse(null);
          }
        }

        // If we still can't parse it, log and use fallback
        Sentry.captureException(jsonError);
        return JobServices.simulateGptResponse(null);
      }
    } catch (error) {
      Sentry.captureException(error);
      return JobServices.simulateGptResponse(null);
    }
  };

  /**
   * Process GPT response or generate a simulated one if needed
   * @param gptResponse The response from GPT or null if failed
   * @returns Properly formatted IExtractedSkills object
   */
  private static simulateGptResponse = (
    gptResponse: IGptResponse | null
  ): IExtractedSkills => {
    // If gptResponse already has the expected structure, use it
    if (
      gptResponse &&
      Array.isArray(gptResponse.careerSkills) &&
      Array.isArray(gptResponse.roleSpecificSkills) &&
      Array.isArray(gptResponse.cultureSpecificSkills)
    ) {
      return {
        careerSkills: gptResponse.careerSkills,
        roleSpecificSkills: gptResponse.roleSpecificSkills,
        cultureSpecificSkills: gptResponse.cultureSpecificSkills,
      };
    }

    // Otherwise, generate using the static Skills array
    return {
      careerSkills: [],
      roleSpecificSkills: [],
      cultureSpecificSkills: [],
    };
  };

  /**
   * Get skill data with caching strategy using Redis
   *
   * @returns Response with skill data
   */
  static getSkillsData = async () => {
    try {
      const cacheKey = SKILLS_CACHE_KEY;
      const cache = new Cache();
      // Check if data exists in Redis cache
      const cachedData = await cache.get(cacheKey);

      if (cachedData) {
        // If cached data exists in Redis, parse and return it
        const parsedData = JSON.parse(cachedData);
        if (parsedData && parsedData.length > 0) {
          return parsedData;
        }
      }

      // If not in cache or cache is empty, get from database

      // Using a more efficient query that groups assessment data by type
      // This uses MySQL's JSON functions to create a grouped structure
      const dataSource = await dbConnection.getS9InnerviewDataSource(); // Get the actual DataSource
      const skillsData = await dataSource.query(`
    SELECT
  type,
  JSON_ARRAYAGG(
    JSON_OBJECT(
      'id', id,
      'title', title,
      'short_description', short_description,
      'type', type
    )
  ) AS items
FROM
  skills
WHERE
  is_core_skill = true
GROUP BY
  type
ORDER BY
  CASE type
    WHEN 'Mentality' THEN 1
    WHEN 'Cognitive Abilities' THEN 2
    WHEN 'Mastery of Emotions' THEN 3
    WHEN 'Social Interaction' THEN 4
    WHEN 'Personal Health' THEN 5
    ELSE 6
  END
      `);
      // If data retrieved from database, cache it in Redis
      if (skillsData && skillsData.length > 0) {
        // Cache the transformed data for better consumption
        await cache.set(
          cacheKey,
          JSON.stringify(skillsData),
          REDIS_EXPIRY.DEFAULT
        );
      }
      return skillsData;
    } catch (error) {
      console.error("Error getting skills data:", error);
      Sentry.captureException(error);
      return [];
    }
  };

  /**
   * Get all skills from the database with caching
   * Retrieves all skills categorized by type from the database with Redis caching
   *
   * @returns {Promise<ResponseObject>} Response object containing skills data or error information
   * @throws Will capture exceptions with Sentry but handle errors gracefully
   *
   * @example
   * // Expected response structure on success
   * {
   *   success: true,
   *   message: 'skills_generated',
   *   data: [
   *     {
   *       type: 'Cognitive Abilities',
   *       items: [
   *         { id: 1, title: 'Strategic Thinking', description: '...', ... },
   *         // other items
   *       ]
   *     },
   *     // other skill types
   *   ]
   * }
   */
  static getSkills = async (): Promise<ResponseObject> => {
    try {
      const skillsData = await JobServices.getSkillsData();

      // Parse the JSON strings in items field into actual JSON objects
      const parsedSkillsData = skillsData.map((category) => {
        try {
          return {
            ...category,
            items: JSON.parse(category.items), // Parse the JSON string into an object
          };
        } catch (parseError) {
          console.error(
            `Error parsing items for category ${category.type}:`,
            parseError
          );
          // Return the original category if parsing fails
          return category;
        }
      });

      return {
        success: true,
        message: API_RESPONSE_MSG.skills_generated,
        data: parsedSkillsData,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
        error: error.message,
      };
    }
  };

  static generateJobRequirement = async (
    requestData: IJobGenerationRequest
  ): Promise<ResponseObject> => {
    try {
      // Initialize OpenAI client if not already initialized
      if (!JobServices.openAiClient) {
        try {
          if (!OPENAI_API_KEY) {
            Sentry.captureMessage(
              "OpenAI API key not configured in environment variables"
            );
            return {
              success: false,
              message: API_RESPONSE_MSG.job_requirement_generation_failed,
              error: API_RESPONSE_MSG.open_ai_key_not_configured,
            };
          }
          JobServices.openAiClient = new OpenAI({ apiKey: OPENAI_API_KEY });
        } catch (error) {
          Sentry.captureException(error);
          return {
            success: false,
            message: API_RESPONSE_MSG.job_requirement_generation_failed,
            error: API_RESPONSE_MSG.failed_to_initialize_openai_client,
          };
        }
      }

      const response = await JobServices.openAiClient.chat.completions.create({
        model: GPT_MODEL,
        messages: [
          {
            role: "system",
            content: JOB_DESCRIPTION_FORMATTER_PROMPT,
          },
          {
            role: "user",
            content: `${JSON.stringify(requestData)}`,
          },
        ],
      });
      const { content } = response.choices[0].message;

      return {
        success: true,
        message: API_RESPONSE_MSG.job_requirement_generated,
        data: content,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.job_requirement_generation_failed,
        error: error.message,
      };
    }
  };

  static saveJobDetails = async (requestData) => {
    try {
      const s9InnerviewDataSource =
        await dbConnection.getS9InnerviewDataSource();
      const jobsRepository = s9InnerviewDataSource.getRepository(JobsModel);
      console.log("requestData", requestData);

      // Check if the organization has available job posting quota
      const benefitCheck = await SubscriptionServices.checkBenefitQuota(
        requestData.org_id,
        BENEFIT_SLUGS.JOB_POSTINGS,
        1
      );

      // If benefit quota not available, return the error
      if (!benefitCheck.success) {
        return {
          success: false,
          message: benefitCheck.message,
        };
      }

      console.log("Job posting quota check passed:", benefitCheck);

      // Create a new job entity
      const newJob = new JobsModel();

      // Map basic fields from request data
      newJob.userId = Number(requestData.user_id);
      newJob.orgId = Number(requestData.org_id);
      newJob.title = requestData.title;
      newJob.employmentType = requestData.employment_type;
      newJob.salaryRange = requestData.salary_range;
      newJob.salaryCycle = requestData.salary_cycle;
      newJob.locationType = requestData.location_type;
      newJob.location = requestData.location_type; // Using location_type as location if not provided
      newJob.state = requestData.state;
      newJob.city = requestData.city;
      newJob.roleOverview = requestData.role_overview;
      newJob.experienceLevel = requestData.experience_level;
      newJob.responsibilities = requestData.responsibilities;
      newJob.educationsRequirement = requestData.educations_requirement;
      newJob.skillsAndSoftwareExpertise =
        requestData.skills_and_software_expertise;
      newJob.certifications = requestData.certifications;
      newJob.experienceRequired = requestData.experience_required;
      newJob.idealCandidateTraits = requestData.ideal_candidate_traits;
      newJob.aboutCompany = requestData.about_company;
      newJob.perksBenefits = requestData.perks_benefits;
      newJob.toneStyle = requestData.tone_style;
      newJob.additionalInfo = requestData.additional_info;
      newJob.departmentId = requestData.department_id;
      newJob.hiringType = requestData.hiring_type;
      // Handle compliance data
      newJob.showCompliance = requestData.show_compliance;
      newJob.complianceStatement = JSON.stringify(
        requestData.compliance_statement
      );

      // Set job description HTML
      newJob.finalJobDescriptionHtml = requestData.final_job_description_html;

      // Set hiring type to EXTERNAL by default if not provided
      newJob.hiringType = requestData.hiring_type;

      newJob.jdLink = requestData.jd_link;

      // Generate job_id in format ORG-JT-XXXX (3 letter org code, 2 letter job title, 4 digit sequence)
      try {
        // Get organization name to extract 3-letter code
        const orgDataSource = await dbConnection.getS9DataSource();
        const orgRepository = orgDataSource.getRepository(OrganizationModel);
        const organization = await orgRepository.findOne({
          where: { id: Number(requestData.org_id) },
        });

        // Extract 3 letters from organization name (default to 'ORG' if not found)
        const orgCode = organization?.name
          ? organization.name
              .replace(/[^A-Za-z]/g, "")
              .substring(0, 3)
              .toUpperCase()
          : "ORG";

        // Extract 2 letters from job title
        const jobTitleCode = requestData.title
          ? requestData.title
              .replace(/[^A-Za-z]/g, "")
              .substring(0, 2)
              .toUpperCase()
          : "JT";

        // Find the highest existing job number with this prefix
        const jobPrefix = `${orgCode}-${jobTitleCode}-`;
        const existingJobs = await jobsRepository.find({
          where: {
            jobId: Like(`${jobPrefix}%`),
          },
          order: {
            jobId: "DESC",
          },
          take: 1,
        });

        let nextNumber = 1; // Default starting number

        if (existingJobs.length > 0) {
          // Extract the numeric part and increment
          const lastJob = existingJobs[0];
          const lastJobNumber = lastJob.jobId.split("-")[2];
          if (lastJobNumber && !Number.isNaN(parseInt(lastJobNumber, 10))) {
            nextNumber = parseInt(lastJobNumber, 10) + 1;
          }
        }

        // Format to 4 digits with leading zeros
        const formattedNumber = nextNumber.toString().padStart(4, "0");

        // Set the job ID
        newJob.jobId = `${jobPrefix}${formattedNumber}`;
      } catch (error) {
        // If anything fails, use a fallback job ID format
        console.error("Error generating job ID:", error);
        const timestamp = Date.now().toString().slice(-8);
        newJob.jobId = `JOB-${timestamp}`;
      }

      // Save the job entity to the database
      const savedJob = await jobsRepository.save(newJob);

      // Import models
      const skillsRepository = s9InnerviewDataSource.getRepository(SkillsModel);
      const jobSkillsRepository =
        s9InnerviewDataSource.getRepository(JobSkillsModel);

      // Helper function to save skill and add to job_skills table
      const saveSkillAndLinkToJob = async (skill, type) => {
        try {
          let skillId;

          // Different logic based on skill type
          if (type === SkillType.CAREER_BASED) {
            // For career-based skills, check if exists in skills table by title
            const existingSkill = await skillsRepository.findOne({
              where: { title: skill.name },
            });

            if (existingSkill) {
              // Use existing skill if found
              skillId = existingSkill.id;
            } else {
              // Create new skill in skills table
              const newSkill = new SkillsModel();
              newSkill.title = skill.name;
              newSkill.shortDescription = skill.description;
              newSkill.isCoreSkill = false;
              newSkill.type = SkillType.CAREER_BASED;

              const savedSkill = await skillsRepository.save(newSkill);
              skillId = savedSkill.id;
            }
          } else {
            // For role-specific and culture-specific skills
            // Use skill.id directly if available
            skillId = skill.id ? Number(skill.id) : null;
            // For these skills, we don't create entries in the skills table
            // We only add them to the job_skills table
          }

          // Add to job_skills table
          const jobSkill = new JobSkillsModel();
          jobSkill.jobId = savedJob.id;
          jobSkill.skillId = skillId;

          console.log("jobSkill----->>>>>", jobSkill);

          // Set type enum
          switch (type) {
            case SkillType.CAREER_BASED:
              jobSkill.type = SkillType.CAREER_BASED;
              break;
            case SkillType.ROLE_SPECIFIC:
              jobSkill.type = SkillType.ROLE_SPECIFIC;
              break;
            case SkillType.CULTURE_SPECIFIC:
              jobSkill.type = SkillType.CULTURE_SPECIFIC;
              break;
            default:
              jobSkill.type = SkillType.CAREER_BASED;
          }

          await jobSkillsRepository.save(jobSkill);

          await helper.addActivityLog({
            orgId: Number(requestData.org_id),
            logType: ACTIVITY_LOG_TYPE.JOB_POSTING,
            userId: Number(requestData.user_id),
            entityId: jobSkill.jobId,
            entityType: ENTITY_TYPE.JOB,
            oldValue: null,
            newValue: null,
            comments: `Posted a new job ${requestData.title}.`,
          });

          return true;
        } catch (error) {
          Sentry.captureException(error);
          console.error(`Error saving ${type} skill:`, error);
          return false;
        }
      };

      // Add career based skills
      if (
        requestData.career_skills &&
        Array.isArray(requestData.career_skills)
      ) {
        await Promise.all(
          requestData.career_skills.map((skill) =>
            saveSkillAndLinkToJob(skill, SkillType.CAREER_BASED)
          )
        );
      }

      // Add role specific skills
      if (
        requestData.role_specific_skills &&
        Array.isArray(requestData.role_specific_skills)
      ) {
        await Promise.all(
          requestData.role_specific_skills.map((skill) =>
            saveSkillAndLinkToJob(skill, SkillType.ROLE_SPECIFIC)
          )
        );
      }

      // Add culture specific skills
      if (
        requestData.culture_specific_skills &&
        Array.isArray(requestData.culture_specific_skills)
      ) {
        await Promise.all(
          requestData.culture_specific_skills.map((skill) =>
            saveSkillAndLinkToJob(skill, SkillType.CULTURE_SPECIFIC)
          )
        );
      }

      // Decrement the job posting quota after successful job creation
      const decrementResult = await SubscriptionServices.decrementBenefitQuota(
        requestData.org_id,
        BENEFIT_SLUGS.JOB_POSTINGS,
        1
      );

      console.log("Job posting quota decremented:", decrementResult);

      return {
        success: true,
        message: API_RESPONSE_MSG.job_details_saved,
        data: savedJob,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.pdf_parsing_failed,
        error: error.message,
      };
    }
  };

  static gettingPreSignedUrlAndData = async (
    filePath: string,
    pdfData: any,
    contentType: string
  ) => {
    try {
      // Generate pre-signed URL
      const uploadUrl = await gettingPreSignedUrl(filePath, contentType);

      // Extract text content from PDF data
      const pdfText = pdfData?.text || "";

      // Initialize OpenAI client if not already initialized
      if (!JobServices.openAiClient && OPENAI_API_KEY) {
        JobServices.openAiClient = new OpenAI({ apiKey: OPENAI_API_KEY });
      }

      // Extract form fields using GPT
      const formFields = await JobServices.extractFormFieldsFromPDF(pdfText);

      // Return URL, link, and extracted form fields
      return { jd_link: uploadUrl, formFields };
    } catch (error) {
      Sentry.captureException(error);
      throw error;
    }
  };

  /**
   * Extract form fields from PDF text using GPT
   * @param pdfText The text content of the PDF
   * @returns Structured job data for form fields
   */
  static extractFormFieldsFromPDF = async (pdfText: string): Promise<any> => {
    try {
      if (!JobServices.openAiClient) {
        throw new Error(API_RESPONSE_MSG.open_ai_client_not_initilezed);
      }

      // Call GPT API
      const gptResponse =
        await JobServices.openAiClient.chat.completions.create({
          model: GPT_MODEL,
          messages: [
            {
              role: "system",
              content: EXTRACT_FORM_FIELDS_FROM_PDF_PROMPT,
            },
            {
              role: "user",
              content: `Extract structured information from the following job description text:${pdfText.substring(0, 8000)}`,
            },
          ],
          temperature: 0.5,
          max_tokens: 2000,
        });

      // Parse the response
      const responseContent = gptResponse.choices[0]?.message?.content || "{}";

      // Try to parse JSON from the response
      try {
        const parsedData = JSON.parse(responseContent);
        return parsedData;
      } catch (parseError) {
        // If we can't parse JSON, return the raw text
        Sentry.captureException(parseError);
        return { raw_response: responseContent };
      }
    } catch (error) {
      Sentry.captureException(error);
      return {
        error: error.message || API_RESPONSE_MSG.failed_to_extract_form_fields,
      };
    }
  };

  /**
   * Get job HTML description by job ID
   * @param jobId The ID of the job to fetch HTML description for
   * @param orgId The organization ID for validation
   * @returns Response object with HTML description or error message
   */
  static getJobHtmlDescription = async (jobId: number, orgId: number) => {
    try {
      // Fetch job from database including HTML description
      const jobSource = await dbConnection.getS9InnerviewDataSource();
      const jobRepo = await jobSource.getRepository(JobsModel);
      const job = await jobRepo.findOne({
        where: {
          id: jobId,
          orgId,
        },
      });

      if (!job) {
        return {
          success: false,
          message: `Job not found`,
        };
      }

      // Return the HTML description
      return {
        success: true,
        message: API_RESPONSE_MSG.job_fetch_success,
        data: {
          id: job.id,
          title: job.title,
          htmlDescription: job.finalJobDescriptionHtml,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message:
          error.message || `Failed to fetch HTML description for job ${jobId}`,
      };
    }
  };

  /**
   * Update job description by job ID
   * Updates the HTML description for a specific job
   *
   * @param {Object} htmlData - The HTML description data to update
   * @param {number} htmlData.jobId - The ID of the job to update
   * @param {string} htmlData.finalJobDescriptionHtml - The HTML description of the job
   * @param {number} orgId - The organization ID for validation
   * @returns {Object} Response object with success status and message
   */
  static updateJobDescription = async (
    htmlData: { jobId: number; finalJobDescriptionHtml: string },
    orgId: number
  ) => {
    try {
      // Fetch job from database including HTML description
      const jobSource = await dbConnection.getS9InnerviewDataSource();
      const jobRepo = await jobSource.getRepository(JobsModel);
      const job = await jobRepo.findOne({
        where: {
          id: htmlData.jobId,
          orgId,
        },
      });

      if (!job) {
        return {
          success: false,
          message: `Job  not found`,
        };
      }

      job.finalJobDescriptionHtml = htmlData.finalJobDescriptionHtml;
      await jobRepo.save(job);
      // Return the HTML description
      return {
        success: true,
        message: API_RESPONSE_MSG.job_update_success,
        data: {
          id: job.id,
          title: job.title,
        },
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message:
          error.message ||
          `Failed to fetch HTML description for job ${htmlData.jobId}`,
      };
    }
  };

  /**
   * Generate PDF from HTML content
   * Generates a PDF from the provided HTML content
   *
   * @param {string} htmlContent - The HTML content to generate PDF from
   * @param {string} jobTitle - The title of the job
   * @returns {Promise<string>} The generated PDF as a base64 string
   */
  static generatePdf = async (htmlContent: string, jobTitle: string) => {
    try {
      const browser = await puppeteer.launch();
      const page = await browser.newPage();
      const html = `
      <html>
        <head>
        <style>
        body {
          font-family: Arial, sans-serif;
          font-size: 12pt;
          line-height: 1.6;
          padding: 0 40px 40px 40px;
        }
        </style>
        </head>
        <body>
          <div>
            ${htmlContent}
          </div>
        </body>
      </html>
    `;
      await page.setContent(html, { waitUntil: "networkidle0" });

      // Import fs and path modules

      const response = await axios.get("https://stratum9.com/logo.png", {
        responseType: "arraybuffer",
      });
      const base64 = Buffer.from(response.data, "binary").toString("base64");
      const pdfBuffer = await page.pdf({
        format: "A4",
        displayHeaderFooter: true,
        headerTemplate: `<style>
    html {
      -webkit-print-color-adjust: exact;
    }
  </style>
  <div style="background:rgb(9, 74, 112); text-align: center; width: 100%; height: 60px; margin-top: -20px"></div>`,
        footerTemplate: `<div style="width: 100%; text-align: center, margin: 0; padding: 0;"><img src="data:image/png;base64,${base64}" alt="Stratum9 Logo" style="display: block; margin: 0 auto; max-height: 40px;"/></div>`,
        margin: {
          top: "60px",
          bottom: "100px",
          left: "0px",
          right: "0px",
        },
      });
      await browser.close();

      // Define output directory as Downloads folder
      const downloadsDir = path.join(os.homedir(), "Downloads");
      const timestamp = new Date()
        .toISOString()
        .replace(/[-:T]/g, "")
        .split(".")[0]; // Example: 20250611_161523
      const pdfPath = path.join(
        downloadsDir,
        `job_requirement_${jobTitle.replace(/ /g, "_")}_${timestamp}.pdf`
      );
      fs.writeFile(pdfPath, pdfBuffer, (err) => {
        if (err) {
          console.error("Error writing PDF:", err);
          return {
            success: false,
            message: API_RESPONSE_MSG.pdf_generation_failed,
            error: err.message,
          };
        }
        return {
          success: true,
          message: API_RESPONSE_MSG.pdf_generated_success,
        };
      });
      return {
        success: true,
        message: API_RESPONSE_MSG.pdf_generated_success,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.pdf_generation_failed,
        error: error.message,
      };
    }
  };
}

export default JobServices;
