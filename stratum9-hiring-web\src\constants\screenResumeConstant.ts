export const GENDER_OPTIONS = [
  { value: "Male", label: "Male" },
  { value: "Female", label: "Female" },
];

export enum InterviewTabType {
  UPCOMING = "UpcomingInterviews",
  PAST = "PastInterviews",
}

export const APPLICATION_UPDATE_STATUS = {
  PROMOTED: "Promoted",
  DEMOTED: "Demoted",
};

export type APPLICATION_UPDATE_STATUS = (typeof APPLICATION_UPDATE_STATUS)[keyof typeof APPLICATION_UPDATE_STATUS];
