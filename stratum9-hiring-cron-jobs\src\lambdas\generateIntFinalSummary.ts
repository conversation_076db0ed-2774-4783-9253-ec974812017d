import dbConnection from "../dbConnection";
import ApplicantAdditionalInfoModel from "../schema/applicant_additional_info";
import FinalAssessmentsModel from "../schema/final_assessments";
import InterviewModel from "../schema/interview";
import InterviewSkillEvaluationModel from "../schema/interview_skill_evaluations";
import JobApplicationsModel from "../schema/job_applications";
import { ACTIVITY_LOG_TYPE, ENTITY_TYPE, SUMMARY_CREATED_COMMENT } from "../utils/constants";
import OpenAiHelper from "../utils/helper";
import { addActivityLog } from "../utils/helper";

export const generateIntFinalSummaryLambda = async (
  event,
  _context,
  _callback
) => {
  console.log("generate interview final summary LambdaFunction event");

  try {
    const jobApplicationId = event.jobApplicationId;

    const interviewRepo = await dbConnection.getRepository(InterviewModel);
    const interviewEvaluationRepo = await dbConnection.getRepository(
      InterviewSkillEvaluationModel
    );
    const finalAssessmentRepo = await dbConnection.getRepository(
      FinalAssessmentsModel
    );
    const applicantAdditionalInfoRepo = await dbConnection.getRepository(
      ApplicantAdditionalInfoModel
    );
    const jobApplicationRepo = await dbConnection.getRepository(
      JobApplicationsModel
    );

    const additionalInfo = await applicantAdditionalInfoRepo.findOne({
      where: {
        jobApplicationId,
      },
      select: ["description"],
    });

    const finalAssessment = await finalAssessmentRepo.findOne({
      where: { jobApplicationId },
      select: ["overallSuccessProbability"],
    });

    console.log("finalAssessment====>>>>", finalAssessment);

    const interviewSummaries = await interviewRepo.find({
      where: { jobApplicationId },
      select: ["interviewSummary", "hardSkillMarks"],
    });

    const evaluations = await interviewEvaluationRepo.find({
      where: { jobApplicationId },
      select: ["jobSkillId", "skillMarks", "potentialsGaps", "strengths"],
    });

    console.log("evaluations====>>", evaluations);

    console.log("Interview summaries to process:", interviewSummaries);

    const result = await OpenAiHelper.generateInterviewFinalSummary(
      interviewSummaries,
      evaluations,
      additionalInfo,
      finalAssessment
    );

    const parsedData = JSON.parse(result);
    console.log("Parsed data:", parsedData);

    const jobApplication = await jobApplicationRepo.findOne({
      where: { id:jobApplicationId },
      relations: ["candidate"],
    });

    console.log("Job application details:", jobApplication);

    if (!finalAssessment) {
      const newFinalAssessment = new FinalAssessmentsModel();
      newFinalAssessment.jobId = jobApplication.jobId;
      newFinalAssessment.jobApplicationId = jobApplicationId;
      newFinalAssessment.overallSuccessProbability = parsedData.overallGoodFit;
      newFinalAssessment.skillSummary = {
        finalSummary: parsedData.finalSummary,
      };
      newFinalAssessment.developmentRecommendations = {
        recommendations: parsedData.improvementAreas,
      };
      newFinalAssessment.behaviouralScores = {
        behaviouralScores: parsedData.behaviouralScores,
      };

      await finalAssessmentRepo.save(newFinalAssessment);

      console.log("New final assessment created:", newFinalAssessment);

      return {
        success: true,
        message: "Final summary generated successfully",
      };
    }

    const updateRes = await finalAssessmentRepo.update(
      { jobApplicationId },
      {
        overallSuccessProbability: parsedData.overallGoodFit,
        skillSummary: { finalSummary: parsedData.finalSummary },
        developmentRecommendations: {
          recommendations: parsedData.improvementAreas,
        },
        behaviouralScores: { behaviouralScores: parsedData.behaviouralScores },
      }
    );

    console.log("=====updateRes", updateRes);

    // Add activity log after updating the final assessment
    // Fetch candidate name for logging

    const activityLogResult = await addActivityLog({
      orgId: event.orgId,
      logType: ACTIVITY_LOG_TYPE.CANDIDATE_FINAL_SUMMARY,
      userId: event.userId || null,
      entityId: jobApplicationId,
      entityType: ENTITY_TYPE.FINAL_ASSESSMENTS_MODEL,
      oldValue: null,
      newValue: SUMMARY_CREATED_COMMENT,
      comments: `Final summary generated for ${jobApplication?.candidate?.name}.`
    });
    console.log("Activity log result:", activityLogResult);

    return {
      success: true,
      message: "Final summary generated successfully",
    };
  } catch (error) {
    console.error("Error generating interview final summary:", error);
    return {
      success: false,
    };
  }
};
