import express from "express";
import {
  addManualFinalAssessmentQuestion,
  createFinalAssessment,
  getFinalAssessmentByCandidate,
  getFinalAssessmentQuestion,
  getAssessmentStatus,
  shareAssessment,
  submitAssessment,
  verifyCandidateEmail,
  generateAssessmentToken,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import {
  // paramsValidation,
  queryValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import {
  addManualQuestionValidation,
  candidateAssessmentIdValidation,
  createFinalAssessmentByAiValidation,
  getAssessmentStatusValidation,
  getQuestionQueryValidation,
  shareAssessmentValidation,
  verifyCandidateEmailValidation,
  generateAssessmentTokenValidation,
} from "./validations";
import HandleErrors from "../../middleware/handleError";
import { authorizedForManageCandidateProfileAccess } from "../../middleware/isAuthorized";

const router = express.Router();

/**
 * @swagger
 * /api/v1/final-assessment:
 *   post:
 *     summary: Create a new final assessment
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobId
 *               - jobApplicationId
 *               - overallSuccessProbability
 *               - skillSummary
 *               - developmentRecommendations
 *             properties:
 *               jobId:
 *                 type: number
 *               jobApplicationId:
 *                 type: number
 *               overallSuccessProbability:
 *                 type: number
 *               skillSummary:
 *                 properties:
 *                   skills:
 *                     type: array
 *                     items:
 *                       properties:
 *                         name:
 *                           type: string
 *                         score:
 *                           type: number
 *                       type: object
 *                 type: object
 *               developmentRecommendations:
 *                 properties:
 *                   recommendations:
 *                     type: array
 *                     items:
 *                       properties:
 *                         area:
 *                           type: string
 *                         suggestion:
 *                           type: string
 *                       type: object
 *                 type: object
 *     responses:
 *       201:
 *         description: Final assessment created successfully
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.CREATE_FINAL_ASSESSMENT,
  auth,
  authorizedForManageCandidateProfileAccess,
  schemaValidation(createFinalAssessmentByAiValidation),
  HandleErrors(createFinalAssessment)
);

/**
 * @swagger
 * /api/v1/final-assessment/{finalAssessmentId}/questions:
 *   get:
 *     summary: Get final assessment questions grouped by skill type
 *     tags: [Final Assessment]
 *     parameters:
 *       - in: path
 *         name: finalAssessmentId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the final assessment
 *     responses:
 *       200:
 *         description: Questions fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     assessmentId:
 *                       type: integer
 *                     isAssessmentSubmitted:
 *                       type: boolean
 *                     isAssessmentShared:
 *                       type: boolean
 *                     questionGroups:
 *                       type: array
 *                       items:
 *                         properties:
 *                           type:
 *                             type: string
 *                           questions:
 *                             type: array
 *                             items:
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                 question:
 *                                   type: string
 *                                 type:
 *                                   type: string
 *                               type: object
 *                         type: object
 */
router.get(
  ROUTES.FINAL_ASSESSMENT.GET_FINAL_ASSESSMENT_QUESTION,
  auth,
  queryValidation(getQuestionQueryValidation),
  HandleErrors(getFinalAssessmentQuestion)
);

/**
 * @swagger
 * /api/v1/final-assessment/assessment/{finalAssessmentId}/questions:
 *   post:
 *     summary: Add a manual question to a final assessment
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - question
 *               - questionType
 *               - skillId
 *               - options
 *               - correctAnswer
 *             properties:
 *               question:
 *                 type: string
 *               questionType:
 *                 type: string
 *                 enum: [mcq, true_false]
 *               skillId:
 *                 type: number
 *               options:
 *                 type: object
 *                 properties:
 *                   options:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         text:
 *                           type: string
 *               correctAnswer:
 *                 type: string
 *     responses:
 *       201:
 *         description: Question added successfully
 *       400:
 *         description: Bad request
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.ADD_MANUAL_QUESTION,
  auth,
  schemaValidation(addManualQuestionValidation),
  HandleErrors(addManualFinalAssessmentQuestion)
);

/**
 * @swagger
 * /api/v1/final-assessment/assessment/share:
 *   post:
 *     summary: Share assessment with candidate via email
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - finalAssessmentId
 *               - assessmentLink
 *               - jobApplicationId
 *             properties:
 *               finalAssessmentId:
 *                 type: number
 *                 description: ID of the final assessment
 *               assessmentLink:
 *                 type: string
 *                 description: Link to the assessment that will be sent to the candidate
 *               jobApplicationId:
 *                 type: number
 *                 description: ID of the job application
 *     responses:
 *       200:
 *         description: Assessment shared successfully
 *       400:
 *         description: Bad request
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.SHARE_ASSESSMENT,
  auth,
  schemaValidation(shareAssessmentValidation),
  HandleErrors(shareAssessment)
);

/**
 * @swagger
 * /api/v1/final-assessment/candidate/assessment:
 *   get:
 *     summary: Get final assessment by finalAssessmentId for candidate view
 *     tags: [Final Assessment]
 *     parameters:
 *       - in: query
 *         name: finalAssessmentId
 *         required: true
 *         schema:
 *           type: number
 *         description: ID of the final assessment
 *     responses:
 *       200:
 *         description: Assessment fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                     questions:
 *                       type: array
 *                       items:
 *                         properties:
 *                           id:
 *                             type: integer
 *                           question:
 *                             type: string
 *                           type:
 *                             type: string
 *                         type: object
 *       400:
 *         description: Bad request
 */
router.get(
  ROUTES.FINAL_ASSESSMENT.GET_FINAL_ASSESSMENT_BY_CANDIDATE,
  authorizedForManageCandidateProfileAccess,
  queryValidation(candidateAssessmentIdValidation),
  HandleErrors(getFinalAssessmentByCandidate)
);

/**
 * @swagger
 * /api/v1/final-assessment/candidate/assessment/submit:
 *   post:
 *     summary: Submit candidate answers for an assessment
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - finalAssessmentId
 *               - candidateEmail
 *               - answers
 *             properties:
 *               finalAssessmentId:
 *                 type: string
 *                 description: Encrypted ID of the assessment
 *               candidateEmail:
 *                 type: string
 *                 description: Email of the candidate
 *               candidateName:
 *                 type: string
 *                 description: Name of the candidate (optional)
 *               answers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - questionId
 *                     - answer
 *                   properties:
 *                     questionId:
 *                       type: number
 *                       description: ID of the question
 *                     answer:
 *                       type: string
 *                       description: Candidate's answer to the question
 *     responses:
 *       200:
 *         description: Assessment submitted successfully
 *       400:
 *         description: Bad request
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.SUBMIT_ASSESSMENT,
  HandleErrors(submitAssessment)
);

/**
 * @swagger
 * /api/v1/final-assessment/assessment/status:
 *   post:
 *     summary: Get assessment status based on isShare and isSubmit flags
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobId
 *               - jobApplicationId
 *             properties:
 *               jobId:
 *                 type: number
 *                 description: ID of the job
 *               jobApplicationId:
 *                 type: number
 *                 description: ID of the job application
 *     responses:
 *       200:
 *         description: Assessment status fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     jobId:
 *                       type: integer
 *                     jobApplicationId:
 *                       type: integer
 *                     isAssessmentSubmitted:
 *                       type: boolean
 *                     isAssessmentShared:
 *                       type: boolean
 *       400:
 *         description: Bad request
 */

// this api is no longer in use
router.post(
  ROUTES.FINAL_ASSESSMENT.GET_ASSESSMENT_STATUS,
  auth,
  schemaValidation(getAssessmentStatusValidation),
  HandleErrors(getAssessmentStatus)
);

/**
 * @swagger
 * /api/v1/final-assessment/candidate/verify-email:
 *   post:
 *     summary: Verify if candidate email exists in the system
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - finalAssessmentId
 *             properties:
 *               email:
 *                 type: string
 *                 description: Email of the candidate
 *               finalAssessmentId:
 *                 type: string
 *                 description: ID of the final assessment
 *     responses:
 *       200:
 *         description: Email verification successful
 *       400:
 *         description: Bad request
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.VERIFY_CANDIDATE_EMAIL,
  schemaValidation(verifyCandidateEmailValidation),
  HandleErrors(verifyCandidateEmail)
);

/**
 * @swagger
 * /api/v1/final-assessment/assessment/generate-token:
 *   post:
 *     summary: Generate a JWT token for a final assessment
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - finalAssessmentId
 *             properties:
 *               finalAssessmentId:
 *                 type: number
 *                 description: ID of the final assessment
 *     responses:
 *       200:
 *         description: Token generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     token:
 *                       type: string
 *                     finalAssessmentId:
 *                       type: number
 *                     expiresIn:
 *                       type: string
 *       400:
 *         description: Bad request
 *       404:
 *         description: Assessment not found
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.GENERATE_ASSESSMENT_TOKEN,
  auth,
  schemaValidation(generateAssessmentTokenValidation),
  HandleErrors(generateAssessmentToken)
);

export default router;
