"use client";
import React, { FC } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Loader from "../loader/Loader";

interface IProps {
  employeeName: string;
  currentRole: string;
  newRole: string;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ChangeRoleModal: FC<IProps> = ({ employeeName, currentRole, newRole, onConfirm, onCancel, isLoading = false }) => {
  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>Change Role?</h2>

            <p className="text-center font16">
              You're changing <strong>"{employeeName}"</strong> role from <strong>{currentRole}</strong> to <strong>{newRole}</strong>.
            </p>
            <Button className="modal-close-btn" onClick={onCancel} disabled={isLoading}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body">
            <div className="button-align">
              <Button className="primary-btn rounded-md w-100" onClick={onConfirm} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader />
                    <span className="ms-2">Changing Role...</span>
                  </>
                ) : (
                  "Change Role"
                )}
              </Button>
              <Button className="dark-outline-btn rounded-md w-100" onClick={onCancel} disabled={isLoading}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangeRoleModal;
