import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Employee } from "./employees";
// import OrganizationModel from "./organization";

@Entity("departments")
export class DepartmentModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  name: string;

  @Column({ name: "organization_id", nullable: false })
  organizationId: number;

  // @ManyToOne(() => OrganizationModel, (organization) => organization.departments)
  // @JoinColumn({ name: "organization_id" })
  // organization: OrganizationModel;

  @Column({ name: "is_active", default: true })
  isActive: boolean;

  @Column({ name: "is_default_department", default: false })
  isDefaultDepartment: boolean;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;

  // Relationships
  @OneToMany(() => Employee, (employee) => employee.department)
  employees: Employee[];
}

export default DepartmentModel;
