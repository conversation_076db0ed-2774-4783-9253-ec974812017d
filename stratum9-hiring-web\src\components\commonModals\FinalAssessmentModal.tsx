"use client";
import React, { FC, useState, useEffect } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import CopyLinkIcon from "../svgComponents/CopyLinkIcon";
import { shareAssessmentToCandidate, getAssessmentToken } from "@/services/assessmentService";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { IFinalAssessmentModalProps } from "@/interfaces/finalAssessment";
import { useTranslations } from "next-intl";
import router from "next/router";
import ROUTES from "@/constants/routes";

const FinalAssessmentModal: FC<IFinalAssessmentModalProps> = ({
  onClickCancel,
  onSubmitSuccess,
  finalAssessmentId,
  jobApplicationId,
  isFromCompleteButton = false,
}) => {
  const t = useTranslations();
  const [isSharing, setIsSharing] = useState(false);
  const [isGeneratingUrl, setIsGeneratingUrl] = useState(true);
  const [assessmentLink, setAssessmentLink] = useState<string>("");

  // Generate assessment link using token from API
  useEffect(() => {
    const fetchAssessmentToken = async () => {
      try {
        setIsGeneratingUrl(true);
        const response = await getAssessmentToken({ finalAssessmentId });

        if (response.data && response.data.success && response.data.data) {
          const { token } = response.data.data;
          const generatedLink = `${window.location.origin}${ROUTES.CANDIDATE_ASSESSMENT}?token=${token}`;
          setAssessmentLink(generatedLink);
        } else {
          toastMessageError(t(response.data?.message || "failed_to_generate_assessment_link"));
        }
      } catch (error) {
        toastMessageError(t((error as string) || "an_error_occurred_while_generating_assessment_link"));
      } finally {
        setIsGeneratingUrl(false);
      }
    };

    fetchAssessmentToken();
  }, [finalAssessmentId, t]);

  // Copy link to clipboard
  const handleCopyLink = () => {
    if (!assessmentLink) {
      toastMessageError(t("token_req_msg"));
      return;
    }
    navigator.clipboard
      .writeText(assessmentLink)
      .then(() => {
        toastMessageSuccess(t("assessment_link_copied_to_clipboard"));
      })
      .catch(() => {
        toastMessageError(t("failed_to_copy_link"));
      });
  };

  // Share assessment via API
  const handleShareAssessment = async () => {
    try {
      if (!assessmentLink) {
        toastMessageError(t("token_req_msg"));
        return;
      }
      setIsSharing(true);
      const response = await shareAssessmentToCandidate({
        finalAssessmentId,
        assessmentLink,
        jobApplicationId,
      });

      if (response.data && response.data.success) {
        toastMessageSuccess(t("assessment_shared_successfully"));
        if (isFromCompleteButton) {
          // Use the router to navigate to the interview summary page
          router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${jobApplicationId}`); // Navigate to candidate profile
          // Ensure the navigation completes before continuing
          // setTimeout is used as a workaround to ensure navigation has time to happen
          setTimeout(() => {
            onClickCancel(); // Close the modal after navigation starts
          }, 100);
        } else {
          // Call onSubmitSuccess if provided, otherwise just close the modal
          if (onSubmitSuccess) {
            onSubmitSuccess();
          } else {
            onClickCancel();
          }
        }
      } else {
        toastMessageError(response.data?.message || t("failed_to_share_assessment"));
      }
    } catch (error) {
      toastMessageError(error ? (error as string) : t("an_error_occurred_while_sharing_assessment"));
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered" style={{ maxWidth: "700px", width: "90%" }}>
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>{t("share_final_assessment")}</h2>
            <p className="textMd w100">
              {t(
                "you_can_share_the_final_assessment_link_to_the_candidate_for_them_to_take_the_test_remotely_they_won_t_be_able_to_see_the_correct_answers"
              )}{" "}
            </p>
            <Button
              className="modal-close-btn"
              onClick={() => {
                if (isFromCompleteButton) {
                  onClickCancel();
                } else {
                  onClickCancel();
                }
              }}
              disabled={isSharing || isGeneratingUrl}
            >
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body pt-0">
            <div className="mb-4">
              <label className="form-label fw-bold mb-2">{t("assessment_link")}</label>
              <div className="input-group">
                {isGeneratingUrl ? (
                  <div
                    className="form-control url-field position-relative p-3"
                    style={{ backgroundColor: "#f8f9fa", border: "1px solid #dee2e6", borderRadius: "8px", minHeight: "50px" }}
                  >
                    <div className="d-flex align-items-center">
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      <span>{t("generating_assessment_url")}</span>
                    </div>
                  </div>
                ) : (
                  <div
                    className="form-control url-field position-relative p-3"
                    style={{
                      backgroundColor: "#f8f9fa",
                      border: "1px solid #dee2e6",
                      borderRadius: "8px",
                      minHeight: "50px",
                      wordBreak: "break-all",
                      color: "#2c7be5",
                    }}
                  >
                    {assessmentLink}
                  </div>
                )}
              </div>
            </div>

            <div className="action-btn">
              <Button className="primary-btn rounded-md w-100" onClick={handleShareAssessment} disabled={isSharing || isGeneratingUrl}>
                {isSharing ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    {t("sharing")}
                  </>
                ) : (
                  t("share_assessment")
                )}
              </Button>
              <Button className="dark-outline-btn rounded-md w-100" onClick={handleCopyLink} disabled={isGeneratingUrl}>
                <>
                  <CopyLinkIcon className="copy-link-icon" /> {t("copy_link")}
                </>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default FinalAssessmentModal;
