import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  <PERSON>inColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { DepartmentModel } from "./departments";
import { Role } from "./roles";

@Entity("employees")
export class Employee {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "user_id" })
  userId: number;

  @Column({ name: "organization_id" })
  organizationId: number;

  @Column({ name: "department_id" })
  departmentId: number;

  @Column({ name: "role_id" })
  roleId: number;

  // user_id of employee creator from user table
  @Column({ name: "assigned_by" })
  assignedBy: number;

  @Column({ name: "is_active", default: true })
  isActive: boolean;

  @Column({ name: "is_admin", default: false })
  isAdmin: boolean;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;

  @Column({ name: "interview_order", nullable: true })
  interviewOrder: number;

  // Relationships
  @ManyToOne(() => DepartmentModel, (department) => department.employees)
  @JoinColumn({ name: "department_id" })
  @Index()
  department: DepartmentModel;

  @ManyToOne(() => Role, (role) => role.employees)
  @JoinColumn({ name: "role_id" })
  @Index()
  role: Role;
}

export default Employee;
