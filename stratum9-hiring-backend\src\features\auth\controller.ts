import { Request, Response } from "express";
import AuthService from "./services";

/**
 * Login.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const login = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };
    const data = await AuthService.login(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Signup.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const resendOtp = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await AuthService.resendOtp(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
/**
 * Verify OTP.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const verifyOtp = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await AuthService.verifyOtp(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Forgot password.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };
    const data = await AuthService.forgotPassword(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Reset password.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const resetPassword = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await AuthService.resetPassword(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 *
 * @param req remove the session
 * @param res
 */

export const deleteSession = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization?.split(" ")[1];
    const { userId } = req.params;
    const data = await AuthService.deleteSession(token, +userId);

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const updateTimeZone = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
      userId: req.userId,
    };
    const data = await AuthService.updateTimeZone(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
