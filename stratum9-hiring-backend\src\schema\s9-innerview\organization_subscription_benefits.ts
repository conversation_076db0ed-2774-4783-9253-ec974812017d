import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

/**
 * Schema for organization subscription benefits.
 * Tracks usage limits and consumption for each organization's subscription features.
 * Numeric values are decremented as features are used. Unlimited features are represented by -1.
 */
@Entity("organization_subscription_benefits")
class OrganizationSubscriptionBenefitModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "organization_id", nullable: false })
  organizationId: number;

  // Job posting limit - decremented with each job posting
  @Column({ name: "job_postings", type: "int", nullable: true })
  jobPostings: number;

  // Resume screening limit - decremented with each resume screened
  @Column({ name: "resume_screening", type: "int", nullable: true })
  resumeScreening: number;

  // Manual resume upload limit - decremented with each manual resume upload
  @Column({ name: "manual_resume_upload", type: "int", nullable: true })
  manualResumeUpload: number;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}

export default OrganizationSubscriptionBenefitModel;
