import { configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from "redux-persist";
import storage from "redux-persist/lib/storage"; // defaults to localStorage for web

// Import the reducers directly to avoid circular dependency
import jobSkillsReducer from "./slices/jobSkillsSlice";
import jobDetailsReducer from "./slices/jobDetailsSlice";
import allSkillsReducer from "./slices/allSkillsSlice";
import authReducer from "./slices/authSlice";
import jobRequirementReducer from "./slices/jobRequirementSlice";
import interviewReducer from "./slices/interviewSlice";
import notificationReducer from "./slices/notificationSlice";

// Configure persist options for job skills slice
const jobSkillsPersistConfig = {
  key: "jobSkills",
  storage,
};

// Configure persist options for job details slice
const jobDetailsPersistConfig = {
  key: "jobDetails",
  storage,
};

// Configure persist options for all skills slice
const allSkillsPersistConfig = {
  key: "allSkills",
  storage,
  blacklist: ["loading", "error"], // Don't persist loading and error states
};

// Configure persist options for auth slice
const authPersistConfig = {
  key: "auth",
  storage,
};
// Configure persist options for job requirement slice
const jobRequirementPersistConfig = {
  key: "jobRequirement",
  storage,
};

// Configure persist options for interview questions slice
const interviewPersistConfig = {
  key: "interview",
  storage,
};

// Configure persist options for notification slice
const notificationPersistConfig = {
  key: "notification",
  storage,
};

// Create persisted reducers
const persistedJobSkillsReducer = persistReducer(jobSkillsPersistConfig, jobSkillsReducer);
const persistedJobDetailsReducer = persistReducer(jobDetailsPersistConfig, jobDetailsReducer);
const persistedAllSkillsReducer = persistReducer(allSkillsPersistConfig, allSkillsReducer);
const persistedAuthReducer = persistReducer(authPersistConfig, authReducer);
const persistedJobRequirementReducer = persistReducer(jobRequirementPersistConfig, jobRequirementReducer);
const persistedInterviewReducer = persistReducer(interviewPersistConfig, interviewReducer);
const persistedNotificationReducer = persistReducer(notificationPersistConfig, notificationReducer);

// Create store with the persisted reducers
export const store = configureStore({
  reducer: {
    jobSkills: persistedJobSkillsReducer,
    jobDetails: persistedJobDetailsReducer,
    allSkills: persistedAllSkillsReducer,
    auth: persistedAuthReducer,
    jobRequirement: persistedJobRequirementReducer,
    interview: persistedInterviewReducer,
    notification: persistedNotificationReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

// Create persistor
export const persistor = persistStore(store);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
