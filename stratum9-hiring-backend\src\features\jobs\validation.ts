import <PERSON><PERSON> from "joi";

import { IJobGenerationRequest } from "./interface";
import {
  EmploymentType,
  LocationType,
  SalaryCycle,
  ToneStyle,
} from "../../schema/s9-innerview/jobs";

/**
 * Validation schema for job entity
 * Validates all required fields with appropriate constraints
 */
const generateJobSkillsSchema = Joi.object<IJobGenerationRequest>({
  department_id: Joi.number().required().description("Department Id "),
  jd_link: Joi.string().optional().allow(""),
  title: Joi.string()
    .trim()
    .required()
    .description("Title of the job position"),

  employment_type: Joi.string()
    .valid(...Object.values(EmploymentType))
    .required()
    .description("Type of employment (full_time, contract)"),

  salary_range: Joi.string()
    .required()
    .description("Salary range for the position"),

  salary_cycle: Joi.string()
    .valid(...Object.values(SalaryCycle))
    .required()
    .description("Salary cycle (per hour, per month, per annum)"),

  location_type: Joi.string()
    .valid(...Object.values(LocationType))
    .required()
    .description("Job location type (in-office, remote)"),

  state: Joi.string()
    .trim()
    .required()
    .description("State where the job is located"),

  city: Joi.string()
    .trim()
    .required()
    .description("City where the job is located"),

  role_overview: Joi.string()
    .trim()
    .required()
    .description("Overview of the job role"),

  experience_level: Joi.string()
    .required()
    .description("Experience level for the position"),

  responsibilities: Joi.string()
    .trim()
    .required()
    .description("Job responsibilities"),

  educations_requirement: Joi.string()
    .trim()
    .required()
    .description("Education requirements for the position"),

  skills_and_software_expertise: Joi.string()
    .trim()
    .required()
    .required()
    .description("Specific skills or software expertise required"),

  certifications: Joi.string()
    .trim()
    .allow("")
    .description("Certifications optional"),

  experience_required: Joi.string()
    .trim()
    .required()
    .required()
    .description("Experience required for the position"),

  ideal_candidate_traits: Joi.string()
    .trim()
    .required()
    .description("Ideal traits for candidates"),

  about_company: Joi.string()
    .trim()
    .required()
    .description("Information about the company"),

  perks_benefits: Joi.string()
    .trim()
    .optional()
    .allow("")
    .description("Job perks and benefits"),

  tone_style: Joi.string()
    .valid(...Object.values(ToneStyle))
    .required()
    .description("Tone and style for job description"),

  additional_info: Joi.string()
    .optional()
    .allow("")
    .description("Additional information"),

  show_compliance: Joi.boolean()
    .required()
    .description("Whether to show compliance information"),

  compliance_statement: Joi.array()
    .items(Joi.string())
    .min(1)
    .required()
    .description("Compliance statement for the job posting"),
  hiring_type: Joi.string().valid("internal", "external").required(),
});

export default generateJobSkillsSchema;
